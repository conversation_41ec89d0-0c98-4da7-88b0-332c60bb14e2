{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_vendor/android/app/.cxx/RelWithDebInfo/1p6wh6p4/x86_64", "source": "/Users/<USER>/.shorebird/bin/cache/flutter/3f9cefb45389b72ff073ddf305fe0939f822143b/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}