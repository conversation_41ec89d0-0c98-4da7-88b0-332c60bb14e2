{"nm": "Comp 3", "ddd": 0, "h": 400, "w": 400, "meta": {"g": "@lottiefiles/toolkit-js 0.33.2"}, "layers": [{"ty": 4, "nm": "Shape Layer 3", "sr": 1, "st": 0, "op": 2092, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.58, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 100], "t": 0}, {"s": [199, 199, 100], "t": 21}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [200, 200, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.58, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [-70], "t": 0}, {"s": [0], "t": 21}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Overshoot", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Bounce", "ix": 2, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Friction", "ix": 3, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Overshoot", "ix": 4, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - <PERSON><PERSON><PERSON>", "ix": 5, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Friction", "ix": 6, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12.89, 0], [0, 0], [0, 12.891], [0, 0], [-12.891, 0], [0, 0], [0, -12.892]], "o": [[0, 12.891], [0, 0], [-12.891, 0], [0, 0], [0, -12.892], [0, 0], [12.89, 0], [0, 0]], "v": [[68.291, 44.951], [44.952, 68.292], [-44.951, 68.292], [-68.291, 44.951], [-68.291, -44.951], [-44.951, -68.292], [44.952, -68.292], [68.291, -44.951]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.2235, 0.3255, 0.5961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 1}, {"ty": 4, "nm": "Shape Layer 2", "sr": 1, "st": 0, "op": 2092, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-0.313, 58.688, 0], "ix": 1}, "s": {"a": 0, "k": [100.003, 100.003, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.46, "y": 0}, "i": {"x": 0.54, "y": 1}, "s": [18.026, 81.664, 0], "t": 15, "ti": [0.2236475944519, 1.97459995746613, 0], "to": [-0.2236475944519, -1.97459995746613, 0]}, {"s": [16.685, 69.816, 0], "t": 28}], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.46, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [-123.998], "t": 9}, {"s": [0.002], "t": 28}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Overshoot", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - <PERSON><PERSON><PERSON>", "ix": 2, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Friction", "ix": 3, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-8.486, 0], [0, 0], [0, 0], [7.476, 0], [0, -17.489], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -5.915], [0, 0], [0, 0], [-1.873, -0.249], [-15.601, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.928, 71.744], [8.901, 5.042], [26.503, 5.042], [29.138, -15.395], [8.901, -15.395], [8.901, -28.445], [19.028, -38.392], [29.85, -38.397], [29.85, -56.675], [14.079, -57.48], [-12.204, -30.466], [-12.204, -15.395], [-29.85, -15.395], [-29.85, 5.042], [-12.204, 5.042], [-12.222, 71.744], [8.928, 71.729]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 1, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 2, "parent": 3}, {"ty": 4, "nm": "Shape Layer 1", "sr": 1, "st": 0, "op": 2092, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.58, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 100], "t": 0}, {"s": [199, 199, 100], "t": 21}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [200, 200, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.58, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [-70], "t": 0}, {"s": [0], "t": 21}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Overshoot", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Bounce", "ix": 2, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Scale - Friction", "ix": 3, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Overshoot", "ix": 4, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - <PERSON><PERSON><PERSON>", "ix": 5, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "mn": "ADBE Slider Control", "nm": "Rotation - Friction", "ix": 6, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12.89, 0], [0, 0], [0, 12.891], [0, 0], [-12.891, 0], [0, 0], [0, -12.892]], "o": [[0, 12.891], [0, 0], [-12.891, 0], [0, 0], [0, -12.892], [0, 0], [12.89, 0], [0, 0]], "v": [[68.291, 44.951], [44.952, 68.292], [-44.951, 68.292], [-68.291, 44.951], [-68.291, -44.951], [-44.951, -68.292], [44.952, -68.292], [68.291, -44.951]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.2235, 0.3255, 0.5961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 3}], "v": "5.1.20", "fr": 30, "op": 120, "ip": 0, "assets": []}