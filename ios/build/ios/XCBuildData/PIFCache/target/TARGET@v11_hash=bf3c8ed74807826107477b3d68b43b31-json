{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98849feb0db5d9d314a29d5d973197e06f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989bd97a893356e58c402984381cf8fb8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820b33a4831bd0084ac591b0da082c7ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c474aabe6ca00ce3fbe43049a9e1c9b3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820b33a4831bd0084ac591b0da082c7ad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af65007bbc638b3fe069f8b1b2a52836", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf18c62fab8827d9d7eae29e64125ef1", "guid": "bfdfe7dc352907fc980b868725387e98b69e43c6aab51c9fc0ea5e8b7a970165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34ec4fa574b00067d44cceadfcd047f", "guid": "bfdfe7dc352907fc980b868725387e9810ba7cd8fa7423449da74f3e05f12341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98391acdfd6a6dcbaedb8c43e9ba5e29da", "guid": "bfdfe7dc352907fc980b868725387e98473aaf3980ecd5786aedec253ebe32f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aebf49532ea0eafacf715ad17465094", "guid": "bfdfe7dc352907fc980b868725387e986f3829470f7ca760fefad3cce97298ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826547b15a89c3041cb49f334c663f246", "guid": "bfdfe7dc352907fc980b868725387e98ae116eea8213c011d5020c5cc7e0927e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5334b3531fe84a5d8c80ca7c9bb1eb2", "guid": "bfdfe7dc352907fc980b868725387e981aa01c886b4a2e4e21468f84e54a7591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb51e0e786334aab7d5ecb583643df5", "guid": "bfdfe7dc352907fc980b868725387e98d0b450dd4702d323a6f3d240f91b7e4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a10cdd8366812f5e36ed7f5bb83904", "guid": "bfdfe7dc352907fc980b868725387e980500e25211f3fdc7ee3b8e778c1cec68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989897eba46b8ef01d4646a6eaaf4501c2", "guid": "bfdfe7dc352907fc980b868725387e984cac8c1b9f674a9aa69bea13ce82b918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b47ca4e41d68085f45104c4dff489d", "guid": "bfdfe7dc352907fc980b868725387e985fa3cd35d57b956b7d34c01c77acdb4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802c6a0b9edf606fca9a7cdf65558782", "guid": "bfdfe7dc352907fc980b868725387e981512a8114cf40d569a4a173266fe15b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa044ed9066e9428212bca8c02484aef", "guid": "bfdfe7dc352907fc980b868725387e98c9f4c8bf7e18376318f8fd2187c27ddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc5b050f48771e25e1543fda661c472", "guid": "bfdfe7dc352907fc980b868725387e985e803912d51ac915c70da36a6a59e393", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d947619ea230bb8bbe64b668b8da9b", "guid": "bfdfe7dc352907fc980b868725387e98f456e141e1c27edffde7a0442568b46e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984629883a00ad00a0ca597f73dfc2e1a1", "guid": "bfdfe7dc352907fc980b868725387e98072c507cb766b07d7bf0e7c08b73294a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98655dbb0fc36015a4584a53085ccece74", "guid": "bfdfe7dc352907fc980b868725387e98928bc1ef91c6d312edbd46bc26a5ae41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b9ba5d9c39f3789ba2db08137a6a6a", "guid": "bfdfe7dc352907fc980b868725387e9843ea31e4924fb9f143b5880da04228da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a75e99077c71378ddd98d5221c97fa", "guid": "bfdfe7dc352907fc980b868725387e9863656324c90f540a0d4d86b55368d46b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619c9d6a9cfa6903a87563fd911e03be", "guid": "bfdfe7dc352907fc980b868725387e980c89fc8ad4c1f7f15e867c2974f0a8d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823557742514b0b45bf70c9d501e5a763", "guid": "bfdfe7dc352907fc980b868725387e9852cada11108170c9e8c3cfde23617ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861aa63f835438c110adec5309de2de79", "guid": "bfdfe7dc352907fc980b868725387e989f1ad3ea4e388e48b603397c6a793152"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0af26d8c3a44880d203eafdc36bcdb9", "guid": "bfdfe7dc352907fc980b868725387e98fba1eb348bacea151e8bd1e17bb5560b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd6e6390a0778a67685d55b97a60e79", "guid": "bfdfe7dc352907fc980b868725387e983d25764d0af0329691f7841bedaf8cd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ebfc7e1adee0c83219809ecb455a0f8", "guid": "bfdfe7dc352907fc980b868725387e9890e850252f380fd17574d2b66e38bd24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616465acc563a367fadcef1f7b9afe8c", "guid": "bfdfe7dc352907fc980b868725387e9819ed1beaca832086f32075dd74b407a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288380a2d20784789ca3da4e3890cabc", "guid": "bfdfe7dc352907fc980b868725387e9892370a39395788c0506a13cf5a2730f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfcfe5660e8c67bc648577e6079d2188", "guid": "bfdfe7dc352907fc980b868725387e98c5d1ae9879b9fb071fdcb00067397f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850dc4462997fc230f1b6ae3b5194f01d", "guid": "bfdfe7dc352907fc980b868725387e98dc35f0c68f44e08eae08009cf4e67a2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df917abb21be04cf72c5ddd1f45ac8e4", "guid": "bfdfe7dc352907fc980b868725387e988896421662184295f96c10717fbf5448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98975d63a1ec4126bcb9aa66cd0367f10e", "guid": "bfdfe7dc352907fc980b868725387e980375645b904d235f304669d823f44dac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986091e3746a759d345b6f202f8aa8c8ba", "guid": "bfdfe7dc352907fc980b868725387e981590a1f5117ccf967eb6f0d4d8b82732"}], "guid": "bfdfe7dc352907fc980b868725387e986871c672ece122705d26c4c056dfad7b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819723694ec8f9c5574808b542946458a", "guid": "bfdfe7dc352907fc980b868725387e98ae21280c5d8d874f4263486dc08a0a22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc545b043d76ed7e38b32616c2730726", "guid": "bfdfe7dc352907fc980b868725387e98c51d7f92803d4dd163e76108c50bc33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6be391edd2f1cfefd823237e55af5c", "guid": "bfdfe7dc352907fc980b868725387e9898cabece838683d137d5babe79c160aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096542b633f2fabd433c3cac1ae654ba", "guid": "bfdfe7dc352907fc980b868725387e9863390675146f19a9e0b9114ecde0f9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9942c862538419604a735d75b32701a", "guid": "bfdfe7dc352907fc980b868725387e9896de80422fd40efba8cdcfd9c28363eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba22b9a48a163ceeb8123dcf6a3e5de", "guid": "bfdfe7dc352907fc980b868725387e98a7106d63a12d19a5bc22e72041129ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817df5873cf1aecb155a8bd97444288ca", "guid": "bfdfe7dc352907fc980b868725387e98b10517492d2c97ddbc61111907eacffe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3456025d908673ca7a860666fa18560", "guid": "bfdfe7dc352907fc980b868725387e98884aab39f6120929805be3e807e8da42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0baed18205779f30f5856372bb66178", "guid": "bfdfe7dc352907fc980b868725387e987c608fa7cb7694ae5e1fb5a436bcfa3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d123063f625ccb18f8ec5800fc2c3fba", "guid": "bfdfe7dc352907fc980b868725387e98828cffbced13a6fe3cd5df7b46e349bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851cc199462789a48a3fa1f729224bc8f", "guid": "bfdfe7dc352907fc980b868725387e98bc21192ce2a5d60fe598abffd3427aff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801d949eedc3596ff7581e39dc9064466", "guid": "bfdfe7dc352907fc980b868725387e98488ab566115ea3f0d778d4203bdccb95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985886c91d442fccaa312fa264b0043fb0", "guid": "bfdfe7dc352907fc980b868725387e98ac41068478744a5467a7e04b5f42ee8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cd29b3d49d774cd2189f807175d7cb1", "guid": "bfdfe7dc352907fc980b868725387e98f4cb447809446b15679d543b92eafc47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c87434b98acfd5e877fa92b40204f8", "guid": "bfdfe7dc352907fc980b868725387e9826d982d3ded0ec56054fe79229e4fbc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba4d93b18f62fc9d8314ec1f7552f94", "guid": "bfdfe7dc352907fc980b868725387e981f62fe40be78a31f320e2ea583e117e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa10657d88ab05513c4a5194c18d3c94", "guid": "bfdfe7dc352907fc980b868725387e9891b4035578ae36b1adeb01a827eb30df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c087e6d24e229c2c17898c727d60455", "guid": "bfdfe7dc352907fc980b868725387e98d97b957d6939b82fd73064e89d003f62"}], "guid": "bfdfe7dc352907fc980b868725387e98b2951da1ea51f454ae9fb3da03568ee7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9825c5e6832d6a038e9ef9abf450f87665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818e1d7dfe2849b95e08804531e42cab8", "guid": "bfdfe7dc352907fc980b868725387e98698f59babad5a511bab92cc712ef8978"}], "guid": "bfdfe7dc352907fc980b868725387e98be681f4f67dbde5afd092789a305e8f3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98474ef7502f851f3523912e8e50d034e5", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98d70992afae5ab0f0c99abe3aa0f24594", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}