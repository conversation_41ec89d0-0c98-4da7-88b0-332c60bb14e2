{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8af036c08ddedd694884896d85e73f5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9833e81f435dccf7b17330354486e549ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdf0ed2550bda17d46f5046fc3b0954c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6bb15f3753e890c0837662e2ec7f00b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdf0ed2550bda17d46f5046fc3b0954c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e0da5de8f9a07b49add410037908aa0e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cfcccc146669c7ebc2f49476ad3124ef", "guid": "bfdfe7dc352907fc980b868725387e98fbd8020625d394ecd0f5fa6c244a2970", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980e7f0a8ca0f8f019714cb75da679cae3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980678c3c4ad3980d3bcd8b154c1fe538d", "guid": "bfdfe7dc352907fc980b868725387e984ba064186530a6b1825b14491580ed0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b078369f9cf80a3167297f43e2b1e782", "guid": "bfdfe7dc352907fc980b868725387e98ecadd1e18b20d42b21182bbcc21fee99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f04396452c4252c0793635820268c4", "guid": "bfdfe7dc352907fc980b868725387e9821b55df22fbe9f22211debeb4d357d06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a2883797816fb5163c09a524db2537", "guid": "bfdfe7dc352907fc980b868725387e98f9971f8fb01c0d5b1e10e3ade0ee95fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b76c8f1d79425f7e4ab99b0a2a6b5b81", "guid": "bfdfe7dc352907fc980b868725387e982c42f5aa60746530cc569cb75df352da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c6a8ff6abff76597d66c57a914a89f", "guid": "bfdfe7dc352907fc980b868725387e98110475a49bcdd5201b2a19a93fe32e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3d3b4ca9b24cdf7290b30662bb525f6", "guid": "bfdfe7dc352907fc980b868725387e9872b76b4e11820b20175235bdf97745ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871b3dadd7a0efea209708cd726e6e778", "guid": "bfdfe7dc352907fc980b868725387e985387426f0c5a9ff4e5bb2a3e9b442db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c528fb9be5d284982af8bd1f5bbc0f", "guid": "bfdfe7dc352907fc980b868725387e9887881b21b4e568deabe831c61713f44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df429811ed17d7c71dea2f870037c31d", "guid": "bfdfe7dc352907fc980b868725387e9841ef31e0664c5698840bad66cac45bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ab6273e388b32494af8542ba96e26e", "guid": "bfdfe7dc352907fc980b868725387e985e20bcaadd0c7f399296775a0b7ed6db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a9ede5a8eb3a4feab0f02d54ecb54d", "guid": "bfdfe7dc352907fc980b868725387e98c4be228597151484559fa09d20b0a09a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a59d552703b815eb716706ecf5f06f", "guid": "bfdfe7dc352907fc980b868725387e98226e58ce8f25de2faf1bd8435e4d59c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98786f046b852794d4bf032139ac5cace5", "guid": "bfdfe7dc352907fc980b868725387e98810d17f80b0231df8fc8b18a168db982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d0df27f57f874f46230196a5375dee", "guid": "bfdfe7dc352907fc980b868725387e98a64c757887b378db65ec15b8345ef809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816ea8fbdff62817ee873f40651140fa0", "guid": "bfdfe7dc352907fc980b868725387e983fc7d2381ff4fcd2b9e2e5033b6d26b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5dc012adf8169010717262e0f07904", "guid": "bfdfe7dc352907fc980b868725387e98b32b1b03ad3cc5668b381789004c1fa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a02f596b2df1eb4bd5c666229971d91", "guid": "bfdfe7dc352907fc980b868725387e9894925bf3c78e8f76ca718cf2380426c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e7637dab5114be93efd5c541aa4ac7", "guid": "bfdfe7dc352907fc980b868725387e98597561659468f9e6c5d8d0d58b9ce4c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98006db2bcd38cbfb5bcfdcedc37af9015", "guid": "bfdfe7dc352907fc980b868725387e988c3af5a8b19b702febf69696098c6df2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45aa8327d25a1b9533aee7b3a3b2f68", "guid": "bfdfe7dc352907fc980b868725387e986d70aa5b8df72da80a33f3cffcb2c2f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79acbf6d1cb15a766d5e35011e28a11", "guid": "bfdfe7dc352907fc980b868725387e986b7fa101d9ceac37008c1256a8160a3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9635bbfa7ecb561cdf140d032b16275", "guid": "bfdfe7dc352907fc980b868725387e98efcb748248f2a6e0b3b548b76f432fa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98236278145dab43170b63d5521c1ef307", "guid": "bfdfe7dc352907fc980b868725387e9827c13a960f8af9acafb1fe9170ee5a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98222b67cf229d470dacefa9e78a8aef7b", "guid": "bfdfe7dc352907fc980b868725387e986db5c05ce16ff0bafb145b5d8b4b3973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833289622a55c2c8723666bd777a4f144", "guid": "bfdfe7dc352907fc980b868725387e985b3f1ef5fe26878d1f4ef82000ef3ecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c789b44b3e3c94705d714dd093e640cf", "guid": "bfdfe7dc352907fc980b868725387e984f13a96a4c018a39063a0ead004ebff3"}], "guid": "bfdfe7dc352907fc980b868725387e98a412cdf8f8d81dbbc13ef540d31dba39", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8f76d148b86e45f5a4fde2844b1b676", "guid": "bfdfe7dc352907fc980b868725387e98947b453361f818be1a823ff2713ab164"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ca0483875414283457834de9e17fdc", "guid": "bfdfe7dc352907fc980b868725387e98b821f5a8d7faa1977410bcc62527cd66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9824c7c01df8d93bfed305a6e2b5fb3095"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989febcf50f0594135daa125ad9e14dbc4", "guid": "bfdfe7dc352907fc980b868725387e987f068f7b21529f2784fa6e684fa26896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd2b7e4839255245a7de0ddb837dec7", "guid": "bfdfe7dc352907fc980b868725387e98e58d45bcd9630fa6ae6a3265d6ca5282"}], "guid": "bfdfe7dc352907fc980b868725387e98c1586d72f60d4d51daa2f24ec126c39c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98737a6e054bf7836abeb55fcd0e9e7c5a", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98048b06781cdc2d79cee9bae0a1a675cb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}