{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980db69b3b2d454763bd5fee80ec42ca2c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f57634461bfc07541306e0e0909c0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980e4d9541c62705af531c301b453cea6a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b77ea053d833db80a19dea3c0cec528e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980e4d9541c62705af531c301b453cea6a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fcf27a1859dbb654e6d1632ecd3da9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb1195097776e7084834d421fb7e305d", "guid": "bfdfe7dc352907fc980b868725387e98ba5a719f03637779d0e4114c173859a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcde4ff6f533b1f78fe7c7981bff6277", "guid": "bfdfe7dc352907fc980b868725387e98e2307c7e3b6ededb064e5ddd2a68ec15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802d9d0befdeb9eaa1682c08c417b9ec2", "guid": "bfdfe7dc352907fc980b868725387e982d562db17f8b5b4b452d2545362214e8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98815251e79cce330a50d05fdecb7f0a4e", "guid": "bfdfe7dc352907fc980b868725387e98d04ba8a39c5cad90f8f9d0692576eaa8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3cd748c7e84d969802ed1dee03cef35", "guid": "bfdfe7dc352907fc980b868725387e98c33f235531156ee0adb6b8d65a5c7830", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcdc47673f0502508eef9c50e978016d", "guid": "bfdfe7dc352907fc980b868725387e985a895af414c847d231b5f4da7f1e0eb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086c14c4d9dfc6ad849bfd968d629acf", "guid": "bfdfe7dc352907fc980b868725387e98a1f1dfde0a897a7a48297b39703ffe87", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987738646b49cea995579b143e2c892752", "guid": "bfdfe7dc352907fc980b868725387e98cf56166b008f01c301c175ad0b214b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98569d4cc9c32f2ac01c00b09a82fb7095", "guid": "bfdfe7dc352907fc980b868725387e98d42fe72f176a80ac9bc8f49ca458e7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c93e901e540451f7f9b217402e0efe0", "guid": "bfdfe7dc352907fc980b868725387e983f53c7c613094709f31b06d10211e623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd659bf3513e38e08c04900e4a13a696", "guid": "bfdfe7dc352907fc980b868725387e982529dcf37e031935f7df29cb7a0c72be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833220b5add0485e40bb88b61b573591d", "guid": "bfdfe7dc352907fc980b868725387e98b957187b0e56ff1342b0658816919310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fcd1eaddf5896df011274fdd39ea03d", "guid": "bfdfe7dc352907fc980b868725387e98d1e1455bdecb88b6dd994789b1a8d005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984acbe0992c4443f1b3f3d92240970704", "guid": "bfdfe7dc352907fc980b868725387e984dc9a9173228d460dce8bd05febcaa21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f5ce86954e9c90ac4d3550ab3f7e2f", "guid": "bfdfe7dc352907fc980b868725387e9866e18cdc65ffb51bafc9666d133d6961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aef4fb78d35c9bed39281193672a6bb", "guid": "bfdfe7dc352907fc980b868725387e9853fba8ec8f30e77eea0ee42d162d7b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ce3e41cbbc1872c139b795385917ee", "guid": "bfdfe7dc352907fc980b868725387e9850835d8912cad2b1f1810e1d3a3f127e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d295cb26b563c4300b2401016fd94f5c", "guid": "bfdfe7dc352907fc980b868725387e98625e8428f767980263004f084f2f06fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d39e9813bf7412c67eb19531a9dec2d", "guid": "bfdfe7dc352907fc980b868725387e98c30c5ac2b99a509847068097d1b90b04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4790fc377527fe03d89f65ea7e11de", "guid": "bfdfe7dc352907fc980b868725387e98d90e924c70c687d95153625ccf22e1b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836a2081adbc8385e3ca55444cbc4c87d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2964b9b3c399d953f6323d5a34b3348", "guid": "bfdfe7dc352907fc980b868725387e986b7cfccea424129119a7e0b7223d203f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807b9ec8748fd6258627ed0db426b32c2", "guid": "bfdfe7dc352907fc980b868725387e987e2fd0030cc6bcc3a49952734b8e1436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d252d61b059fd14f9eb5e487069b6cea", "guid": "bfdfe7dc352907fc980b868725387e989addc411376632e0842314e1f3dbd3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be4c73127e7baa7efc35be9c1a1250f", "guid": "bfdfe7dc352907fc980b868725387e98f2b75083ed4223737365239d09129b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf06e4dd309e38bffb1e279ce7b5cfa", "guid": "bfdfe7dc352907fc980b868725387e98f6a65e94218fb5f98f3b3395aafcb285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986616b2c393b058ab9a657902f7b5adab", "guid": "bfdfe7dc352907fc980b868725387e98719f6caf81038a4fb6b36c401e41927f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817da2b299dbff74d7ab673a249fed552", "guid": "bfdfe7dc352907fc980b868725387e9897454b0f741f6ecb896065015ddc7084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f95a8fd93f565c0a753842a76e1894", "guid": "bfdfe7dc352907fc980b868725387e980555b502c9c0197b86706e000f128fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4cc757b1828aa03d66e3e296ec5cc13", "guid": "bfdfe7dc352907fc980b868725387e9803c38b008504781c8e28657eb9ba9929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98092efe770f6f66ade1613399e4caf264", "guid": "bfdfe7dc352907fc980b868725387e985a2ed91aac5da7e9139f8ae19e8ee587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddbbae6bb89db3e873163d962dc88d5f", "guid": "bfdfe7dc352907fc980b868725387e988ad01aaa6d9324c69a1230aa30684633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820afe58000652dad46ffbfba8ef1b687", "guid": "bfdfe7dc352907fc980b868725387e9871bc7f2e968b7164541a22f887513a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98428f4d96f5b0cc8ee5bda978794ff654", "guid": "bfdfe7dc352907fc980b868725387e98811905a9c4ba0e19039cca05ddec61d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e53ec6061ff582e60aeee1b60bbcc808", "guid": "bfdfe7dc352907fc980b868725387e9833f6e0f517dacc5b6398ab313854ccb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981721dbe8d2fa26b6e7badf2474ad6439", "guid": "bfdfe7dc352907fc980b868725387e980bb1e730b18176ad52f058cae5417ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c06f73f31192fce8ec89ef9e69bf657", "guid": "bfdfe7dc352907fc980b868725387e981af43685ae73254d59946e622f376749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4f9ffcc1297fff092d5359601b5b93", "guid": "bfdfe7dc352907fc980b868725387e98641fcdc650b720a8dfa8fb181fa118f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dde1f6d525a703b7caf328c78bea57", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9844ec617da2f257b0b49d378a54c7b385"}], "guid": "bfdfe7dc352907fc980b868725387e987ca18eb2083c48f78e919a58dc322457", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c95c6b31cc49dcf5ba698c2dfa2917e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}