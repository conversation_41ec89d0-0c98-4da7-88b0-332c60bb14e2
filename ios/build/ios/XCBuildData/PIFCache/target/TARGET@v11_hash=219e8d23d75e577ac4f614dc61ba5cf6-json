{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98156b7461e3000e90ef638db6fc80dc7b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a13393d9c3a5cff24ace84ef60f8b086", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986efc53ee563eb6c1523bf74e830d477f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989d048369c93ebe8e3c3484591bb81a1d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986efc53ee563eb6c1523bf74e830d477f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98752f57fa73067e5b31063e25baa5edae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a76d621ef8813777f11ded0c830babb4", "guid": "bfdfe7dc352907fc980b868725387e988519e4d1971ce9e5111aed8e2a7076f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872fe4e48945a6eae5f11e9ad150b1248", "guid": "bfdfe7dc352907fc980b868725387e98bc6aec6600b9fc2c219e0b05536d685c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98171ba2965a8deea63d2d7c4688e9bd4b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803833dad6abfc3e017852fcf07afe0de", "guid": "bfdfe7dc352907fc980b868725387e98c5793c7565efea962c8aab4e566e29d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98074f0f6e91bc0ad0d6b291f6717001a6", "guid": "bfdfe7dc352907fc980b868725387e98910e1627329a2b27042b7adae44ca29d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f041b52bb2bb25ca35265aea081c62c", "guid": "bfdfe7dc352907fc980b868725387e98138879af6e77943fc17be5ab09030dde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a325a800908e5e2ba0f17c67b775822", "guid": "bfdfe7dc352907fc980b868725387e98696c8be68c27385d85db513b54b9385f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881af587ec91245476467c557d053b8ce", "guid": "bfdfe7dc352907fc980b868725387e9876bba091c6a49ee5765b098bc642d690"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e26abd14e897d8e04bfd70be9f8d4ebd", "guid": "bfdfe7dc352907fc980b868725387e986f87b7032ae960bc3e15277a6cd010d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e4259f0d54d24138c8119368a1fb3c", "guid": "bfdfe7dc352907fc980b868725387e985c49437c64232efd3c51c9b70c768ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b57e04f8ade8315fce235c69cb1a4f", "guid": "bfdfe7dc352907fc980b868725387e98b6839877f1d7c0e94d06e001f061a21e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637186d7feaa548d4c6b0ca3c4410eee", "guid": "bfdfe7dc352907fc980b868725387e989a32ebd5fdccbd06eedf3e32dc670a4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d13f346d77bc85297e02233bfa9b53", "guid": "bfdfe7dc352907fc980b868725387e98f4b2c823b9674ea2d62fd1664c1c7270"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b7d31b8c88d1d5a92b0cec4ce21a80e", "guid": "bfdfe7dc352907fc980b868725387e98599329d451d6187845480b30c6f30e06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890356404fc637bc63ef8a8f5de06f710", "guid": "bfdfe7dc352907fc980b868725387e9821a3f8cd0b2c93125566780a9b470cad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e476e1ff7df943d3281bea3369ce969", "guid": "bfdfe7dc352907fc980b868725387e98085b8cace71bef3e86f998da4a91b478"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989942991bdd98bce8eacb917c6c814547", "guid": "bfdfe7dc352907fc980b868725387e9818f110c4096b18058b2584513c0cc2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d8a01ff7da72910f9676e0a5bef7dd7", "guid": "bfdfe7dc352907fc980b868725387e983f2e824c5abd0fa11c8c9870dae0b3c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec3be0d5abc49defdd732f4218bfcd66", "guid": "bfdfe7dc352907fc980b868725387e9828399f4f3d9387d80620941b970bf85c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c54a596b7fa1f986e69822544ac806e", "guid": "bfdfe7dc352907fc980b868725387e986008d95157780189d2ed92ba19c00ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc02e3ca9436485bf26930120c2f106", "guid": "bfdfe7dc352907fc980b868725387e98c156dfa8558608c45761e94cad186434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827123e34994d2d88f69e8a1634f6a7f4", "guid": "bfdfe7dc352907fc980b868725387e98c2f56be85bc4a80618975aa1f294c13c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb83c7c9efbd03f62e781fb2e3f2e917", "guid": "bfdfe7dc352907fc980b868725387e989bea0a5cbcf92843b515c07ac63b47f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5fd1a83f647b45b2322b17044429bd3", "guid": "bfdfe7dc352907fc980b868725387e98e3d6a0b91f5ed12eed415aa4346b2eab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821205c27fa96c05b0eb80d9bfecd996c", "guid": "bfdfe7dc352907fc980b868725387e98f3e23a21f9f22c563dc7e278183e3873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08fd9230cfa0319a16926bab867f588", "guid": "bfdfe7dc352907fc980b868725387e9851162d107aa865e01f55ef40fbe7ae17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6fed0e6bb65ddcc11d99637d78a99f6", "guid": "bfdfe7dc352907fc980b868725387e98bc3e7745591104cab29c3fbcabda9a7e"}], "guid": "bfdfe7dc352907fc980b868725387e9863249e6e6f9bf933f1a520832724a0dc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823eca4b3b33af74b846381c4acc45081", "guid": "bfdfe7dc352907fc980b868725387e9866ab2440941c4eaad1609e8bcf3a8274"}], "guid": "bfdfe7dc352907fc980b868725387e9820dfd420cb0019e73011fc88ac9e913b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ada79cc6f620b4aac1ed0dcb2dab3c5f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}