name: idea2app_vendor_app
description: Dashboard Idea2App Vendor.

publish_to: 'none'

version: 1.0.56+56

environment:
  sdk: '>=3.0.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  #? dart run intl_utils:generate
  #! Localization
  flutter_localizations:
    sdk: flutter

  #! Helper Package
  xr_helper:
    path: packages/xr_helper

  rounded_loading_button:
    path: packages/rounded_loading_button

  syncfusion_flutter_charts: ^28.2.7

  printing: ^5.13.2

  cupertino_icons: ^1.0.6
  fl_chart: ^0.70.2
  intl: ^0.20.2

  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.10+1
  #  dio: ^5.7.0
  #  dio: ^5.8.0+1
  get_storage: ^2.1.1
  http:
  global_configuration:
  share_plus: ^10.0.2
  shared_preferences:
  google_fonts:
  blur: ^4.0.0
  gap: ^3.0.1
  another_flushbar:
  dotted_border: ^2.1.0
  logger: ^2.0.2+1
  flutter_hooks:
  provider: ^6.1.1
  file_picker: ^9.0.2
  image_picker: ^1.1.2
  flutter_luban: ^0.1.14
  lottie: ^3.1.0
  permission_handler: ^11.4.0
  flutter_staggered_grid_view: ^0.7.0
  connectivity_plus: ^6.0.3
  shimmer: ^3.0.0
  fast_cached_network_image: ^1.3.3+5
  flutter_image_compress: ^2.3.0
  auto_height_grid_view: ^1.0.0
  url_launcher: ^6.3.0
  flutter_phoenix:
  cloudinary: ^1.2.0
  any_animated_button: ^0.0.4
  open_file: ^3.5.10
  colornames: ^0.2.0
  collection:
  percent_indicator: ^4.2.2
  smooth_page_indicator: ^1.0.0+2
  loading_animation_widget: ^1.2.1
  easy_infinite_pagination: ^0.0.6
  path: ^1.9.0

  universal_html:
  uuid: ^4.5.0
  universal_platform: ^1.1.0
  zo_animated_border: ^0.0.9

  #? Firebase
  firebase_core: ^3.4.1
  firebase_messaging: ^15.1.1
  googleapis: ^13.2.0
  googleapis_auth: ^1.6.0
  firebase_storage: ^12.3.0


  quickalert: ^1.1.0


  flutter_direct_caller_plugin: ^0.0.4



  #? Injector
  get_it: ^8.0.0
  flutter_colorpicker: ^1.1.0
  flutter_animate: ^4.5.0


  #? Splash
  animated_splash_screen: ^1.3.0
  page_transition: ^2.1.0

  #? AI
  flutter_gemini: ^2.0.5

  #? Update App
  upgrader: ^11.1.0
  package_info_plus: ^8.0.2

  url_strategy: ^0.3.0

  #? QR Code
  qr_flutter: ^4.1.0
  pretty_qr_code: ^3.3.0
  qr_flutter_wc: ^0.0.3

  flutter_exif_rotation: ^0.5.2
  exif: ^3.3.0

  dropdown_search:
    path: packages/dropdown_search

  #? Notifications
  awesome_notifications: ^0.10.1

  animated_number: ^1.0.2
  flutter_markdown: ^0.7.3+1
  #  flutter_smartlook: ^4.1.25

  #? Facebook
#  facebook_app_events: ^0.19.5

dependency_overrides:
  http: ^0.13.0
  web: ^1.0.0
  archive: ^3.6.1
  win32: ^5.5.4
  intl: ^0.19.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons:
  intl_utils: ^2.8.10

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/sounds/
    - assets/images/
    - assets/icons/
    - assets/svg/
    - assets/animated/
    - assets/fonts/
    - assets/fonts/cairo/
    - shorebird.yaml


#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  #  "launcher_icon"
  ios: true
  image_path: "assets/images/ios_logo.jpg"
  #  adaptive_icon_background: "assets/images/app_logo.png"
  #  adaptive_icon_foreground: "assets/images/app_logo.png"
  remove_alpha_ios: true


  fonts:
    - family: ProductSans
      fonts:
        - asset: assets/fonts/ProductSans-Thin.ttf
          weight: 100
        - asset: assets/fonts/ProductSans-Thin.ttf
          weight: 200
        - asset: assets/fonts/ProductSans-Light.ttf
          weight: 300
        - asset: assets/fonts/ProductSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/ProductSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/ProductSans-Bold.ttf
          weight: 600
        - asset: assets/fonts/ProductSans-Black.ttf
          weight: 700
        - asset: assets/fonts/ProductSans-Black.ttf
          weight: 800
        - asset: assets/fonts/ProductSans-Black.ttf
          weight: 900

    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-Regular.ttf
        - asset: assets/fonts/cairo/Cairo-Bold.ttf
        - asset: assets/fonts/cairo/Cairo-Black.ttf
        - asset: assets/fonts/cairo/Cairo-ExtraLight.ttf
        - asset: assets/fonts/cairo/Cairo-Light.ttf
        - asset: assets/fonts/cairo/Cairo-SemiBold.ttf

flutter_intl:
  enabled: true