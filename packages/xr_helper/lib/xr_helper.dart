library xr_helper;

//! ================================ Imports ================================
import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/src/config/app_config.dart';

//! ================================ Base View Model ================================
part 'src/base_functions/base_repo.dart';
part 'src/base_functions/base_view_model.dart';
//! ================================ Data ================================
//? Local ================================
part 'src/data/local/get_storage_service.dart';
part 'src/data/local/keys/local_keys.dart';
// part 'src/data/remote/app_exception.dart';
// part 'src/data/remote/helpers/network_mixin_helper.dart';
//? Remote ================================
// part 'src/data/remote/network/base_api_service.dart';
// part 'src/data/remote/network/network_api_service.dart';
//! ================================ Extensions ================================
part 'src/extensions/context_extensions.dart';
part 'src/extensions/date_time_extensions.dart';
part 'src/extensions/list_extensions.dart';
part 'src/extensions/navigation_extensions.dart';
part 'src/extensions/string_extensions.dart';
part 'src/extensions/widget_extensions.dart';
//! ================================ Resources ================================
part 'src/resources/app_radius.dart';
part 'src/resources/app_spaces.dart';
part 'src/resources/spaces/gap_spaces.dart';
part 'src/resources/theme/color_manager.dart';
part 'src/resources/theme/text_styles.dart';
part 'src/services/navigation_service.dart';
//! ================================ Services ================================
// part 'src/services/notifications_service.dart';
//! ================================ Shared Widgets ================================
part 'src/shared_widgets/animated/widget_animator.dart';
part 'src/shared_widgets/base_material/base_material_widget.dart';
part 'src/shared_widgets/base_widgets/base_cached_image.dart';
part 'src/shared_widgets/buttons/button.dart';
part 'src/shared_widgets/dialog/base_dialog.dart';
part 'src/shared_widgets/text_fields/date_picker.dart';
part 'src/shared_widgets/text_fields/drop_down_field.dart';
//! ================================ Utils ================================
part 'src/utils/enum_values.dart';
part 'src/utils/http_overrides.dart';
part 'src/utils/logger.dart';
part 'src/utils/toast.dart';
part 'src/utils/validations.dart';
