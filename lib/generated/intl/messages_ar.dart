// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(mainCategoryName) =>
      "هل أنت متأكد أنك تريد حذف هذا القسم الرئيسي ${mainCategoryName} ؟";

  static String m1(cityName) => "المناطق في ${cityName}";

  static String m2(cost) => "تم تغيير تكلفة التوصيل إلى ${cost}";

  static String m3(status) => "تم تغيير حالة طلبك إلى ${status}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "ContactSupport": MessageLookupByLibrary.simpleMessage("التواصل مع الدعم "),
    "IfYouDontWantToAddStockForThemYouCanChooseProductStockOrTurnOffTheInventory":
        MessageLookupByLibrary.simpleMessage(
          "إذا كنت لا تريد إضافة مخزون لهم، يمكنك اختيار مخزون المنتج أو إيقاف تشغيل المخزون",
        ),
    "Or": MessageLookupByLibrary.simpleMessage("أو"),
    "PayAttachment": MessageLookupByLibrary.simpleMessage("مرفق الدفع"),
    "ToActivateYourOnlinePayment": MessageLookupByLibrary.simpleMessage(
      "لتفعيل الدفع الإلكتروني",
    ),
    "YourFacebookLink": MessageLookupByLibrary.simpleMessage(
      "رابط الفيسبوك الخاص بك",
    ),
    "YourInstagramLink": MessageLookupByLibrary.simpleMessage(
      "رابط الإنستجرام الخاص بك",
    ),
    "YourTiktokLink": MessageLookupByLibrary.simpleMessage(
      "رابط تيكتوك الخاص بك",
    ),
    "YourWhatsApp": MessageLookupByLibrary.simpleMessage(
      "رقم الواتساب الخاص بك",
    ),
    "YourYoutubeLink": MessageLookupByLibrary.simpleMessage(
      "رابط يوتيوب الخاص بك",
    ),
    "about": MessageLookupByLibrary.simpleMessage("حول"),
    "aboutAndPrivacy": MessageLookupByLibrary.simpleMessage(
      "نبذة عنا و الخصوصية",
    ),
    "aboutDescription": MessageLookupByLibrary.simpleMessage(
      " نحن متخصصون في حلول البرمجيات، ونقدم مجموعة متنوعة من المنتجات والخبرات في تطوير تطبيقات مخصصة. يلتزم فريقنا بتصميم منتجات برامج مبتكرة، ولدينا القدرة على إنشاء تطبيقات برامج مصممة خصيصًا لتلبية متطلباتك المحددة. بالإضافة إلى ذلك، نقدم تطبيق بائع متطور يتيح لك إدارة عملك بكفاءة. نحن ملتزمون بتعزيز عملك من خلال حلولنا التكنولوجية المتقدم",
    ),
    "aboutUs": MessageLookupByLibrary.simpleMessage("نبذة عنا"),
    "accountSettings": MessageLookupByLibrary.simpleMessage("إعدادات الحساب"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "add": MessageLookupByLibrary.simpleMessage("إضافة"),
    "addArea": MessageLookupByLibrary.simpleMessage("إضافة منطقة"),
    "addBanner": MessageLookupByLibrary.simpleMessage("إضافة الإعلان"),
    "addCategory": MessageLookupByLibrary.simpleMessage("إضافة قسم"),
    "addColors": MessageLookupByLibrary.simpleMessage("إضافة الألوان"),
    "addColorsStock": MessageLookupByLibrary.simpleMessage(
      "إضافة مخزون الألوان",
    ),
    "addExpenses": MessageLookupByLibrary.simpleMessage(" اضافة مدفوعات"),
    "addMainCategory": MessageLookupByLibrary.simpleMessage("إضافة قسم رئيسي"),
    "addNewVendor": MessageLookupByLibrary.simpleMessage("إضافة بائع جديد"),
    "addPaymentMethod": MessageLookupByLibrary.simpleMessage("إضافة وسيلة دفع"),
    "addProduct": MessageLookupByLibrary.simpleMessage("إضافة منتج"),
    "addPromoCode": MessageLookupByLibrary.simpleMessage("إضافة كود ترويجي"),
    "addSizes": MessageLookupByLibrary.simpleMessage("إضافة الأحجام"),
    "addSizesStock": MessageLookupByLibrary.simpleMessage(
      "إضافة مخزون الأحجام",
    ),
    "addStocks": MessageLookupByLibrary.simpleMessage("إضافة المخزون"),
    "addTask": MessageLookupByLibrary.simpleMessage("اضافة مهمة"),
    "addToCart": MessageLookupByLibrary.simpleMessage("أضف الي السلة"),
    "addYourFirstBanner": MessageLookupByLibrary.simpleMessage(
      "إضافة أول إعلان",
    ),
    "addYourFirstCategory": MessageLookupByLibrary.simpleMessage(
      "إضافة أول قسم",
    ),
    "addYourFirstProduct": MessageLookupByLibrary.simpleMessage(
      "إضافة أول منتج",
    ),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "address_not_provided_contact_client": MessageLookupByLibrary.simpleMessage(
      "لم يتم توفير العنوان، اتصل بالعميل",
    ),
    "address_not_provided_please_call_the_client":
        MessageLookupByLibrary.simpleMessage(
          "لم يتم توفير العنوان، يرجى الاتصال بالعميل",
        ),
    "addresses_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث العناوين بنجاح",
    ),
    "ago": MessageLookupByLibrary.simpleMessage("منذ"),
    "aiAssistant": MessageLookupByLibrary.simpleMessage(
      "مساعد الذكاء الاصطناعي",
    ),
    "aiHintMessage": MessageLookupByLibrary.simpleMessage("بما تفكر..."),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "annually": MessageLookupByLibrary.simpleMessage("سنويا"),
    "apartment": MessageLookupByLibrary.simpleMessage("الشقة"),
    "appAndWebsiteSettings": MessageLookupByLibrary.simpleMessage(
      "اعدادات التطبيق/الموقع",
    ),
    "appSettings": MessageLookupByLibrary.simpleMessage("إعدادت التطبيق"),
    "appStore": MessageLookupByLibrary.simpleMessage("متجر أبل"),
    "app_language": MessageLookupByLibrary.simpleMessage("لغة التطبيق"),
    "app_settings": MessageLookupByLibrary.simpleMessage("إعدادات التطبيق"),
    "appearance": MessageLookupByLibrary.simpleMessage("المظهر"),
    "applicationAndWebsiteSettings": MessageLookupByLibrary.simpleMessage(
      "إعدادات التطبيق/ الموقع",
    ),
    "application_preferences": MessageLookupByLibrary.simpleMessage(
      "تفضيلات التطبيق",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("تطبيق"),
    "approve": MessageLookupByLibrary.simpleMessage("موافقة"),
    "approveRequest": MessageLookupByLibrary.simpleMessage(
      "الموافقة على الطلب",
    ),
    "approveSubscriptionRequest": MessageLookupByLibrary.simpleMessage(
      "الموافقة على الطلب",
    ),
    "approved": MessageLookupByLibrary.simpleMessage("موافق عليها"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "arabicDescription": MessageLookupByLibrary.simpleMessage("الوصف العربي"),
    "arabicName": MessageLookupByLibrary.simpleMessage("الاسم العربي"),
    "areYouSureYouWantToApproveThisSubscriptionRequest":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد الموافقة على هذا الطلب ؟",
        ),
    "areYouSureYouWantToCancelThisOrderOf":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد إلغاء هذا الطلب من العميل ؟",
        ),
    "areYouSureYouWantToDeleteThisColor": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا اللون ؟",
    ),
    "areYouSureYouWantToDeleteThisExpense":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد حذف هذه الصرف ؟",
        ),
    "areYouSureYouWantToDeleteThisMainCategory": m0,
    "areYouSureYouWantToDeleteThisPayment":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد حذف وسيلة الدفع ؟",
        ),
    "areYouSureYouWantToDeleteThisPromoCode":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد حذف هذا الكود الترويجي ؟",
        ),
    "areYouSureYouWantToDeleteThisSize": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا الحجم ؟",
    ),
    "areYouSureYouWantToDeleteThisSubscriptionRequest":
        MessageLookupByLibrary.simpleMessage(
          "هل أنت متأكد أنك تريد حذف هذا الطلب ؟",
        ),
    "areYouSureYouWantToDeleteThisTask": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذه المهمة ؟",
    ),
    "areYouSureYouWantToDeleteThisVendor": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا البائع ؟",
    ),
    "areYouSureYouWantToDeleteYourAccount":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متأكد انك تريد حذف حسابك ؟",
        ),
    "areYouSureYouWantToExitTheApp": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد الخروج من التطبيق ؟",
    ),
    "area": MessageLookupByLibrary.simpleMessage("المنطقة"),
    "areas": MessageLookupByLibrary.simpleMessage("المناطق"),
    "areasIn": m1,
    "askMeAnything": MessageLookupByLibrary.simpleMessage(
      "اسألني في أي حاجة\n(تسويق، مبيعات، تكنولوجيا، إلخ...)",
    ),
    "attachment": MessageLookupByLibrary.simpleMessage("مرفق"),
    "back": MessageLookupByLibrary.simpleMessage("رجوع"),
    "bankAccount": MessageLookupByLibrary.simpleMessage("حساب بنك"),
    "bannerLimitReached": MessageLookupByLibrary.simpleMessage(
      "لقد وصلت إلى الحد الأقصى لللإعلانات !",
    ),
    "banners": MessageLookupByLibrary.simpleMessage("الإعلانات"),
    "bestImageSize": MessageLookupByLibrary.simpleMessage(
      "3000W X 1200H هو الحجم المناسب للإعلان",
    ),
    "building": MessageLookupByLibrary.simpleMessage("المبنى"),
    "bulkEdit": MessageLookupByLibrary.simpleMessage("تعديل الأسعار"),
    "bulkEditProducts": MessageLookupByLibrary.simpleMessage(
      "تعديل جماعي للمنتجات",
    ),
    "businessName": MessageLookupByLibrary.simpleMessage("اسم المشروع"),
    "businessType": MessageLookupByLibrary.simpleMessage("نوع المشروع"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "canceled": MessageLookupByLibrary.simpleMessage("ملغى"),
    "cantChangeStatus": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار الحالة السابقة أولاً",
    ),
    "cart": MessageLookupByLibrary.simpleMessage("السلة"),
    "carts_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث السلات بنجاح",
    ),
    "cash_on_delivery": MessageLookupByLibrary.simpleMessage(
      "الدفع عند الاستلام",
    ),
    "categories": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "categoriesSettings": MessageLookupByLibrary.simpleMessage(
      "اعدادات الاقسام",
    ),
    "categoriesTab": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "category": MessageLookupByLibrary.simpleMessage("الاقسام"),
    "categoryLimitReached": MessageLookupByLibrary.simpleMessage(
      "لقد وصلت إلى الحد الأقصى للأقسام !\nمن فضلك قم بترقية خطتك...",
    ),
    "category_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الفئة بنجاح",
    ),
    "changePlan": MessageLookupByLibrary.simpleMessage("تغيير الخطة"),
    "checkedOutSuccessfully": MessageLookupByLibrary.simpleMessage(
      "لقد تم عمل الطلب بنجاح",
    ),
    "checkout": MessageLookupByLibrary.simpleMessage("الدفع"),
    "choosePaymentMethods": MessageLookupByLibrary.simpleMessage(
      "قم بإضافة وسيلة دفع",
    ),
    "chooseQRType": MessageLookupByLibrary.simpleMessage("اختر نوع QR"),
    "chooseYourPlan": MessageLookupByLibrary.simpleMessage("اختر خطتك"),
    "city": MessageLookupByLibrary.simpleMessage("المحافظة"),
    "clearFilter": MessageLookupByLibrary.simpleMessage("مسح الفلتر"),
    "clickToBrowse": MessageLookupByLibrary.simpleMessage("انقر لإختيار صورة"),
    "clickToBrowseLogo": MessageLookupByLibrary.simpleMessage(
      "انقر لإختيار اللوجو",
    ),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "code": MessageLookupByLibrary.simpleMessage("الكود"),
    "color": MessageLookupByLibrary.simpleMessage("اللون"),
    "colorAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "هذا اللون موجود بالفعل",
    ),
    "colorStockIInventoryIsOff": MessageLookupByLibrary.simpleMessage(
      "مخزون اللون مغلق",
    ),
    "colors": MessageLookupByLibrary.simpleMessage("الألوان"),
    "colorsAndSizesStock": MessageLookupByLibrary.simpleMessage(
      "مخزون الألوان و الأحجام",
    ),
    "completed": MessageLookupByLibrary.simpleMessage("منجز"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmTheChanges": MessageLookupByLibrary.simpleMessage(
      "هل ترغب في تأكيد حفظ التغييرات؟",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmed": MessageLookupByLibrary.simpleMessage("تم التأكيد"),
    "contactUs": MessageLookupByLibrary.simpleMessage("تواصل معنا"),
    "copiedToClipboard": MessageLookupByLibrary.simpleMessage(
      "تم النسخ إلى الحافظة",
    ),
    "cost": MessageLookupByLibrary.simpleMessage("التكلفة"),
    "customer": MessageLookupByLibrary.simpleMessage("تفاصيل العميل"),
    "customerInfo": MessageLookupByLibrary.simpleMessage("بيانات العميل"),
    "customers": MessageLookupByLibrary.simpleMessage("العملاء"),
    "dailyOrders": MessageLookupByLibrary.simpleMessage("الطلبات\nاليومية"),
    "dailyProfit": MessageLookupByLibrary.simpleMessage("الأرباح\nاليومية"),
    "dark": MessageLookupByLibrary.simpleMessage("داكن"),
    "dark_mode": MessageLookupByLibrary.simpleMessage("الوضع الداكن"),
    "dashboard": MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "dateAndTime": MessageLookupByLibrary.simpleMessage("التاريخ و الوقت"),
    "days": MessageLookupByLibrary.simpleMessage("يوم"),
    "defaultLanguage": MessageLookupByLibrary.simpleMessage("اللغة الافتراضية"),
    "defaultLanguageDescription": MessageLookupByLibrary.simpleMessage(
      "هذه اللغة ستكون الافتراضية لعملائك في التطبيق أو الموقع.",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
    "deleteCategory": MessageLookupByLibrary.simpleMessage("حذف القسم"),
    "deleteCategoryConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذه القسم ؟",
    ),
    "deleteExpense": MessageLookupByLibrary.simpleMessage("إلغاء الصرف"),
    "deleteProductConfirmationBanner": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذه الاعلان ؟",
    ),
    "deleteProductConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذه المنتج ؟",
    ),
    "deletePromoCode": MessageLookupByLibrary.simpleMessage("حذف كود ترويجي"),
    "deletePromoCodeConfirmationMessage": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا الكود الترويجي ؟",
    ),
    "deleteTask": MessageLookupByLibrary.simpleMessage("إلغاء المهمة"),
    "deleted": MessageLookupByLibrary.simpleMessage("تم الحذف"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الحذف بنجاح",
    ),
    "delivered": MessageLookupByLibrary.simpleMessage("تم التوصيل"),
    "delivery": MessageLookupByLibrary.simpleMessage("جاري التوصيل"),
    "deliveryAddress": MessageLookupByLibrary.simpleMessage("عنوان التوصيل"),
    "deliveryCostChangedTo": m2,
    "delivery_addresses": MessageLookupByLibrary.simpleMessage(
      "عناوين التوصيل",
    ),
    "delivery_confirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد التوصيل",
    ),
    "delivery_fee": MessageLookupByLibrary.simpleMessage("رسوم التوصيل"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "discount": MessageLookupByLibrary.simpleMessage("الخصم"),
    "dismiss": MessageLookupByLibrary.simpleMessage("تجاهل"),
    "doNotHaveAnyOrder": MessageLookupByLibrary.simpleMessage(
      "مفيش طلبات هنا... 🙂",
    ),
    "done": MessageLookupByLibrary.simpleMessage("تم"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "dont_have_any_item_in_the_notification_list":
        MessageLookupByLibrary.simpleMessage(
          "لا توجد عناصر في قائمة الإشعارات",
        ),
    "dont_have_any_item_in_your_cart": MessageLookupByLibrary.simpleMessage(
      "لا توجد عناصر في سلة التسوق الخاصة بك",
    ),
    "earnings": MessageLookupByLibrary.simpleMessage("الأرباح"),
    "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
    "editBanner": MessageLookupByLibrary.simpleMessage("تعديل الإعلان"),
    "editCategory": MessageLookupByLibrary.simpleMessage(" تعديل قسم"),
    "editMainCategory": MessageLookupByLibrary.simpleMessage("تعديل قسم رئيسي"),
    "editPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "تعديل وسيلة دفع",
    ),
    "editProduct": MessageLookupByLibrary.simpleMessage("تعديل منتج"),
    "editProfile": MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
    "editPromoCode": MessageLookupByLibrary.simpleMessage("تعديل كود ترويجي"),
    "editShipping": MessageLookupByLibrary.simpleMessage("تغيير سعر الشحن"),
    "editSize": MessageLookupByLibrary.simpleMessage("تعديل الحجم"),
    "editTask": MessageLookupByLibrary.simpleMessage("تعديل مهمة"),
    "editVendor": MessageLookupByLibrary.simpleMessage("تعديل البائع"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني موجود بالفعل",
    ),
    "emailAndWebsiteNameAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني واسم الموقع موجودان بالفعل",
    ),
    "emailIsInvalid": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني غير صحيح",
    ),
    "email_address": MessageLookupByLibrary.simpleMessage(
      "عنوان البريد الإلكتروني",
    ),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "englishDescription": MessageLookupByLibrary.simpleMessage(
      "الوصف الإنجليزي",
    ),
    "englishName": MessageLookupByLibrary.simpleMessage("الاسم الإنجليزي"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterColor": MessageLookupByLibrary.simpleMessage("أدخل اللون"),
    "enterSize": MessageLookupByLibrary.simpleMessage("أدخل الحجم"),
    "enterValidEmail": MessageLookupByLibrary.simpleMessage(
      "أدخل بريد إلكتروني صحيح",
    ),
    "enterValidLink": MessageLookupByLibrary.simpleMessage("أدخل رابط صحيح"),
    "enterValidNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقمًا صحيحًا",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتف صحيح",
    ),
    "enterWebsiteName": MessageLookupByLibrary.simpleMessage("أدخل اسم لموقعك"),
    "enterYourMessage": MessageLookupByLibrary.simpleMessage("أدخل رسالتك !"),
    "error_verify_email_settings": MessageLookupByLibrary.simpleMessage(
      "خطأ! تحقق من إعدادات البريد الإلكتروني",
    ),
    "exitTheApp": MessageLookupByLibrary.simpleMessage("الخروج من التطبيق"),
    "expenses": MessageLookupByLibrary.simpleMessage("المصروفات"),
    "expireDate": MessageLookupByLibrary.simpleMessage("تاريخ الانتهاء"),
    "expired": MessageLookupByLibrary.simpleMessage("منتهي"),
    "extraSettings": MessageLookupByLibrary.simpleMessage("إعدادات إضافية"),
    "extras": MessageLookupByLibrary.simpleMessage("إضافات"),
    "facebook": MessageLookupByLibrary.simpleMessage("فيسبوك"),
    "failed": MessageLookupByLibrary.simpleMessage("فشل"),
    "failedToSendNotificationsToUsers": MessageLookupByLibrary.simpleMessage(
      "فشل في إرسال الإشعارات إلى المستخدمين",
    ),
    "favorite_products": MessageLookupByLibrary.simpleMessage(
      "المنتجات المفضلة",
    ),
    "featured": MessageLookupByLibrary.simpleMessage("مميز"),
    "filterByDate": MessageLookupByLibrary.simpleMessage("فلترة بالتاريخ"),
    "filterWithDateRange": MessageLookupByLibrary.simpleMessage(
      "فلترة بنطاق التاريخ",
    ),
    "floor": MessageLookupByLibrary.simpleMessage("الطابق"),
    "free": MessageLookupByLibrary.simpleMessage("مجانية"),
    "freePlan": MessageLookupByLibrary.simpleMessage("الخطة المجانية"),
    "freeShipping": MessageLookupByLibrary.simpleMessage("الشحن مجانا"),
    "freeWebsite": MessageLookupByLibrary.simpleMessage("الموقع المجاني"),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "full_address": MessageLookupByLibrary.simpleMessage("العنوان الكامل"),
    "full_name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "generalInformation": MessageLookupByLibrary.simpleMessage("معلومات عامة"),
    "generate": MessageLookupByLibrary.simpleMessage("إنشاء"),
    "help__support": MessageLookupByLibrary.simpleMessage("المساعدة والدعم"),
    "help_support": MessageLookupByLibrary.simpleMessage("المساعدة والدعم"),
    "help_supports": MessageLookupByLibrary.simpleMessage("المساعدة والدعم"),
    "hide": MessageLookupByLibrary.simpleMessage("إخفاء"),
    "hint_full_address": MessageLookupByLibrary.simpleMessage(
      "١٢ شارع، مدينة ٢١٦٦٣، البلد",
    ),
    "history": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "home": MessageLookupByLibrary.simpleMessage("الصفحة الرئيسية"),
    "home_address": MessageLookupByLibrary.simpleMessage("عنوان المنزل"),
    "iHaveAnAccount": MessageLookupByLibrary.simpleMessage("لدي حساب بالفعل"),
    "i_forgot_password": MessageLookupByLibrary.simpleMessage(
      "نسيت كلمة المرور؟",
    ),
    "ifYouDontNeedToManageProductStockMakeSureToTurnOnTheInventoryAndLeaveItEmpty":
        MessageLookupByLibrary.simpleMessage(
          "إذا لم تحتاج إلى إدارة مخزون المنتج، تأكد من فتح المخزون وتركه فارغًا",
        ),
    "ifYouEnableThisCustomersWillSeeThePromoCodeInTheAppOrWebsitePromoList":
        MessageLookupByLibrary.simpleMessage(
          "إذا قمت بتفعيل هذا، سيتمكن العملاء من رؤية الكود الترويجي في قائمة الترويج في التطبيق أو الموقع",
        ),
    "ifYouNeedToChangeYourWebsiteNamePlease":
        MessageLookupByLibrary.simpleMessage(
          "إذا كنت بحاجة لتغيير اسم موقعك، من فضلك",
        ),
    "ifYouTurnOnTheInventoryYouWillRemoveSizeAndColorsStock":
        MessageLookupByLibrary.simpleMessage(
          "إذا قمت بفتح المخزون، ستقوم بإزالة مخزون الألوان و الأحجام",
        ),
    "ifYouTurnOofTheInventoryYouWillRemoveTheProductStock":
        MessageLookupByLibrary.simpleMessage(
          "إذا قمت بإغلاق المخزون، ستقوم بإزالة مخزون المنتج",
        ),
    "inStock": MessageLookupByLibrary.simpleMessage("متاح"),
    "instagram": MessageLookupByLibrary.simpleMessage("إنستجرام"),
    "invalidWebsiteName": MessageLookupByLibrary.simpleMessage(
      "اسم الموقع غير صحيح",
    ),
    "inventory": MessageLookupByLibrary.simpleMessage("المخزون"),
    "invoice": MessageLookupByLibrary.simpleMessage("الفاتورة"),
    "invoiceId": MessageLookupByLibrary.simpleMessage("برقم الفاتورة"),
    "invoices": MessageLookupByLibrary.simpleMessage("الفواتير"),
    "isActive": MessageLookupByLibrary.simpleMessage("نشط"),
    "isDone": MessageLookupByLibrary.simpleMessage("منتهية"),
    "isPaid": MessageLookupByLibrary.simpleMessage("تم الدفع"),
    "isPercentage": MessageLookupByLibrary.simpleMessage("نسبة مئوية"),
    "items": MessageLookupByLibrary.simpleMessage("العناصر"),
    "itsNotActiveAndWillNotBeDisplayedToYourCustomers":
        MessageLookupByLibrary.simpleMessage("ليس نشطًا ولن يظهر لعملائك"),
    "john_doe": MessageLookupByLibrary.simpleMessage("جون دو"),
    "keep_your_old_meals_of_this_market": MessageLookupByLibrary.simpleMessage(
      "احتفظ بوجباتك القديمة من هذا السوق",
    ),
    "languages": MessageLookupByLibrary.simpleMessage("اللغات"),
    "large": MessageLookupByLibrary.simpleMessage("كبير"),
    "lastMonth": MessageLookupByLibrary.simpleMessage("الشهر الماضي"),
    "lastQuarter": MessageLookupByLibrary.simpleMessage("الربع الأخير"),
    "lastSemiAnnual": MessageLookupByLibrary.simpleMessage(
      "النصف السنوي الماضي",
    ),
    "lastWeek": MessageLookupByLibrary.simpleMessage("الأسبوع الماضي"),
    "lastYear": MessageLookupByLibrary.simpleMessage("العام الماضي"),
    "latestOrders": MessageLookupByLibrary.simpleMessage("الطلبات الأخيرة"),
    "left": MessageLookupByLibrary.simpleMessage("متبقي"),
    "letsLoginToYourAccountFirst": MessageLookupByLibrary.simpleMessage(
      "لنبدأ بتسجيل الدخول إلى\nحسابك أولاً!",
    ),
    "lets_start_with_login": MessageLookupByLibrary.simpleMessage(
      "لنبدأ بتسجيل الدخول!",
    ),
    "lets_start_with_register": MessageLookupByLibrary.simpleMessage(
      "لنبدأ بالتسجيل!",
    ),
    "light": MessageLookupByLibrary.simpleMessage("مضيء"),
    "light_mode": MessageLookupByLibrary.simpleMessage("الوضع الفاتح"),
    "link": MessageLookupByLibrary.simpleMessage("الرابط"),
    "log_out": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "logoSize": MessageLookupByLibrary.simpleMessage("حجم اللوجو"),
    "long_press_to_edit_item_swipe_item_to_delete_it":
        MessageLookupByLibrary.simpleMessage(
          "اضغط مع الاستمرار لتحرير العنصر، اسحب العنصر لحذفه",
        ),
    "mainCategories": MessageLookupByLibrary.simpleMessage("الأقسام الرئيسية"),
    "mainCategoriesTab": MessageLookupByLibrary.simpleMessage(
      "الأقسام الرئيسية",
    ),
    "mainCategory": MessageLookupByLibrary.simpleMessage("قسم رئيسي"),
    "mainCategorySettingsDesc": MessageLookupByLibrary.simpleMessage(
      "أنشئ فئة رئيسية لتجميع الفئات الفرعية ذات الصلة تحت تصنيف واحد. على سبيل المثال: فئة \'رجالي\' يمكن أن تحتوي على \'سترات\' و\'تيشيرتات\' و\'بناطيل\'.",
    ),
    "manageSizesAndColors": MessageLookupByLibrary.simpleMessage(
      "إدارة الأحجام و الألوان",
    ),
    "market_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث السوق بنجاح",
    ),
    "maxUploadFileSizeIsOnly10MB": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لحجم الصورة هو 10 ميجا بايت فقط",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى 4 صور فقط",
    ),
    "medium": MessageLookupByLibrary.simpleMessage("متوسط"),
    "minimumOrderCost": MessageLookupByLibrary.simpleMessage(
      "الحد الأدنى للطلب",
    ),
    "monthly": MessageLookupByLibrary.simpleMessage("الشهرية"),
    "more": MessageLookupByLibrary.simpleMessage("المزيد"),
    "myWebsite": MessageLookupByLibrary.simpleMessage("موقعى"),
    "myWebsiteLink": MessageLookupByLibrary.simpleMessage("رابط موقعي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "newOrder": MessageLookupByLibrary.simpleMessage("طلب جديد"),
    "newOrdersAlertBell": MessageLookupByLibrary.simpleMessage(
      "جرس تنبيه للطلبات الجديدة",
    ),
    "new_order_from_costumer": MessageLookupByLibrary.simpleMessage(
      "طلب جديد من العميل",
    ),
    "noAreasFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على مناطق",
    ),
    "noBanners": MessageLookupByLibrary.simpleMessage("لا يوجد إعلانات"),
    "noCategories": MessageLookupByLibrary.simpleMessage("لا يوجد أقسام"),
    "noColorsAdded": MessageLookupByLibrary.simpleMessage(
      "لا يوجد ألوان مضافة",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("لا يوجد بيانات"),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noDomainFees": MessageLookupByLibrary.simpleMessage("(دومين بدون رسوم)"),
    "noExpenses": MessageLookupByLibrary.simpleMessage("لا يوجد مصرفات"),
    "noInternet": MessageLookupByLibrary.simpleMessage(
      "لا يوجد اتصال بالإنترنت",
    ),
    "noOrdersFound": MessageLookupByLibrary.simpleMessage("لا يوجد طلبات"),
    "noPaymentMethods": MessageLookupByLibrary.simpleMessage(
      "لا توجد وسائل دفع",
    ),
    "noProductsFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على منتجات",
    ),
    "noProductsInThisCategory": MessageLookupByLibrary.simpleMessage(
      "لا يوجد منتجات في هذا القسم",
    ),
    "noPromoCodes": MessageLookupByLibrary.simpleMessage(
      "لا توجد أكواد ترويجية",
    ),
    "noResultFound": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
    "noSelectedSubCategories": MessageLookupByLibrary.simpleMessage(
      "لا توجد فئات فرعية",
    ),
    "noSizesAdded": MessageLookupByLibrary.simpleMessage("لا يوجد أحجام مضافة"),
    "noStoreInvoices": MessageLookupByLibrary.simpleMessage(
      "لا توجد فواتير للمتجر",
    ),
    "noSubCategories": MessageLookupByLibrary.simpleMessage(
      "لا توجد فئات فرعية متاحة",
    ),
    "noSubscriptionRequests": MessageLookupByLibrary.simpleMessage(
      "لا يوجد طلبات اشتراك",
    ),
    "noTasks": MessageLookupByLibrary.simpleMessage("لا يوجد مهام"),
    "noUsersFound": MessageLookupByLibrary.simpleMessage("لا يوجد عملاء"),
    "noUsersOrders": MessageLookupByLibrary.simpleMessage(
      "لا يوجد طلبات لمستخدمين",
    ),
    "noVendorsFound": MessageLookupByLibrary.simpleMessage("لا يوجد بائعين"),
    "not_a_valid_address": MessageLookupByLibrary.simpleMessage(
      "ليس عنوانًا صحيحًا",
    ),
    "not_a_valid_biography": MessageLookupByLibrary.simpleMessage(
      "ليست سيرة ذاتية صحيحة",
    ),
    "not_a_valid_cvc": MessageLookupByLibrary.simpleMessage(
      "ليس رمز CVC صحيحًا",
    ),
    "not_a_valid_date": MessageLookupByLibrary.simpleMessage(
      "ليست تاريخًا صحيحًا",
    ),
    "not_a_valid_email": MessageLookupByLibrary.simpleMessage(
      "ليس بريدًا إلكترونيًا صحيحًا",
    ),
    "not_a_valid_full_name": MessageLookupByLibrary.simpleMessage(
      "ليس اسمًا صحيحًا",
    ),
    "not_a_valid_number": MessageLookupByLibrary.simpleMessage(
      "ليس رقمًا صحيحًا",
    ),
    "not_a_valid_phone": MessageLookupByLibrary.simpleMessage(
      "ليس رقم هاتف صحيح",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "notifications_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الإشعارات بنجاح",
    ),
    "off": MessageLookupByLibrary.simpleMessage("مغلق"),
    "on": MessageLookupByLibrary.simpleMessage("مفتوح"),
    "onSale": MessageLookupByLibrary.simpleMessage("يوجد خصم "),
    "oneMonth": MessageLookupByLibrary.simpleMessage("شهر"),
    "oneTimePrice": MessageLookupByLibrary.simpleMessage("شراء مرة واحدة"),
    "optional": MessageLookupByLibrary.simpleMessage("إختياري"),
    "orderDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
    "orderId": MessageLookupByLibrary.simpleMessage("برقم الطلب"),
    "orderStatus": MessageLookupByLibrary.simpleMessage("حالة الطلب"),
    "orderStatusChangedTo": m3,
    "orderTotal": MessageLookupByLibrary.simpleMessage("إجمالي الطلب"),
    "order_details": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
    "order_id": MessageLookupByLibrary.simpleMessage("رقم الطلب"),
    "order_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الطلب بنجاح",
    ),
    "order_satatus_changed": MessageLookupByLibrary.simpleMessage(
      "تم تغيير حالة الطلب",
    ),
    "ordered_products": MessageLookupByLibrary.simpleMessage(
      "المنتجات المطلوبة",
    ),
    "orders": MessageLookupByLibrary.simpleMessage("الطلبات"),
    "ordersAndPricing": MessageLookupByLibrary.simpleMessage(
      "الطلبات و اسعار الشحن",
    ),
    "ordersSettings": MessageLookupByLibrary.simpleMessage("إعدادات الطلبات"),
    "orders_history": MessageLookupByLibrary.simpleMessage("سجل الطلبات"),
    "orders_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الطلبات بنجاح",
    ),
    "paid": MessageLookupByLibrary.simpleMessage("تم الدفع"),
    "paidAmount": MessageLookupByLibrary.simpleMessage("المبلغ المدفوع"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordLength": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور يجب أن تكون 8 أحرف على الأقل",
    ),
    "payNow": MessageLookupByLibrary.simpleMessage("ادفع الآن"),
    "payWithInstapay": MessageLookupByLibrary.simpleMessage("ادفع بالإنستاباى"),
    "payWithVodafoneCash": MessageLookupByLibrary.simpleMessage(
      "ادفع بفودافون كاش",
    ),
    "paymentAttachment": MessageLookupByLibrary.simpleMessage("صورة الدفع"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("وسائل الدفع"),
    "paymentSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الدفع بنجاح",
    ),
    "payment_mode": MessageLookupByLibrary.simpleMessage("وضع الدفع"),
    "payment_settings": MessageLookupByLibrary.simpleMessage("إعدادات الدفع"),
    "payment_settings_updated_successfully":
        MessageLookupByLibrary.simpleMessage("تم تحديث إعدادات الدفع بنجاح"),
    "paymentsAndPromotions": MessageLookupByLibrary.simpleMessage(
      "وسائل الدفع و الترويج",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "phone2": MessageLookupByLibrary.simpleMessage("الهاتف 2"),
    "phoneIsInvalid": MessageLookupByLibrary.simpleMessage(
      "رقم الهاتف غير صحيح",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "phoneOrPayLink": MessageLookupByLibrary.simpleMessage(
      "رقم هاتفك أو رابط الدفع",
    ),
    "pickAColor": MessageLookupByLibrary.simpleMessage("اختر لون !"),
    "plan": MessageLookupByLibrary.simpleMessage("الخطة"),
    "playStore": MessageLookupByLibrary.simpleMessage("متجر جوجل"),
    "pleaseAddCategoryFirst": MessageLookupByLibrary.simpleMessage(
      "من فضلك قم بإضافة قسم أولاً",
    ),
    "pleaseAddPriceToYourAllSizes": MessageLookupByLibrary.simpleMessage(
      "من فضلك قم بإضافة سعر لجميع الأحجام",
    ),
    "pleaseAttachPaymentAttachment": MessageLookupByLibrary.simpleMessage(
      "من فضلك قم بإرفاق صورة الدفع",
    ),
    "pleaseChooseQrType": MessageLookupByLibrary.simpleMessage(
      "من فضلك اختر نوع QR",
    ),
    "pleaseContactSupport": MessageLookupByLibrary.simpleMessage(
      "من فضلك تواصل مع الدعم",
    ),
    "pleaseSelect": MessageLookupByLibrary.simpleMessage("من فضلك اختر"),
    "pleaseSelectSubCategories": MessageLookupByLibrary.simpleMessage(
      "من فضلك اختر الأقسام الفرعية",
    ),
    "premiumWebsite": MessageLookupByLibrary.simpleMessage("الموقع المميز"),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "pricingPlan": MessageLookupByLibrary.simpleMessage("خطة الأسعار"),
    "primaryColor": MessageLookupByLibrary.simpleMessage("اللون الأساسي"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "product": MessageLookupByLibrary.simpleMessage("المنتج"),
    "productLimitReached": MessageLookupByLibrary.simpleMessage(
      "لقد وصلت إلى الحد الأقصى للمنتجات !\nمن فضلك قم بترقية خطتك...",
    ),
    "productStatus": MessageLookupByLibrary.simpleMessage("حالة المنتج"),
    "productStock": MessageLookupByLibrary.simpleMessage("مخزون المنتج"),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "products_ordered": MessageLookupByLibrary.simpleMessage(
      "Products Ordered",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "profile_settings": MessageLookupByLibrary.simpleMessage(
      "إعدادات الملف الشخصي",
    ),
    "profile_settings_updated_successfully":
        MessageLookupByLibrary.simpleMessage(
          "تم تحديث إعدادات الملف الشخصي بنجاح",
        ),
    "promoCode": MessageLookupByLibrary.simpleMessage("الكود الترويجي"),
    "promoCodeAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "هذا الكود الترويجي موجود بالفعل",
    ),
    "promoCodes": MessageLookupByLibrary.simpleMessage("الأكواد الترويجية"),
    "qr": MessageLookupByLibrary.simpleMessage("رمز QR"),
    "qrCircle": MessageLookupByLibrary.simpleMessage("QR دائرة"),
    "qrLanding": MessageLookupByLibrary.simpleMessage("صفحة QR"),
    "qrSettings": MessageLookupByLibrary.simpleMessage("إعدادات QR"),
    "qrSquare": MessageLookupByLibrary.simpleMessage("QR مربع"),
    "qrType": MessageLookupByLibrary.simpleMessage("نوع QR"),
    "quantity": MessageLookupByLibrary.simpleMessage("الكمية"),
    "recent_orders": MessageLookupByLibrary.simpleMessage("الطلبات الأخيرة"),
    "recents_search": MessageLookupByLibrary.simpleMessage(
      "عمليات البحث الأخيرة",
    ),
    "refunded": MessageLookupByLibrary.simpleMessage("مرتجع"),
    "register": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "remainingAmount": MessageLookupByLibrary.simpleMessage("المبلغ المتبقي"),
    "renewNow": MessageLookupByLibrary.simpleMessage("تجديد الآن"),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "reportsAndOperations": MessageLookupByLibrary.simpleMessage(
      "التقارير و العمليات",
    ),
    "requestApproved": MessageLookupByLibrary.simpleMessage(
      "تمت الموافقة على الطلب",
    ),
    "requestDate": MessageLookupByLibrary.simpleMessage("تاريخ الطلب"),
    "requiredField": MessageLookupByLibrary.simpleMessage("هذا الحقل مطلوب"),
    "reset": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
    "reset_your_cart_and_order_meals_form_this_market":
        MessageLookupByLibrary.simpleMessage(
          "أعد تعيين سلة التسوق الخاصة بك واطلب وجبات من هذا السوق",
        ),
    "salePrice": MessageLookupByLibrary.simpleMessage("السعر بعد الخصم"),
    "salePriceMustBeLessThanPrice": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون سعر الخصم أقل من السعر الأصلي",
    ),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchBy": MessageLookupByLibrary.simpleMessage("البحث ب"),
    "searchProduct": MessageLookupByLibrary.simpleMessage("البحث عن منتج"),
    "select": MessageLookupByLibrary.simpleMessage("اختر"),
    "selectAll": MessageLookupByLibrary.simpleMessage("اختر الكل"),
    "selectAreas": MessageLookupByLibrary.simpleMessage("اختر المناطق"),
    "selectCategories": MessageLookupByLibrary.simpleMessage("اختر الأقسام"),
    "selectProducts": MessageLookupByLibrary.simpleMessage("اختر المنتجات"),
    "selectVendor": MessageLookupByLibrary.simpleMessage("اختر البائع"),
    "selectYourBusinessType": MessageLookupByLibrary.simpleMessage(
      "اختر نوع مشروعك",
    ),
    "selectedAreas": MessageLookupByLibrary.simpleMessage("المناطق المختارة"),
    "selectedProducts": MessageLookupByLibrary.simpleMessage(
      "المنتجات المختارة",
    ),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendNotification": MessageLookupByLibrary.simpleMessage("إرسال إشعار"),
    "sendRequest": MessageLookupByLibrary.simpleMessage("إرسال الطلب"),
    "sendSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الإرسال بنجاح",
    ),
    "senderInfo": MessageLookupByLibrary.simpleMessage("بيانات الراسل"),
    "setting": MessageLookupByLibrary.simpleMessage("إعدادات"),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
    "shareLink": MessageLookupByLibrary.simpleMessage("مشاركة الرابط"),
    "shareQr": MessageLookupByLibrary.simpleMessage("مشاركة QR"),
    "shippingCost": MessageLookupByLibrary.simpleMessage("سعر الشحن"),
    "should_be_a_valid_email": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون بريدًا إلكترونيًا صحيحًا",
    ),
    "should_be_more_than_3_characters": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون أكثر من 3 أحرف",
    ),
    "should_be_more_than_3_letters": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون أكثر من 3 أحرف",
    ),
    "should_be_more_than_6_letters": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون أكثر من 6 أحرف",
    ),
    "showDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
    "showInList": MessageLookupByLibrary.simpleMessage("إظهار في القائمة"),
    "showLogo": MessageLookupByLibrary.simpleMessage(" إظهار اللوجو"),
    "showPricing": MessageLookupByLibrary.simpleMessage("عرض التسعير"),
    "sixMonths": MessageLookupByLibrary.simpleMessage("6 أشهر"),
    "size": MessageLookupByLibrary.simpleMessage("الحجم"),
    "sizeAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "هذا الحجم موجود بالفعل",
    ),
    "sizeStockIInventoryIsOff": MessageLookupByLibrary.simpleMessage(
      "مخزون الحجم مغلق",
    ),
    "sizes": MessageLookupByLibrary.simpleMessage("الأحجام"),
    "sizesAndColors": MessageLookupByLibrary.simpleMessage("الأحجام و الألوان"),
    "small": MessageLookupByLibrary.simpleMessage("صغير"),
    "socialLinks": MessageLookupByLibrary.simpleMessage(
      "روابط التواصل & نبذة عنا",
    ),
    "socialLinksHint": MessageLookupByLibrary.simpleMessage(
      "إذا قمت بملء أي من هذه الحقول، فسوف تكون مرئية لعملائك. إذا تركت أيًا منها فارغًا، فلن يتم عرض تلك الروابط المحددة.",
    ),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage("حدث خطأ ما"),
    "sortCategories": MessageLookupByLibrary.simpleMessage("ترتيب الاقسام"),
    "sortCategoriesDescription": MessageLookupByLibrary.simpleMessage(
      "امسك واسحب القسم لتغيير موقعها. رتبها بالطريقة التي تفضلها.",
    ),
    "sortMainCategories": MessageLookupByLibrary.simpleMessage(
      "ترتيب الأقسام الرئيسية",
    ),
    "sortMainCategoriesDescription": MessageLookupByLibrary.simpleMessage(
      "امسك واسحب القسم الرئيسي لتغيير موقعه. رتبها بالطريقة التي تفضلها.",
    ),
    "sortProducts": MessageLookupByLibrary.simpleMessage("ترتيب المنتجات"),
    "sortProductsDescription": MessageLookupByLibrary.simpleMessage(
      "امسك واسحب المنتج لتغيير موقعه. رتبها بالطريقة التي تفضلها.",
    ),
    "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البدء"),
    "start_exploring": MessageLookupByLibrary.simpleMessage("ابدأ الاستكشاف"),
    "state": MessageLookupByLibrary.simpleMessage("المحافظة"),
    "stepsToCreateYourFreeWebSite": MessageLookupByLibrary.simpleMessage(
      "خطوات إنشاء موقعك المجانى",
    ),
    "stepsToCreateYourWebSite": MessageLookupByLibrary.simpleMessage(
      "خطوات إنشاء موقعك",
    ),
    "stock": MessageLookupByLibrary.simpleMessage("المخزون"),
    "store": MessageLookupByLibrary.simpleMessage("المتجر"),
    "storeActive": MessageLookupByLibrary.simpleMessage("المتجر نشط"),
    "storeAppearance": MessageLookupByLibrary.simpleMessage("مظهر المتجر"),
    "storeInvoices": MessageLookupByLibrary.simpleMessage("فواتير المتجر"),
    "streetName": MessageLookupByLibrary.simpleMessage("اسم الشارع"),
    "subCategories": MessageLookupByLibrary.simpleMessage("الأقسام الفرعية"),
    "subscribe": MessageLookupByLibrary.simpleMessage("اشتراك"),
    "subscribeNow": MessageLookupByLibrary.simpleMessage("اشترك الأن"),
    "subscriptionPlan": MessageLookupByLibrary.simpleMessage("خطة الاشتراك"),
    "subscriptionRequestApprovedSuccessfully":
        MessageLookupByLibrary.simpleMessage("تمت الموافقة على الطلب بنجاح"),
    "subscriptionRequests": MessageLookupByLibrary.simpleMessage(
      "طلبات الاشتراك",
    ),
    "subscriptionType": MessageLookupByLibrary.simpleMessage("نوع الاشتراك"),
    "subtotal": MessageLookupByLibrary.simpleMessage("المجموع الفرعي"),
    "suggestions": MessageLookupByLibrary.simpleMessage("اقتراحات"),
    "suggestionsAreas": MessageLookupByLibrary.simpleMessage(
      "المناطق المقترحة",
    ),
    "swip_left_the_notification_to_delete_or_read__unread":
        MessageLookupByLibrary.simpleMessage(
          "اسحب الإشعار لليسار لحذفه أو قراءته / عدم قراءته",
        ),
    "system": MessageLookupByLibrary.simpleMessage("النظام"),
    "tapBackAgainToLeave": MessageLookupByLibrary.simpleMessage(
      "انقر مرة أخرى للخروج",
    ),
    "taskDone": MessageLookupByLibrary.simpleMessage("منتهية"),
    "tasks": MessageLookupByLibrary.simpleMessage("المهام"),
    "tax": MessageLookupByLibrary.simpleMessage("الضريبة"),
    "template": MessageLookupByLibrary.simpleMessage("قالب"),
    "termsAndCondition": MessageLookupByLibrary.simpleMessage(
      "الشروط و الأحكام",
    ),
    "theTemplate": MessageLookupByLibrary.simpleMessage("القالب"),
    "the_product_was_removed_from_your_cart":
        MessageLookupByLibrary.simpleMessage(
          "تمت إزالة المنتج \$productName من سلة التسوق الخاصة بك",
        ),
    "themeAndLanguageSettings": MessageLookupByLibrary.simpleMessage(
      "إعدادات المظهر و اللغة",
    ),
    "thisAccountNotExist": MessageLookupByLibrary.simpleMessage(
      "هذا الحساب غير موجود",
    ),
    "thisProductIsDeleted": MessageLookupByLibrary.simpleMessage(
      "هذا المنتج محذوف",
    ),
    "threeMonths": MessageLookupByLibrary.simpleMessage("3 أشهر"),
    "tiktok": MessageLookupByLibrary.simpleMessage("تيكتوك"),
    "title": MessageLookupByLibrary.simpleMessage("العنوان"),
    "to": MessageLookupByLibrary.simpleMessage("إلى"),
    "today": MessageLookupByLibrary.simpleMessage("اليوم"),
    "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
    "totalEarning": MessageLookupByLibrary.simpleMessage("الأرباح الكلية"),
    "totalOrders": MessageLookupByLibrary.simpleMessage("إجمالي الطلبات"),
    "totalOrdersHome": MessageLookupByLibrary.simpleMessage("إجمالي\nالطلبات"),
    "totalPrice": MessageLookupByLibrary.simpleMessage("الإجمالي:"),
    "totalProducts": MessageLookupByLibrary.simpleMessage("إجمالي المنتجات"),
    "totalProfit": MessageLookupByLibrary.simpleMessage("إجمالي\nالأرباح"),
    "totalTasks": MessageLookupByLibrary.simpleMessage("إجمالي المهام"),
    "tracking_order": MessageLookupByLibrary.simpleMessage("تتبع الطلب"),
    "tracking_refreshed_successfuly": MessageLookupByLibrary.simpleMessage(
      "تم تحديث التتبع بنجاح",
    ),
    "uncompleted": MessageLookupByLibrary.simpleMessage("الغير مكتمل"),
    "unknown": MessageLookupByLibrary.simpleMessage("غير معروف"),
    "unlimited": MessageLookupByLibrary.simpleMessage("غير محدود"),
    "unpaid": MessageLookupByLibrary.simpleMessage("غير مدفوعة"),
    "updateSelected": MessageLookupByLibrary.simpleMessage("تحديث المختارة"),
    "updatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التعديل بنجاح",
    ),
    "userName": MessageLookupByLibrary.simpleMessage("باسم المستخدم"),
    "userPhone": MessageLookupByLibrary.simpleMessage("بهاتف المستخدم"),
    "users": MessageLookupByLibrary.simpleMessage("العملاء"),
    "usersOrders": MessageLookupByLibrary.simpleMessage("طلبات المستخدمين"),
    "vendorDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف البائع بنجاح",
    ),
    "vendorStatus": MessageLookupByLibrary.simpleMessage("حالة البائع"),
    "vendors": MessageLookupByLibrary.simpleMessage("البائعين"),
    "verify_your_internet_connection": MessageLookupByLibrary.simpleMessage(
      "تحقق من اتصالك بالإنترنت",
    ),
    "version": MessageLookupByLibrary.simpleMessage("النسخة"),
    "view": MessageLookupByLibrary.simpleMessage("عرض"),
    "viewAttachment": MessageLookupByLibrary.simpleMessage("إظهار صورة الدفع"),
    "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
    "visitWebsite": MessageLookupByLibrary.simpleMessage("زيارة الموقع"),
    "waitingPayment": MessageLookupByLibrary.simpleMessage(
      "في انتظار الدفع...",
    ),
    "webLinkMessage": MessageLookupByLibrary.simpleMessage(
      "مرحباً بكم في IDEA2APP - يمكنك رؤية موقع الويب الخاص بك من خلال هذا الرابط أدناه.\nنوصي بإضافة منتجاتك أولاً لرؤية موقع الويب الخاص بك بأفضل شكل ممكن",
    ),
    "website": MessageLookupByLibrary.simpleMessage("الموقع الإلكتروني"),
    "websiteName": MessageLookupByLibrary.simpleMessage("اسم الموقع"),
    "websiteNameAlreadyExist": MessageLookupByLibrary.simpleMessage(
      "اسم الموقع موجود بالفعل",
    ),
    "welcome": MessageLookupByLibrary.simpleMessage("مرحبًا"),
    "whatsApp": MessageLookupByLibrary.simpleMessage("واتساب"),
    "would_you_please_confirm_if_you_have_delivered_all_meals":
        MessageLookupByLibrary.simpleMessage(
          "هل يمكنك تأكيد تسليم جميع الوجبات للعميل؟",
        ),
    "wrong_email_or_password": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني أو كلمة المرور غير صحيحة",
    ),
    "youCanNotChangeThePriceOfTheProductBecauseItIsSetByTheSizePrice":
        MessageLookupByLibrary.simpleMessage(
          "لا يمكنك تغيير سعر المنتج لأنه محدد بسعر الحجم",
        ),
    "youHaveNewOrder": MessageLookupByLibrary.simpleMessage("لديك طلب جديد"),
    "youHaveNewOrderPleaseCheckYourLatestOrders":
        MessageLookupByLibrary.simpleMessage(
          "لديك طلب جديد، من فضلك تحقق من طلباتك الأخيرة !",
        ),
    "youMayNeedToReInstallTheAppToApplyChanges":
        MessageLookupByLibrary.simpleMessage(
          "قد تحتاج إلى إعادة تثبيت التطبيق لتطبيق التغييرات",
        ),
    "youMust": MessageLookupByLibrary.simpleMessage("يجب عليك "),
    "youMustAddAtLeastOneName": MessageLookupByLibrary.simpleMessage(
      "يجب عليك إضافة اسم واحد على الأقل من العربي أو الإنجليزي",
    ),
    "youMustAddOneOfThoseDescriptions": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة وصف إما باللغة العربية أو الإنجليزية.",
    ),
    "youMustAddOneOfThoseNames": MessageLookupByLibrary.simpleMessage(
      "يجب عليك إضافة أحد هذه الأسماء",
    ),
    "youMustAddOneOfThoseTitles": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة عنوان إما باللغة العربية أو الإنجليزية.",
    ),
    "youMustAddStockForColorOrSizes": MessageLookupByLibrary.simpleMessage(
      "يجب عليك إضافة مخزون\nللألوان أو الأحجام",
    ),
    "youMustButCostForAllAreasYouSelected":
        MessageLookupByLibrary.simpleMessage(
          "يجب عليك إضافة تكلفة لجميع المناطق التي اخترتها",
        ),
    "youMustTurnOffInventoryFirst": MessageLookupByLibrary.simpleMessage(
      "يجب عليك إغلاق المخزون أولاً",
    ),
    "youMustTurnOnInventoryFirst": MessageLookupByLibrary.simpleMessage(
      "يجب عليك فتح المخزون أولاً",
    ),
    "you_dont_have_any_order_assigned_to_you":
        MessageLookupByLibrary.simpleMessage("ليس لديك أي طلب معين لك!"),
    "yourAccountIsNotActive": MessageLookupByLibrary.simpleMessage(
      "حسابك غير نشط",
    ),
    "yourAccountIsNotActivePleaseRenewYourSubscription":
        MessageLookupByLibrary.simpleMessage(
          "حسابك غير نشط، من فضلك قم بتجديد اشتراكك !",
        ),
    "yourDeliveryCostHasBeenChanged": MessageLookupByLibrary.simpleMessage(
      "تم تغيير تكلفة التوصيل الخاصة بك",
    ),
    "yourOrderStatusHasBeenChanged": MessageLookupByLibrary.simpleMessage(
      "تم تغيير حالة الطلب الخاص بك",
    ),
    "yourRequestSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال طلبك بنجاح وهو قيد المراجعة حالياً. سوف نوافيك بالرد في أقرب وقت ممكن.",
    ),
    "yourSettings": MessageLookupByLibrary.simpleMessage("الاعدادات الخاصة بك"),
    "yourSubscriptionHasExpired": MessageLookupByLibrary.simpleMessage(
      "انتهت مدة اشتراكك",
    ),
    "your_address": MessageLookupByLibrary.simpleMessage("عنوانك"),
    "your_biography": MessageLookupByLibrary.simpleMessage("سيرتك الذاتية"),
    "your_have_an_order_assigned_to_you": MessageLookupByLibrary.simpleMessage(
      "لديك طلب معين لك",
    ),
    "youtube": MessageLookupByLibrary.simpleMessage("يوتيوب"),
  };
}
