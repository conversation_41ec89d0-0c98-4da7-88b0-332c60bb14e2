// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCDU9UxRNumCG3tfk29NyMsqPtWdDhKcvM',
    appId: '1:163437334343:web:0b88f7c6e780da38b98662',
    messagingSenderId: '163437334343',
    projectId: 'idea2app-dev',
    authDomain: 'idea2app-dev.firebaseapp.com',
    storageBucket: 'idea2app-dev.appspot.com',
    measurementId: 'G-68DYQLDMXN',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC8A_w07iRJkt_vy_3HSRsPm_vUWzgdwFU',
    appId: '1:163437334343:android:68ef6716186a0d59b98662',
    messagingSenderId: '163437334343',
    projectId: 'idea2app-dev',
    storageBucket: 'idea2app-dev.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCyHenGYinzdjEHNvozczxLWfXywldoNkI',
    appId: '1:163437334343:ios:ba8a427ff9c54886b98662',
    messagingSenderId: '163437334343',
    projectId: 'idea2app-dev',
    storageBucket: 'idea2app-dev.appspot.com',
    iosBundleId: 'com.dashboard.idea2appVendorApp',
  );
}
