import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/admin/orders/admin_vendor_orders_screen.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view_model/vendors_view_model.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view/banner_screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/home_category_screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/expenses/view/expenses_screen.dart';
import 'package:idea2app_vendor_app/src/screens/reports/reports_screen.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/store_screen.dart';
import 'package:idea2app_vendor_app/src/screens/tasks/view/tasks_screen.dart';
import 'package:idea2app_vendor_app/src/screens/users/view/users_screen.dart';
import 'package:provider/provider.dart';

import '../vendors_screen/vendors_screen.dart';

class VendorDetailsScreen extends HookWidget {
  const VendorDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Log.w(
        "start Date ${currentAdminVendor?.startDate} end Date ${currentAdminVendor?.expireDate}");
    final vendorsVM = context.read<VendorsVM>();
    final watchVendorVM = context.watch<VendorsVM>();

    const iconColor = ColorManager.primaryColor;

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        vendorsVM.getVendorOrdersCount(context);
      });

      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        title: currentAdminVendor?.name,
        haveBackButton: true,
      ),
      body: ListView(
        children: [
          ListTile(
            onTap: () => context.to(const AdminVendorOrdersScreen()),
            leading: SvgPicture.asset(Assets.ordersSvg,
                color: iconColor, height: 25.h),
            title: Text(context.tr.orders, style: context.title),
            trailing: Text(
              '${watchVendorVM.ordersCount}/${currentAdminVendor?.pricing?.ordersCount ?? '150'}',
              style: context.title,
            ),
          ),
          ListTile(
            onTap: () => context.to(const ReportsScreen()),
            leading:
                const IconWidget(icon: Assets.iconsAnalytics, color: iconColor),
            title: Text(context.tr.reports, style: context.title),
          ),
          ListTile(
            onTap: () {
              context.read<CategoryVM>().clearCategories();
              context.to(const StoreScreen());
            },
            leading: const Icon(
              Icons.store_mall_directory_rounded,
              color: iconColor,
            ),
            title: Text(context.tr.store, style: context.title),
          ),
          ListTile(
            onTap: () => context.to(const HomeCategoriesScreen()),
            leading:
                const IconWidget(icon: Assets.categoryNav, color: iconColor),
            title: Text(context.tr.categories, style: context.title),
          ),
          ListTile(
            onTap: () => context.to(const BannerScreen()),
            leading: const IconWidget(icon: Assets.iconsAds, color: iconColor),
            title: Text(context.tr.banners, style: context.title),
          ),
          ListTile(
            onTap: () => context.to(const UsersScreen()),
            leading: const IconWidget(
                icon: Assets.iconsUserCircle, color: iconColor),
            title: Text(context.tr.users, style: context.title),
          ),
          ListTile(
            onTap: () => context.to(const ExpensesScreen()),
            leading:
                const IconWidget(icon: Assets.iconsMoney, color: iconColor),
            title: Text(context.tr.expenses, style: context.title),
          ),
          ListTile(
            onTap: () => context.to(const TasksScreen()),
            leading:
                const IconWidget(icon: Assets.iconsTasks, color: iconColor),
            title: Text(context.tr.tasks, style: context.title),
          ),
        ],
      ),
    );
  }
}
