import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/send_vendor_notification_dialog.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../../core/resources/theme/theme.dart';
import '../../../../../../../core/shared_widgets/dialogs/show_dialog.dart';
import '../../../../../../../screens/categories/view/categories/widgets/category_search_header.dart';

class VendorCardBottomSection extends StatelessWidget {
  final VendorModel vendor;

  const VendorCardBottomSection({super.key, required this.vendor});

  @override
  Widget build(BuildContext context) {
    final isLoadingAuth = context.watch<AuthVM>().isLoading;

    return Row(
      children: [
        Expanded(
          child: categoriesScreenButtonWidget(
            context,
            icon: SizedBox(),
            text: context.tr.delete,
            onPressed: () {
              showAlertDialog(
                  isLoading: isLoadingAuth,
                  context,
                  child: Text(
                      '${context.tr.areYouSureYouWantToDeleteThisVendor} ${vendor.name}',
                      style: context.labelLarge), onConfirm: () {
                currentAdminVendor = vendor;
                context.read<AuthVM>().adminDeleteVendor(
                      context,
                      vendor: vendor,
                    );
                context.back();
              });
            },
          ),
        ),
        context.mediumGap,
        Expanded(
          child: categoriesScreenButtonWidget(
            context,
            icon: SizedBox(),
            text: context.tr.sendNotification,
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) {
                  return SendVendorNotificationDialog(
                      topic: vendor.businessName.toString());
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
