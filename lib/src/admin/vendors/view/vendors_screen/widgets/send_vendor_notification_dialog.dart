import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view_model/vendors_view_model.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:provider/provider.dart';

class SendVendorNotificationDialog extends HookWidget {
  final String topic;

  const SendVendorNotificationDialog({
    super.key,
    required this.topic,
  });

  @override
  Widget build(BuildContext context) {
    final titleController = useTextEditingController();
    final descriptionController = useTextEditingController();
    final formKey = useState(GlobalKey<FormState>());

    return Consumer<VendorsVM>(
      builder: (context, vendorVM, child) {
        Future<void> sendNotification() async {
          if (!formKey.value.currentState!.validate()) return;
          await vendorVM.sendNotification(context,
              title: titleController.text,
              body: descriptionController.text,
              topic: topic);
        }

        return BaseDialog(
            withCloseButton: true,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
              child: Form(
                key: formKey.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // * Header ===========================
                    Text(
                      context.tr.sendNotification,
                      style: context.subHeadLine,
                    ),

                    context.largeGap,
                    // * Title ===========================
                    BaseTextField(
                      title: context.tr.title,
                      controller: titleController,
                      hint: context.tr.title,
                    ),
                    context.largeGap,

                    // * Description ===========================
                    BaseTextField(
                      title: context.tr.description,
                      hint: context.tr.description,
                      maxLines: 4,
                      radius: AppRadius.longFieldRadius,
                      controller: descriptionController,
                    ),

                    context.largeGap,

                    // * Add Product Button ========================
                    Button(
                      isLoading: vendorVM.isLoading,
                      isOutLine: vendorVM.isLoading,
                      label: context.tr.send,
                      onPressed: () async => await sendNotification(),
                      isWhiteText: true,
                    )
                  ],
                ),
              ),
            ));
      },
    );
  }
}
