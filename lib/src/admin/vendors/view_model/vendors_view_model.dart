import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/repository/vendors_repository.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/repository/auth_remote_repo.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';

import '../../../core/extensions/extensions.dart';
import '../../../screens/auth/models/helper_models/pricing_model.dart';

class VendorsVM extends BaseVM {
  final VendorsRepository vendorRepository;
  final AuthRemoteRepository authRemoteRepository;

  VendorsVM({
    required this.vendorRepository,
    required this.authRemoteRepository,
  });

  var vendors = <VendorModel>[];
  var pricingList = <PricingModel>[];

  List<PricingModel> get oneMonthPricingList => pricingList
      .where((element) => element.name!.contains('Monthly'))
      .toList();

  List<PricingModel> get threeMonthsPricingList => pricingList
      .where((element) => element.name!.contains('3 Month'))
      .toList();

  List<PricingModel> get oneTimePricingList => pricingList
      .where((element) => element.name!.contains('One Time Pricing'))
      .toList();

  int ordersCount = 0;

  Future<List<VendorModel>> getVendors(BuildContext context) async {
    return await baseFunction(context, () async {
      vendors = await vendorRepository.getVendors();

      return vendors;
    });
  }

  //? Edit Vendor
  Future<void> editVendor(
    BuildContext context, {
    required Map<String, TextEditingController> controllers,
    required Map<String, ValueNotifier> valueNotifiers,
    required String pickedImage,
    required VendorModel? vendor,
  }) async {
    return await baseFunction(context, () async {
      final updatedVendor = _setVendorModel(
        controllers: controllers,
        vendor: vendor,
        valueNotifiers: valueNotifiers,
      );

      await authRemoteRepository.editVendor(
        vendor: updatedVendor,
        pickedImage: pickedImage,
        pricingDocId:
            (valueNotifiers[ApiStrings.pricing]?.value as PricingModel?)
                ?.documentId,
      );
    },
        type: FlushBarType.update,
        additionalFunction: (_) =>
            context.toReplacement(const VendorsScreen()));
  }

  //? Add Vendor
  Future<void> addVendor(
    BuildContext context, {
    required Map<String, TextEditingController> controllers,
    required Map<String, ValueNotifier> valueNotifiers,
    required String pickedImage,
  }) async {
    return await baseFunction(context, () async {
      final vendor = _setVendorModel(
        controllers: controllers,
        valueNotifiers: valueNotifiers,
      );

      await authRemoteRepository.registerVendor(
        vendor: vendor,
        pickedImage: pickedImage,
        pricingId:
            (valueNotifiers[ApiStrings.pricing]?.value as PricingModel?)?.id,
      );
    },
        type: FlushBarType.add,
        additionalFunction: (_) =>
            context.toReplacement(const VendorsScreen()));
  }

  //? Set Vendor Model
  VendorModel _setVendorModel({
    VendorModel? vendor,
    required Map<String, TextEditingController> controllers,
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final updatedVendor = VendorModel(
      documentId: vendor?.documentId,
      name: controllers[ApiStrings.name]!.text,
      password: controllers[ApiStrings.password]!.text,
      email: controllers[ApiStrings.email]!.text,
      phone: controllers[ApiStrings.phone]!.text,
      address: controllers[ApiStrings.address]!.text,
      businessType:
          (valueNotifiers[ApiStrings.businessType]?.value as TemplateModel?)
                  ?.name ??
              '',
      pricing: valueNotifiers[ApiStrings.pricing]!.value,
      isActive: valueNotifiers[ApiStrings.isActive]!.value,
      businessName: controllers[ApiStrings.businessName]!.text,
      startDate: valueNotifiers[ApiStrings.startDate]!.value,
      expireDate: valueNotifiers[ApiStrings.expireDate]!.value,
      vendorType: valueNotifiers[ApiStrings.vendorType]!.value,
      websiteLink: controllers[ApiStrings.websiteLink]!.text,
      paidAmount: num.tryParse(controllers[ApiStrings.paidAmount]?.text ?? '0'),
    );
    // final updatedConfig = vendor?.config?.copyWith(
    //   showPricing: valueNotifiers[ApiStrings.showPricing]!.value,
    // );
    //
    // final copiedVendor =  vendor != null ? updatedVendor.copyWith(
    //   config: updatedConfig,
    // ) : updatedVendor;

    return updatedVendor;
  }

  Future<void> sendNotification(
    BuildContext context, {
    required String title,
    required String body,
    required String topic,
  }) async {
    return await baseFunction(context, () async {
      await NotificationService.sendNotification(
        title: title,
        body: body,
        userTokenOrTopic: topic,
        isTopic: true,
      );

      if (!context.mounted) return;

      context.back();
      context.showBarMessage(context.tr.sendSuccessfully);
    });
  }

  Future<void> getVendorOrdersCount(BuildContext context) async {
    return await baseFunction(context, () async {
      ordersCount = await vendorRepository.getVendorOrdersCount();
    });
  }

  Future<List<PricingModel>> getPricingList(BuildContext context) async {
    return await baseFunction(context, () async {
      pricingList = await vendorRepository.getPricingList();

      return pricingList;
    }, isLoading: false);
  }
}
