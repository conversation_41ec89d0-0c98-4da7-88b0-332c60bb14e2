import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/notification/model/notification_model.dart';
import 'package:provider/provider.dart';

import '../view_model/notification_view_model.dart';

class NotificationScreen extends HookWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final notificationVM = context.read<NotificationVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notificationVM.setReadNotifications();
      });

      return () {};
    }, []);
    return Consumer<NotificationVM>(
      builder: (context, notificationViewModel, child) {
        if (notificationViewModel.isLoading) {
          return const LoadingWidget();
        }
        return Scaffold(
          appBar: MainAppBar(
            haveBackButton: true,
            title: context.tr.notifications,
          ),
          body: notificationVM.notifications.isEmpty
              ? const Center(
                  child: EmptyDataWidget(),
                )
              : ListView.separated(
                  itemBuilder: (context, index) => _NotificationCard(
                        notification:
                            notificationViewModel.notifications[index],
                      ),
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemCount: notificationViewModel.notifications.length),
        );
      },
    );
  }
}

class _NotificationCard extends StatelessWidget {
  final NotificationModel notification;

  const _NotificationCard({required this.notification});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      width: double.infinity,
      color: context.appTheme.cardColor,
      child: Row(
        children: [
          //! Circle & notification icon
          Container(
              margin: const EdgeInsets.only(right: AppSpaces.mediumPadding),
              height: 55.r,
              width: 55.r,
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                      begin: Alignment.bottomLeft,
                      end: Alignment.topRight,
                      colors: context.isDark
                          ? ColorManager.darkGradientBackground
                          : ColorManager.gradientBackground)),
              child: const Icon(
                Icons.notifications,
                color: ColorManager.white,
                size: 40,
              )),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                //! New order from costumer
                Text(
                  notification.title,
                  style: context.labelLarge,
                ),

                context.xSmallGap,

                //! Date & time
                Text(
                  notification.body,
                  style: context.greyLabelMedium,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
