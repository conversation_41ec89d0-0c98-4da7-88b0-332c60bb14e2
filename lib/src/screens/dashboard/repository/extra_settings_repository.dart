import 'dart:async';
import 'dart:io';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';

import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../models/extra_setting_model.dart';

class ExtraSettingsRepository {
  final BaseApiServices networkApiServices;

  ExtraSettingsRepository({required this.networkApiServices});

  //! Get extraSettings ====================================
  Future<MainExtraSettingsModel> getExtraSettings() async {
    try {
      dynamic response =
          await networkApiServices.getResponse(ApiEndPoints.configs);

      final data = response != null && response.isNotEmpty ? response[0] : {};

      final extraSettingData = MainExtraSettingsModel.fromJson(data);

      return extraSettingData;
    } catch (error) {
      rethrow;
    }
  }

  //! add extraSetting ====================================
  Future<void> updateExtraSetting(
      {required MainExtraSettingsModel extraSettings}) async {
    try {
      final data = await networkApiServices.putResponse(ApiEndPoints.configs,
          data: extraSettings.toJson());

      final documentId = data[ApiStrings.documentId] as String?;

      Log.w("extra toJson ${extraSettings.toJson()}");

      await networkApiServices.connectRelation(data: {
        "config": {
          "set": [documentId],
        }
      });
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
