import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

import '../../../../core/resources/theme/theme.dart';

class ExtraSettingsTabBar extends HookWidget {
  final ValueNotifier<int> currentIndex;

  const ExtraSettingsTabBar({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    final List<String> tabs = [
      context.tr.sizes,
      context.tr.colors,
    ];
    return DefaultTabController(
      initialIndex: currentIndex.value,
      length: tabs.length,
      child: TabBar(
        onTap: (index) {
          currentIndex.value = index;
        },
        labelPadding: const EdgeInsets.symmetric(horizontal: 10),
        unselectedLabelColor: ColorManager.primaryColor,
        labelColor: ColorManager.white,
        dividerColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: ColorManager.primaryColor),
        tabs: tabs
            .map((e) => Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      border: Border.all(
                          color: ColorManager.primaryColor.withOpacity(0.2),
                          width: 1)),
                  child: Align(
                    alignment: Alignment.center,
                    child: Text(e),
                  ),
                ))
            .toList(),
      ),
    );
  }
}
