import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/widgets/sizes/size_card.dart';
import 'package:provider/provider.dart';

import '../../../view_model/extra_settings_view_model.dart';

class SizesList extends StatelessWidget {
  const SizesList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ExtraSettingVM>(
      builder: (context, extraSettingsVM, child) {
        if (extraSettingsVM.isLoading) {
          return const Expanded(child: LoadingWidget());
        }

        if (extraSettingsVM.extraSettings != null &&
            (extraSettingsVM.extraSettings!.sizes.isEmpty ||
                extraSettingsVM.extraSettings!.sizes
                    .every((element) => element.categories.isEmpty))) {
          return Expanded(
            child: EmptyDataWidget(
              message: context.tr.noSizesAdded,
            ),
          );
        }

        return Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.only(
                top: AppSpaces.smallPadding,
                bottom: AppSpaces.xlLargePadding * 1.5),
            itemCount: extraSettingsVM.allCategories.length,
            itemBuilder: (context, index) {
              final categorySizes =
                  extraSettingsVM.filterCategoriesSizes(index: index);

              return SizeCard(
                cat: extraSettingsVM.allCategories[index],
                sizes: categorySizes,
              );
            },
          ),
        );
      },
    );
  }
}
