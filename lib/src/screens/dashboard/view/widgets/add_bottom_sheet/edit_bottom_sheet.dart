import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';

class EditSizeBottomSheet extends HookWidget {
  final TextEditingController sizeControllerEn;
  final TextEditingController sizeControllerAr;

  const EditSizeBottomSheet(
      {super.key,
      required this.sizeControllerEn,
      required this.sizeControllerAr});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      child: Row(
        children: [
          Expanded(
            child: BaseTextField(
              title: context.tr.englishName,
              controller: sizeControllerEn,
            ),
          ),
          context.smallGap,
          Expanded(
            child: BaseText<PERSON>ield(
              title: context.tr.arabic<PERSON><PERSON>,
              controller: sizeControllerAr,
            ),
          ),
        ],
      ),
    );
  }
}
