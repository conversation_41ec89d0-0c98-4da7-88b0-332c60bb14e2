import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:provider/provider.dart';

import '../../models/extra_setting_model.dart';
import '../../view_model/extra_settings_view_model.dart';
import 'add_bottom_sheet/add_bottom_sheet.dart';

class ExtraSettingsFloatingButton extends HookWidget {
  final int currentIndex;

  const ExtraSettingsFloatingButton({
    super.key,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    final isOpen = useState(false);
    final pickerColor = useState<Color>(ColorManager.primaryColor);

    final extraSettingsVM = context.read<ExtraSettingVM>();

    void changeColor() {
      final oldColors = extraSettingsVM.extraSettings!.colors;

      final pickerColorString = pickerColor.value.toHexWithoutHash();

      if (pickerColorString != null) {
        final ExtraSettingsModel extraSetting = ExtraSettingsModel(
          englishName: pickerColorString,
        );

        final checkIfExist =
            oldColors?.map((e) => e.englishName).contains(pickerColorString);

        if (checkIfExist ?? false) {
          context.showBarMessage(context.tr.colorAlreadyExist, isError: true);
          return;
        }

        final extraSettings = extraSettingsVM.extraSettings!
            .copyWith(colors: [...?oldColors, extraSetting]);

        extraSettingsVM.addExtraSetting(context, extraSettings: extraSettings);
        return;
      }
    }

    return FloatingActionButton(
      elevation: 0,
      backgroundColor: ColorManager.primaryColor,
      shape: const CircleBorder(),
      child: Icon(
        isOpen.value ? Icons.close : Icons.add,
        color: ColorManager.white,
      ),
      onPressed: () {
        if (currentIndex == 1) {
          showDialog(
              context: context,
              builder: (_) => AlertDialog(
                    title: Text(context.tr.pickAColor),
                    content: SingleChildScrollView(
                      child: MaterialPicker(
                        pickerColor: pickerColor.value,
                        portraitOnly: true,
                        onColorChanged: (color) {
                          pickerColor.value = color;
                        },
                      ),
                    ),
                    actions: <Widget>[
                      ElevatedButton(
                        child: Text(
                          context.tr.confirm,
                          style: context.labelLarge
                              .copyWith(color: ColorManager.primaryColor),
                        ),
                        onPressed: () {
                          context.back();
                          changeColor();
                        },
                      ),
                    ],
                  ));
          return;
        }
        isOpen.value = !isOpen.value;
        if (isOpen.value) {
          showBottomSheet(
              elevation: 2,
              backgroundColor: context.appTheme.cardColor,
              context: context,
              builder: (_) => AddSizeBottomSheet(
                    isColor: currentIndex == 1,
                  )).closed.then((value) => isOpen.value = false);
        } else {
          context.back();
        }
      },
    );
  }
}
