import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/validations.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/about_vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:provider/provider.dart';

class SocialLinksScreen extends HookWidget {
  const SocialLinksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final whatsAppController = useTextEditingController(
        text: VendorModelHelper.currentVendorModel().about?.whatsapp ?? '');
    final facebookController = useTextEditingController(
        text: VendorModelHelper.currentVendorModel().about?.facebookLink ?? '');
    final instagramController = useTextEditingController(
        text:
            VendorModelHelper.currentVendorModel().about?.instagramLink ?? '');
    final tiktokController = useTextEditingController(
        text: VendorModelHelper.currentVendorModel().about?.tiktokLink ?? '');

    final youtubeController = useTextEditingController(
        text: VendorModelHelper.currentVendorModel().about?.youtubeLink ?? '');

    final aboutController = useTextEditingController(
        text: VendorModelHelper.currentVendorModel().about?.about ?? '');
    final privacyController = useTextEditingController(
        text:
            VendorModelHelper.currentVendorModel().about?.privacyPolicy ?? '');

    final formKey = useState(GlobalKey<FormState>());
    return Consumer<AuthVM>(
      builder: (context, authVM, child) {
        return Form(
          key: formKey.value,
          child: Scaffold(
            appBar: MainAppBar(
              title: context.tr.socialLinks,
              haveBackButton: true,
            ),
            body: ListView(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              children: [
                Text(
                  '* ${context.tr.socialLinksHint}',
                  style: context.labelSmall,
                ),
                context.mediumGap,
                BaseTextField(
                  title: context.tr.whatsApp,
                  hint: context.tr.YourWhatsApp,
                  controller: whatsAppController,
                  textInputType: TextInputType.phone,
                  validator: (value) {
                    if (value!.isNotEmpty) {
                      return Validations.phoneNumber(context, value);
                    } else {
                      return null;
                    }
                  },
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.facebook,
                  hint: context.tr.YourFacebookLink,
                  controller: facebookController,
                  textInputType: TextInputType.url,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.instagram,
                  hint: context.tr.YourInstagramLink,
                  controller: instagramController,
                  textInputType: TextInputType.url,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.tiktok,
                  hint: context.tr.YourTiktokLink,
                  controller: tiktokController,
                  textInputType: TextInputType.url,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.youtube,
                  hint: context.tr.YourYoutubeLink,
                  controller: youtubeController,
                  textInputType: TextInputType.url,
                ),
                context.largeGap,
                Text(
                  context.tr.aboutAndPrivacy,
                  style: context.title,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.about,
                  controller: aboutController,
                  textInputType: TextInputType.text,
                  radius: AppRadius.longFieldRadius,
                  maxLines: 10,
                  isRequired: false,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.privacyPolicy,
                  controller: privacyController,
                  textInputType: TextInputType.text,
                  radius: AppRadius.longFieldRadius,
                  maxLines: 10,
                  isRequired: false,
                ),
              ],
            ),
            bottomNavigationBar: Padding(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: Button(
                label: context.tr.save,
                isLoading: authVM.isLoading,
                onPressed: () async {
                  if (!formKey.value.currentState!.validate()) {
                    return;
                  }
                  final about = AboutVendorModel(
                    whatsapp: whatsAppController.text,
                    facebookLink: facebookController.text,
                    instagramLink: instagramController.text,
                    tiktokLink: tiktokController.text,
                    youtubeLink: youtubeController.text,
                    about: aboutController.text,
                    privacyPolicy: privacyController.text,
                  );

                  final vendor = authVM.currentVendor?.copyWith(about: about);

                  await authVM.updateVendorData(context, vendor: vendor!);
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
