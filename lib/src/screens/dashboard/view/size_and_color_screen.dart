import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/widgets/extra_settings_tab_bar.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/view/widgets/sizes/size_list.dart';
import 'package:provider/provider.dart';

import '../../../core/shared_widgets/appbar/main_appbar.dart';
import '../view_model/extra_settings_view_model.dart';
import 'widgets/add_floating_button.dart';
import 'widgets/colors/colors_list.dart';

class SizeAndColorScreen extends HookWidget {
  final int selectedTabIndex;

  const SizeAndColorScreen({
    super.key,
    this.selectedTabIndex = 0,
  });

  @override
  Widget build(BuildContext context) {
    final catVM = context.read<CategoryVM>();
    final extraSettings = context.read<ExtraSettingVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        catVM.getCategories(context);
        extraSettings.getExtraSettings(context);
      });
      return () {};
    }, []);

    final currentIndex = useState(selectedTabIndex);

    return RefreshIndicatorWidget(
      onRefresh: () async {
        await catVM.getCategories(context);
        await extraSettings.getExtraSettings(context);
      },
      child: Scaffold(
        floatingActionButton: ExtraSettingsFloatingButton(
          currentIndex: currentIndex.value,
        ),
        appBar: MainAppBar(
          title: context.tr.sizesAndColors,
          haveBackButton: true,
          isCenterTitle: false,
        ),
        body: Column(
          children: [
            //! Extra Settings Tab Bar (Sizes, Colors)
            ExtraSettingsTabBar(
              currentIndex: currentIndex,
            ),

            //! Extra Settings List
            _SelectedExtraScreenList(
              currentIndex: currentIndex,
            ),
          ],
        ).paddingAll(AppSpaces.mediumPadding),
      ),
      //.onWillPopMainScreen(context)
    );
  }
}

class _SelectedExtraScreenList extends StatelessWidget {
  final ValueNotifier<int> currentIndex;

  const _SelectedExtraScreenList({required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    if (currentIndex.value == 0) {
      return const SizesList();
    }
    return const ColorsList();
  }
}
