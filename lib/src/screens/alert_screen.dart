import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/login.screen.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';

import 'auth/view_model/auth_view_model.dart';
import 'drawer/widgets/settings_widgets.dart';

class AlertSubscriptionWidget extends StatelessWidget {
  const AlertSubscriptionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        title: Text(context.tr.log_out, style: context.labelLarge),
        leading: IconButton(
          icon: Icon(Icons.logout, color: ColorManager.primaryColor),
          onPressed: () {
            context.read<AuthVM>().logout(context);
            context.toReplacement(const LoginScreen());
          },
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(child: Lottie.asset(Assets.animatedAlert, width: 200.w)),
          Text(
            context.tr.yourSubscriptionHasExpired,
            textAlign: TextAlign.center,
            style: context.headLine,
          ),
          TextButton(
              onPressed: () => context.to(const ContactUsScreen()),
              child: Text(
                context.tr.pleaseContactSupport,
                textAlign: TextAlign.center,
                style: context.subTitle.copyWith(
                    color: ColorManager.primaryColor,
                    decorationThickness: 2,
                    decoration: TextDecoration.underline,
                    decorationColor: ColorManager.primaryColor),
              )),
          context.xLargeGap,
        ],
      ),
    );
  }
}
