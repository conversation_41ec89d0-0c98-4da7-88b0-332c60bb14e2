import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/products_screen.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/extensions/extensions.dart';
import '../models/products_model.dart';
import '../repository/product_repository.dart';

class ProductVM extends BaseVM {
  ProductRepository productRepository;

  ProductVM(this.productRepository);

  var productsByCategory = <ProductModel>[];
  var allProducts = <ProductModel>[];

  var searchedProducts = <ProductModel>[];
  bool isProductNotEmpty = false;

  String? getProductTitle(
      {required ProductModel product, required BuildContext context}) {
    final productName = context.isEng
        ? (product.englishTitle?.isNotEmpty == true
            ? product.englishTitle!
            : product.arabicTitle ?? '')
        : (product.arabicTitle?.isNotEmpty == true
            ? product.arabicTitle!
            : product.englishTitle ?? '');

    return productName;
  }

  int _allProductColorsStock(List<ExtraSettingsModel> colors) => colors.fold(
      0, (previousValue, element) => previousValue + (element.stock ?? 0));

  int _allProductSizesStock(List<ExtraSettingsModel> sizes) => sizes.fold(
      0, (previousValue, element) => previousValue + (element.stock ?? 0));

  int totalProductStock(
      {required List<ExtraSettingsModel> colors,
      required List<ExtraSettingsModel> sizes}) {
    final colorsStock = _allProductColorsStock(colors);
    final sizesStock = _allProductSizesStock(sizes);

    return colorsStock + sizesStock;
  }

  bool productHaveAnyColorOrSizePriceOrStockNotNull({
    required List<ExtraSettingsModel> colors,
    required List<ExtraSettingsModel> sizes,
  }) {
    final hasNonNullColorPriceOrStock =
        colors.any((color) => color.stock != null);

    final hasNonNullSizePriceOrStock = sizes.any((size) => size.stock != null);

    return hasNonNullColorPriceOrStock || hasNonNullSizePriceOrStock;
  }

  //! Get All products  ====================================
  Future<List<ProductModel>> getAllProducts(
    BuildContext context, {
    String? query,
  }) async {
    await baseFunction(context, () async {
      allProducts = await productRepository.getAllProducts();

      searchedProducts = allProducts;

      if (query != null && query.isNotEmpty) {
        filterSearchedList(query: query, isAll: true, context: context);
      }
    });

    return allProducts;
  }

  // ! Get Product Count ====================================

  Future<bool> isOneProductExist(BuildContext context) async {
    return await baseFunction(
      context,
      () async {
        isProductNotEmpty = await productRepository.getProductCount();

        Log.w("VMMMMMM isProductNotEmpty: $isProductNotEmpty");

        return isProductNotEmpty;
      },
    );
  }

  void filterSearchedList(
      {required String query,
      required bool isAll,
      required BuildContext context}) {
    if (isAll) {
      searchedProducts = allProducts.where((element) {
        final title = element.nameByLang(context);
        return title.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } else {
      searchedProducts = productsByCategory.where((element) {
        final title = element.nameByLang(context);
        return title.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }

    notifyListeners();
  }

  //! Get products By Category ====================================
  Future<void> getProductsByCategory(BuildContext context,
      {required String catId, String? query}) async {
    await baseFunction(context, () async {
      productsByCategory =
          await productRepository.getProductsByCategory(catId: catId);

      searchedProducts = productsByCategory;

      if (query != null && query.isNotEmpty) {
        filterSearchedList(query: query, isAll: false, context: context);
      }
    });
  }

  //! Add product ====================================
  Future<void> addProduct(BuildContext context,
      {required (
        Map<String, TextEditingController> controllers,
        Map<String, ValueNotifier> valueNotifiers
      ) fields,
      required List<String> pickedImage,
      required CategoryModel category}) async {
    await baseFunction(
        context,
        () async {
          final product = _setProductModel(fields: fields, category: category);

          await productRepository.addProduct(
              product: product, fileResult: pickedImage);
        },
        type: FlushBarType.add,
        additionalFunction: (_) async {
          await getProductsByCategory(context, catId: category.documentId!);
          if (pickedImage.isNotEmpty && context.mounted) {
            context.read<MediaVM>().clearFiles();
          }
        });
  }

  //! Edit product ============================================
  Future<void> editProduct(BuildContext context,
      {ProductModel? productModel,
      required (
        Map<String, TextEditingController> controllers,
        Map<String, ValueNotifier> valueNotifiers,
      ) fields,
      required List<String>? fileResult,
      required List<BaseMediaModel>? images,
      required String documentId,
      required CategoryModel category}) async {
    baseFunction(
        context,
        () async {
          final product = _setProductModel(
                  fields: fields, category: category, product: productModel)
              .copyWith(documentId: documentId, images: images);

          await productRepository.editProduct(
              product: product, fileResult: fileResult);
        },
        type: FlushBarType.update,
        additionalFunction: (_) async {
          await getProductsByCategory(context, catId: category.documentId!);

          if (fileResult != null && fileResult.isNotEmpty && context.mounted) {
            context.read<MediaVM>().clearFiles();
          }
        });
  }

  //! Update Inventory ============================================
  Future<void> updateInventory(
    BuildContext context, {
    required String productId,
    required int? inventory,
    required List<ExtraSettingsModel?> colors,
    required List<ExtraSettingsModel?> sizes,
  }) async {
    baseFunction(
      context,
      () async {
        await productRepository.updateInventory(
          productId: productId,
          inventory: inventory,
          colors: colors,
          sizes: sizes,
        );
      },
      isLoading: false,
      isBack: false,
    );
  }

  //! Bulk Update Products ============================================
  Future<void> bulkUpdateProducts(
    BuildContext context,
    Map<String, Map<String, dynamic>> bulkUpdateData,
  ) async {
    await baseFunction(
      context,
      () async {
        await productRepository.bulkUpdateProducts(
          bulkUpdateData: bulkUpdateData,
        );
      },
      type: FlushBarType.update,
    );
  }

  //! Edit Product Status ============================================
  Future<void> editStatusProduct(
    BuildContext context, {
    required ProductModel product,
    required bool status,
  }) async {
    baseFunction(context, () async {
      final productModel = product.copyWith(
        isActive: status,
      );

      await productRepository.editProductStatus(product: productModel);
    }, type: FlushBarType.update, isLoading: false, isBack: false);
  }

  //! product fields & value notifier
  ProductModel _setProductModel({
    ProductModel? product,
    required fields,
    required category,
  }) {
    final controllers = fields.$1;
    final valueNotifiers = fields.$2;

    final colors =
        valueNotifiers[ApiStrings.colors].value as List<ExtraSettingsModel>;
    final sizes =
        valueNotifiers[ApiStrings.sizes].value as List<ExtraSettingsModel>;

    final isInventoryEnabled =
        valueNotifiers[ApiStrings.inventoryEnabled]!.value;
    final inventory =
        int.tryParse(controllers[ApiStrings.inventory]?.text ?? '');

    final isSizeColorInventory =
        valueNotifiers[ApiStrings.isSizeColorInventory]!.value;

    final isSizeOrColorsHasAnyStock =
        productHaveAnyColorOrSizePriceOrStockNotNull(
      colors: colors,
      sizes: sizes,
    );

    if (isInventoryEnabled == true && isSizeOrColorsHasAnyStock) {
      if (inventory == null || inventory <= 0) {
        controllers[ApiStrings.inventory]?.text = '0';
      }
    }

    final handledSizes = isInventoryEnabled == false ||
            isInventoryEnabled && (inventory ?? 0) > 0
        ? sizes
            .map((e) => e.copyWith(
                  stock: null,
                  canStockBeNull: true,
                ))
            .toList()
        : sizes;

    final handledColors = isInventoryEnabled == false ||
            isInventoryEnabled && (inventory ?? 0) > 0
        ? colors
            .map((e) => e.copyWith(
                  stock: null,
                  canStockBeNull: true,
                ))
            .toList()
        : colors;

    return ProductModel(
        inventory: inventory,
        inventoryEnabled: isInventoryEnabled,
        isSizeColorInventory: isSizeColorInventory,
        isFeatured: valueNotifiers[ApiStrings.isFeatured]!.value,
        englishTitle: controllers[ApiStrings.title]!.text,
        arabicTitle: controllers[ApiStrings.arabicTitle]!.text,
        englishDescription: controllers[ApiStrings.description]!.text,
        arabicDescription: controllers[ApiStrings.arabicDescription]!.text,
        totalPrice: num.tryParse(controllers[ApiStrings.price]?.text ?? '0'),
        salePrice: num.tryParse(controllers[ApiStrings.salePrice]?.text ?? '0'),
        isInStock: valueNotifiers[ApiStrings.isInStock]!.value,
        isSale: valueNotifiers[ApiStrings.isSale]!.value,
        colors: handledColors,
        sizes: handledSizes,
        images: product?.images,
        isActive: valueNotifiers[ApiStrings.isActive]?.value ?? true,
        category: category);
  }

  //! Delete product ============================================
  Future<void> deleteProduct(BuildContext context,
      {required String id,
      required CategoryModel cat,
      bool fromDetails = false}) async {
    baseFunction(
        context,
        () async {
          context.back();

          await productRepository.deleteProduct(docId: id);
        },
        type: FlushBarType.delete,
        additionalFunction: (_) async {
          await getProductsByCategory(context, catId: cat.documentId!);

          if (fromDetails) context.toReplacement(ProductsScreen(category: cat));
        });
  }

  //! Delete product Image ============================================
  Future<void> deleteProductImage(BuildContext context,
      {required ProductModel product, required String url}) async {
    baseFunction(context, () async {
      await productRepository.deleteProductImage(product: product, url: url);
    },
        type: FlushBarType.delete,
        additionalFunction: (_) => getProductsByCategory(context,
            catId: product.category!.documentId!));
  }
}
