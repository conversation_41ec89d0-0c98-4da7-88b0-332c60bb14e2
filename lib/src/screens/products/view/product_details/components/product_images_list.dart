import 'package:blur/blur.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

import '../../../../../core/shared_models/base_media_model.dart';
import '../../../../../core/shared_widgets/cached_images/main_cached_image.dart';
import '../../../models/products_model.dart';

class ProductImagesList extends StatelessWidget {
  final ProductModel productModel;
  final ValueNotifier<int> selectedIndex;

  const ProductImagesList(
      {super.key, required this.productModel, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50.h,
      width: double.infinity,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: productModel.images!.length,
        itemBuilder: (context, index) => GestureDetector(
          onTap: () => selectedIndex.value = index,
          child: _BuildLProductListWidget(
              index: index,
              selectedIndex: selectedIndex.value,
              image: productModel.images![index]),
        ),
        separatorBuilder: (context, index) => 5.horizontalSpace,
      ),
    ).frosted(
        blur: 12,
        frostColor: ColorManager.grey,
        frostOpacity: 0.1,
        padding: const EdgeInsets.all(AppSpaces.xSmallPadding));
  }
}

class _BuildLProductListWidget extends StatelessWidget {
  final BaseMediaModel image;
  final int index;
  final int selectedIndex;

  const _BuildLProductListWidget(
      {super.key,
      required this.image,
      required this.index,
      required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    final isSelected = index == selectedIndex;

    if (!isSelected) {
      return Container(
        width: kIsWeb ? 30.w : 60.w,
        height: 60.h,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8), boxShadow: const []),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: BaseCachedImage(
                image.url ?? '',
                fit: BoxFit.fill,
                width: kIsWeb ? 30.w : 60.w,
                height: 60.h,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.black.withOpacity(0.3),
              ),
            ),
          ],
        ),
      );
    }
    return Container(
      width: kIsWeb ? 30.w : 60.w,
      height: 60.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: ColorManager.primaryColor, width: 1.5)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: BaseCachedImage(
          width: kIsWeb ? 30.w : 60.w,
          height: 60.h,
          image.url ?? '',
          fit: BoxFit.fill,
        ),
      ),
    );
  }
}
