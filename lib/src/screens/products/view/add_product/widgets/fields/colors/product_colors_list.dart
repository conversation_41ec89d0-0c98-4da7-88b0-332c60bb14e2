import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/colors/choose_color_card.dart';
import 'package:provider/provider.dart';

import '../../../../../../../core/shared_widgets/animated/empty_data_widget.dart'
    show EmptyDataWidget;
import '../../../../../../../core/shared_widgets/buttons/base_text_button.dart';
import '../../../../../../dashboard/models/extra_setting_model.dart';
import '../../../../../../dashboard/view/size_and_color_screen.dart';
import '../../../../../../dashboard/view_model/extra_settings_view_model.dart';
import '../../../../../view_model/products_view_model.dart';

class ProductColorsList extends HookWidget {
  final ValueNotifier<List<ExtraSettingsModel>> selectedColors;
  final bool isSingle;
  final bool isInventory;
  final bool isVendorNeedInventory;
  final bool canAddStock;

  const ProductColorsList({
    super.key,
    required this.selectedColors,
    this.isInventory = false,
    this.isSingle = false,
    this.isVendorNeedInventory = false,
    this.canAddStock = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ExtraSettingVM>(
      builder: (context, extraSettingsVM, child) {
        if (extraSettingsVM.isLoading) {
          return const LoadingWidget();
        }

        final allColors = extraSettingsVM.extraSettings?.colors ?? [];

        if (allColors.isEmpty) {
          return Column(
            children: [
              EmptyDataWidget(
                message: context.tr.noColorsAdded,
              ),
              BaseTextButton(
                title: context.tr.addColors,
                withUnderline: true,
                onTap: () {
                  context.to(const SizeAndColorScreen(selectedTabIndex: 1));
                },
              ),
            ],
          );
        }

        return HookBuilder(builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(context.tr.colors, style: context.title),
                  if (canAddStock) ...[
                    context.smallGap,
                    const _AddColorsButton(),
                  ]
                ],
              ),
              context.mediumGap,
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: ColorManager.textFieldColor(context),
                  borderRadius: BorderRadius.circular(
                    AppRadius.baseRadius,
                  ),
                ),
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: allColors.indexed.map((indexedColor) {
                    final color = indexedColor.$2;
                    final index = indexedColor.$1;

                    final colorIsSelected = selectedColors.value
                        .map((e) => e.englishName)
                        .contains(color.englishName);

                    final border = colorIsSelected
                        ? Border.all(
                            color: color.englishName == AppConsts.whiteColor ||
                                    color.englishName == AppConsts.blackColor
                                ? Colors.blueGrey.shade100
                                : Color(
                                    int.tryParse(color.englishName ?? '') ?? 0),
                          )
                        : null;

                    void onSelectColor() {
                      extraSettingsVM.selectOrUnSelectColor(
                        selectedColors: selectedColors,
                        color: color.englishName ?? '',
                        isSingle: isSingle,
                      );
                    }

                    return Wrap(
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            ChooseColorCard(color: color).decorated(
                              border: border,
                              shape: BoxShape.circle,
                              padding: const EdgeInsets.all(
                                  AppSpaces.xSmallPadding / 1.2),
                            ),
                            if (colorIsSelected)
                              Icon(
                                Icons.check,
                                color:
                                    color.englishName == AppConsts.whiteColor ||
                                            color.englishName ==
                                                AppConsts.blackColor
                                        ? Colors.blueGrey.shade100
                                        : Colors.white,
                              )
                          ],
                        ).onTap(onSelectColor),
                      ],
                    ).paddingSymmetric(
                      horizontal: AppSpaces.smallPadding,
                      vertical: AppSpaces.xSmallPadding,
                    );
                  }).toList(),
                ),
              ),
              if (selectedColors.value.isNotEmpty) ...[
                context.largeGap,
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: selectedColors.value.length,
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemBuilder: (context, index) {
                    final color = selectedColors.value[index];
                    return Row(
                      children: [
                        ChooseColorCard(color: color),
                        context.mediumGap,
                        Expanded(
                          child: BaseTextField(
                            initialValue: canAddStock
                                ? color.stock?.toString() ?? '0'
                                : context.tr.colorStockIInventoryIsOff,
                            hint: canAddStock ? context.tr.stock : null,
                            textInputType: TextInputType.number,
                            onChanged: (value) {
                              color.stock = int.tryParse(value);
                              context.read<ProductVM>().notifyListeners();
                            },
                            enabled: canAddStock,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ]
            ],
          );
        });
      },
    );
  }
}

class _AddColorsButton extends StatelessWidget {
  const _AddColorsButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.to(const SizeAndColorScreen(selectedTabIndex: 1)),
      child: CircleAvatar(
        radius: 12,
        backgroundColor: ColorManager.primaryColor,
        child: Icon(
          Icons.add,
          color: ColorManager.white,
          size: 18.r,
        ),
      ),
    );
  }
}
