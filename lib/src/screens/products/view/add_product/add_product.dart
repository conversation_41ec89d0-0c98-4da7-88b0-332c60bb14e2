import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/widgets/fields/add_product_fields.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/products_screen.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/consts/api_strings.dart';
import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../categories/models/category_model.dart';
import '../../../shared/media/view_models/media_view_model.dart';

class AddProductScreen extends HookWidget {
  final ProductModel? product;
  final CategoryModel category;
  final bool isFromStepsDialog;

  const AddProductScreen({
    super.key,
    this.product,
    required this.category,
    this.isFromStepsDialog = false,
  });

  @override
  Widget build(BuildContext context) {
    final valueNotifiers = {
      ApiStrings.isSale: useState<bool>(product?.isSale ?? false),
      ApiStrings.isInStock: useState<bool>(product?.isInStock ?? true),
      ApiStrings.isFeatured: useState<bool>(product?.isFeatured ?? false),
      ApiStrings.isActive: useState<bool>(product?.isActive ?? true),
      ApiStrings.inventoryEnabled:
          useState<bool>(product?.inventoryEnabled ?? false),
      ApiStrings.isSizeColorInventory:
          useState<bool>(product?.isSizeColorInventory ?? false),
      ApiStrings.colors:
          useState<List<ExtraSettingsModel>>(product?.colors ?? []),
      ApiStrings.sizes:
          useState<List<ExtraSettingsModel>>(product?.sizes ?? []),
    };
    final fieldsControllers = {
      ApiStrings.title: useTextEditingController(text: product?.englishTitle),
      ApiStrings.arabicTitle:
          useTextEditingController(text: product?.arabicTitle),
      ApiStrings.inventory:
          useTextEditingController(text: product?.inventory?.toString()),
      ApiStrings.description:
          useTextEditingController(text: product?.englishDescription),
      ApiStrings.arabicDescription:
          useTextEditingController(text: product?.arabicDescription),
      ApiStrings.price:
          useTextEditingController(text: product?.totalPrice?.toString()),
      ApiStrings.salePrice: useTextEditingController(
        text: product?.isSale == true ? product?.salePrice?.toString() : null,
      ),
    };

    final isEdit = product != null;
    final productVM = context.read<ProductVM>();

    final mediaVM = context.read<MediaVM>();

    final formKey = useState(GlobalKey<FormState>());

    final images = useState<List<BaseMediaModel>>(product?.images ?? []);

    final inventoryEnabled = useState<bool>(product?.inventoryEnabled ?? false);

    void addEditProduct() async {
      if (isEdit) {
        await productVM.editProduct(context,
            productModel: product!,
            documentId: product!.documentId!,
            images: images.value,
            fields: (fieldsControllers, valueNotifiers),
            category: category,
            fileResult: mediaVM.filesPaths);
      } else {
        await productVM.addProduct(context,
            category: category,
            fields: (fieldsControllers, valueNotifiers),
            pickedImage: mediaVM.filesPaths);
      }
    }

    void validateAndAddOrEditProduct() async {
      if (mediaVM.filesPaths.isEmpty && !isEdit) {
        context.showBarMessage(
          context.tr.pleasePickImage,
          isError: true,
        );

        await productVM.resetErrorWithDuration();

        return;
      }

      if ((fieldsControllers[ApiStrings.title]?.text.isEmpty ?? true) &&
          (fieldsControllers[ApiStrings.arabicTitle]?.text.isEmpty ?? true)) {
        context.showBarMessage(context.tr.youMustAddOneOfThoseTitles,
            duration: 5, isError: true);

        return;
      }

      if ((fieldsControllers[ApiStrings.description]?.text.isEmpty ?? true) &&
          (fieldsControllers[ApiStrings.arabicDescription]?.text.isEmpty ??
              true)) {
        context.showBarMessage(context.tr.youMustAddOneOfThoseDescriptions,
            duration: 5, isError: true);

        return;
      }

      if (!formKey.value.currentState!.validate()) {
        productVM.resetErrorWithDuration();

        return;
      }
      final price =
          num.tryParse(fieldsControllers[ApiStrings.price]?.text ?? '0') ?? 0;
      final salePrice =
          num.tryParse(fieldsControllers[ApiStrings.salePrice]?.text ?? '0') ??
              0;

      if (salePrice >= price) {
        context.showBarMessage(
          context.tr.salePriceMustBeLessThanPrice,
          isError: true,
        );

        return;
      }

      if (salePrice > 0) {
        valueNotifiers[ApiStrings.isSale]?.value = true;
      } else {
        valueNotifiers[ApiStrings.isSale]?.value = false;
      }

      addEditProduct();
    }

    return WillPopScope(
      onWillPop: () async {
        backButtonOnPressed(context);

        return true;
      },
      child: Form(
        key: formKey.value,
        child: Consumer<ProductVM>(
          builder: (context, productVM, child) {
            return Scaffold(
              appBar: MainAppBar(
                haveBackButton: true,
                title: isEdit ? context.tr.editProduct : context.tr.addProduct,
                onBackPressed: backButtonOnPressed(context),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(
                  AppSpaces.mediumPadding,
                ),
                child: Consumer<MediaVM>(
                  builder: (context, mediaVM, child) {
                    return Column(
                      children: [
                        // * Pick & View images ========================
                        PickMultiImageWidget(
                            pickedResult: mediaVM.filesPaths,
                            networkImages: images,
                            onRemoveImage: (url) async {
                              await productVM.deleteProductImage(context,
                                  product: product!, url: url);
                            }),

                        context.largeGap,

                        // * Fields ========================
                        AddProductsFields(
                          category: category,
                          product: product,
                          fields: (fieldsControllers, valueNotifiers),
                          isVendorNeedInventory: inventoryEnabled,
                        ),

                        context.largeGap,

                        // * Add Product Button ========================
                        Button(
                          isOutLine: productVM.isLoading,
                          isLoading: productVM.isLoading,
                          label: isEdit
                              ? context.tr.editProduct
                              : context.tr.addProduct,
                          onPressed: validateAndAddOrEditProduct,
                        )
                      ],
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Function() backButtonOnPressed(BuildContext context) {
    if (isFromStepsDialog) {
      return () => context.back();
    } else {
      return () {
        context.back();
        context.toReplacement(ProductsScreen(category: category));
        context.read<MediaVM>().clearFiles();
      };
    }
  }
}
