import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';

import '../../../../../core/shared_widgets/cached_images/main_cached_image.dart'
    show BaseCachedImage;

class BulkEditProductsListView extends HookWidget {
  final List<ProductModel> products;
  final ValueNotifier<List<ProductModel>> selectedProducts;
  final ValueNotifier<Map<String, Map<String, dynamic>>> bulkEditData;

  const BulkEditProductsListView({
    super.key,
    required this.products,
    required this.selectedProducts,
    required this.bulkEditData,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      itemCount: products.length,
      separatorBuilder: (context, index) => context.smallGap,
      itemBuilder: (context, index) {
        final product = products[index];
        return _ProductListTile(
          product: product,
          selectedProducts: selectedProducts,
          bulkEditData: bulkEditData,
        );
      },
    );
  }
}

class _ProductListTile extends HookWidget {
  final ProductModel product;
  final ValueNotifier<List<ProductModel>> selectedProducts;
  final ValueNotifier<Map<String, Map<String, dynamic>>> bulkEditData;

  const _ProductListTile({
    required this.product,
    required this.selectedProducts,
    required this.bulkEditData,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = useState(false);
    final priceController = useTextEditingController();
    final salePriceController = useTextEditingController();
    final sizeControllers = useState(<String, TextEditingController>{});

    // Initialize controllers with current values
    useEffect(() {
      priceController.text = product.totalPrice?.toString() ?? '';
      salePriceController.text = product.salePrice?.toString() ?? '';

      // Initialize size controllers
      for (var size in product.sizes) {
        if (size.price != null) {
          final key = size.englishName ?? '';
          sizeControllers.value[key] =
              TextEditingController(text: size.price.toString());
        }
      }

      return () {
        // priceController.dispose();
        // salePriceController.dispose();
        // for (var controller in sizeControllers.value.values) {
        //   controller.dispose();
        // }
      };
    }, []);

    // Check if product uses size pricing
    final usesSizePricing =
        product.sizes.any((size) => size.price != null && size.price! > 0);

    void toggleSelection() {
      isSelected.value = !isSelected.value;

      if (isSelected.value) {
        selectedProducts.value = [...selectedProducts.value, product];
        // Initialize bulk edit data for this product
        if (product.documentId != null) {
          final currentData =
              Map<String, Map<String, dynamic>>.from(bulkEditData.value);
          currentData[product.documentId!] = {};
          bulkEditData.value = currentData;
        }
      } else {
        selectedProducts.value = selectedProducts.value
            .where((p) => p.documentId != product.documentId)
            .toList();
        // Remove from bulk edit data
        if (product.documentId != null) {
          final currentData =
              Map<String, Map<String, dynamic>>.from(bulkEditData.value);
          currentData.remove(product.documentId!);
          bulkEditData.value = currentData;
        }
      }
    }

    void updateBulkEditData(String field, dynamic value) {
      if (product.documentId != null && isSelected.value) {
        final currentData =
            Map<String, Map<String, dynamic>>.from(bulkEditData.value);
        currentData[product.documentId!] = {
          ...currentData[product.documentId!] ?? {},
          field: value,
        };
        bulkEditData.value = currentData;
      }
    }

    void updateSizePricing(String sizeName, num? price) {
      if (product.documentId != null && isSelected.value) {
        final currentData =
            Map<String, Map<String, dynamic>>.from(bulkEditData.value);
        final productData = currentData[product.documentId!] ?? {};

        final sizes =
            List<Map<String, dynamic>>.from(productData['sizes'] ?? []);
        final existingSizeIndex =
            sizes.indexWhere((s) => s['name'] == sizeName);

        if (existingSizeIndex >= 0) {
          sizes[existingSizeIndex]['price'] = price;
        } else {
          sizes.add({'name': sizeName, 'price': price});
        }

        productData['sizes'] = sizes;
        currentData[product.documentId!] = productData;
        bulkEditData.value = currentData;
      }
    }

    return Container(
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: isSelected.value
            ? ColorManager.primaryColor.withOpacity(0.1)
            : ColorManager.textFieldColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected.value
              ? ColorManager.primaryColor
              : ColorManager.grey.withOpacity(0.2),
          width: isSelected.value ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // Header with checkbox and product info
          Row(
            children: [
              // Checkbox
              Checkbox(
                value: isSelected.value,
                onChanged: (_) => toggleSelection(),
                activeColor: ColorManager.primaryColor,
              ),

              // Product thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: BaseCachedImage(
                  product.thumbnail?.url ?? '',
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  cachedImage: false,
                ),
              ),

              context.mediumGap,

              // Product name
              Expanded(
                child: Text(
                  product.nameByLang(context),
                  style: context.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          // Price editing fields (only show if selected)
          if (isSelected.value) ...[
            context.mediumGap,
            const Divider(),
            context.smallGap,
            if (usesSizePricing) ...[
              // Size-based pricing
              Text(
                context.tr.sizePricing,
                style:
                    context.labelMedium.copyWith(fontWeight: FontWeight.w600),
              ),
              context.smallGap,
              ...product.sizes.where((size) => size.price != null).map((size) {
                final sizeName = size.englishName ?? '';
                final sizeNameAr = size.arabicName ?? '';
                if (!sizeControllers.value.containsKey(sizeName)) {
                  sizeControllers.value[sizeName] =
                      TextEditingController(text: size.price?.toString() ?? '');
                }

                return Padding(
                  padding: EdgeInsets.only(bottom: AppSpaces.smallPadding),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          context.isEng
                              ? (size.englishName ?? '')
                              : (size.arabicName ?? size.englishName ?? ''),
                          style: context.labelMedium,
                        ),
                      ),
                      context.smallGap,
                      Expanded(
                        flex: 3,
                        child: TextFormField(
                          controller: sizeControllers.value[sizeName],
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: context.tr.price,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: AppSpaces.smallPadding,
                              vertical: AppSpaces.xSmallPadding,
                            ),
                          ),
                          onChanged: (value) {
                            final price = num.tryParse(value);
                            updateSizePricing(sizeName, price);
                          },
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ] else ...[
              // Regular pricing
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: priceController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: context.tr.price,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSpaces.smallPadding,
                          vertical: AppSpaces.xSmallPadding,
                        ),
                      ),
                      onChanged: (value) {
                        final price = num.tryParse(value);
                        updateBulkEditData('price', price);
                      },
                    ),
                  ),
                  context.mediumGap,
                  Expanded(
                    child: TextFormField(
                      controller: salePriceController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: context.tr.salePrice,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: AppSpaces.smallPadding,
                          vertical: AppSpaces.xSmallPadding,
                        ),
                      ),
                      onChanged: (value) {
                        final salePrice = num.tryParse(value);
                        updateBulkEditData('sale_price', salePrice);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ],
      ),
    );
  }
}
