import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/widgets/products_by_category_grid_view.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/shared_widgets/animated/empty_data_widget.dart';
import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../categories/models/category_model.dart';
import '../add_product/add_product.dart';

class ProductsScreen extends HookWidget {
  final CategoryModel category;

  const ProductsScreen({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    final productsVM = context.read<ProductVM>();

    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        productsVM.getProductsByCategory(context, catId: category.documentId!);
      });

      return () {};
    }, []);

    return Consumer2<AuthVM, ProductVM>(
      builder: (context, authVM, productsVM, child) {
        final reachLimit = authVM.isFree &&
            productsVM.productsByCategory.length > AppConsts.maxProductsLimit;

        final colorIcon =
            context.isDark ? ColorManager.white : ColorManager.black;

        void navigateToProduct() {
          if (authVM.isFree && reachLimit) {
            context.showBarMessage(context.tr.productLimitReached,
                isError: true);

            return;
          } else {
            context.to(AddProductScreen(category: category));
          }
        }

        return RefreshIndicatorWidget(
            onRefresh: () => productsVM.getProductsByCategory(context,
                catId: category.documentId!),
            child: Scaffold(
              appBar: MainAppBar(
                isCenterTitle: false,
                title: context.tr.products,
                actionWidget: AddButton(
                  label: context.tr.addProduct,
                  onPressed: navigateToProduct,
                ).paddingOnly(right: AppSpaces.smallPadding),
                haveBackButton: true,
              ),
              body: Column(
                children: [
                  context.mediumGap,
                  BaseTextField(
                    controller: queryController,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        isSearch.value = false;
                        productsVM.filterSearchedList(
                            query: '', isAll: false, context: context);
                        return;
                      }

                      isSearch.value = true;

                      queryController.text = value;

                      productsVM.filterSearchedList(
                          query: value, isAll: false, context: context);
                    },
                    suffixIcon: Icon(
                            isSearch.value
                                ? Icons.close
                                : CupertinoIcons.search,
                            color: colorIcon)
                        .onTap(() {
                      queryController.clear();

                      isSearch.value = false;

                      productsVM.filterSearchedList(
                          query: '', isAll: false, context: context);
                    }),
                    hint: context.tr.searchProduct,
                    withoutEnter: true,
                  ).paddingSymmetric(horizontal: AppSpaces.mediumPadding),
                  context.largeGap,
                  Expanded(
                    child: Builder(builder: (context) {
                      final products = productsVM.searchedProducts;

                      if (products.isEmpty || productsVM.isLoading) {
                        return EmptyDataWidget(
                          isLoading: productsVM.isLoading,
                          message: context.tr.noProductsInThisCategory,
                        );
                      } else {
                        return ProductsByCategoryGridView(
                          categoryModel: category,
                          products: productsVM.searchedProducts,
                        );
                      }
                    }),
                  ),
                ],
              ),
            ));
      },
    );
  }
}
