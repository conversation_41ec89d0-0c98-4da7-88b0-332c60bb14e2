import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/theme/theme.dart';
import '../../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../../core/shared_widgets/cached_images/main_cached_image.dart'
    show BaseCachedImage;
import '../../../../core/shared_widgets/shared_widgets.dart';

class SortProductsScreen extends HookWidget {
  final List<ProductModel> products;
  final CategoryModel category;

  const SortProductsScreen({
    super.key,
    required this.products,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final sortedProducts = useState(List<ProductModel>.from(products));

    void _onReorder(int oldIndex, int newIndex) {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }

      final item = sortedProducts.value.removeAt(oldIndex);
      sortedProducts.value.insert(newIndex, item);

      for (var i = 0; i < sortedProducts.value.length; i++) {}
      sortedProducts.value = List.from(sortedProducts.value);
    }

    Future<void> _saveChanges() async {
      final productVM = context.read<ProductVM>();

      try {
        final Map<String, Map<String, dynamic>> bulkUpdateData = {};

        for (var i = 0; i < sortedProducts.value.length; i++) {
          final product = sortedProducts.value[i];
          if (product.documentId != null) {
            bulkUpdateData[product.documentId!] = {
              'sort': i + 1,
            };
          }
        }

        await productVM.bulkUpdateProducts(context, bulkUpdateData);

        productVM.getProductsByCategory(context, catId: category.documentId!);
      } catch (e) {
        if (context.mounted) {
          context.showBarMessage(context.tr.somethingWentWrong, isError: true);
        }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.sortProducts,
        haveBackButton: true,
        isCenterTitle: false,
        actionWidget: Button(
          isLoading: context.watch<ProductVM>().isLoading,
          label: context.tr.save,
          onPressed: _saveChanges,
        ).sized(
          height: 40,
        ),
      ),
      body: Column(
        children: [
          // Description
          Padding(
            padding: EdgeInsets.all(AppSpaces.mediumPadding),
            child: Text(
              context.tr.sortProductsDescription,
              style: context.labelMedium,
              textAlign: TextAlign.center,
            ),
          ),

          context.mediumGap,

          // Products list
          Expanded(
            child: ReorderableListView(
              padding:
                  EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
              onReorder: _onReorder,
              children: sortedProducts.value.indexed
                  .map((e) => Container(
                        key: ValueKey(e.$2.documentId),
                        margin: EdgeInsets.only(bottom: AppSpaces.smallPadding),
                        decoration: BoxDecoration(
                          color: ColorManager.textFieldColor(context),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: ListTile(
                          leading: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: context.appTheme.primaryColor
                                      .withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    (e.$1 + 1).toString(),
                                    style: context.labelSmall.copyWith(
                                      color: context.appTheme.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              context.smallGap,
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: BaseCachedImage(
                                  e.$2.thumbnail?.url ?? '',
                                  width: 50,
                                  height: 50,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ],
                          ),
                          title: Text(
                            e.$2.nameByLang(context),
                            style: context.labelMedium,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              context.xSmallGap,
                              Row(
                                children: [
                                  if (e.$2.isSale) ...[
                                    Text(
                                      e.$2.totalPrice?.toCurrency(context) ??
                                          '',
                                      style: context.labelSmall.copyWith(
                                        decoration: TextDecoration.lineThrough,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    context.xSmallGap,
                                    Text(
                                      e.$2.salePrice?.toCurrency(context) ?? '',
                                      style: context.labelSmall.copyWith(
                                        color: ColorManager.primaryColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ] else ...[
                                    Text(
                                      e.$2.totalPrice?.toCurrency(context) ??
                                          '',
                                      style: context.labelSmall.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                          trailing: Icon(
                            Icons.drag_handle,
                            color: context.appTheme.primaryColor,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}
