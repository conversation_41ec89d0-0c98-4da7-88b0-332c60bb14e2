import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';

import '../../../../../core/shared_widgets/product_card/product_card.dart';
import '../../../../categories/models/category_model.dart';
import '../../../models/products_model.dart';

class ProductsByCategoryGridView extends HookWidget {
  final CategoryModel categoryModel;
  final List<ProductModel> products;

  const ProductsByCategoryGridView(
      {super.key, required this.categoryModel, required this.products});

  @override
  Widget build(BuildContext context) {
    return AutoHeightGridView(
        shrinkWrap: true,
        itemCount: products.length,
        crossAxisCount: 2,
        mainAxisSpacing: 15.h,
        crossAxisSpacing: 10,
        padding: EdgeInsets.only(left: 10, right: 10, bottom: 50.h),
        builder: (BuildContext context, int index) {
          final product = products[index];

          return WidgetAnimator(
            delay: Duration(milliseconds: AppConsts.animatedDuration * index),
            child: ProductCard(
              productModel: product,
              cat: categoryModel,
            ),
          );
        });
  }
}
