import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/bulk_edit_screen/bulk_edit_screen.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/products/sort_products_screen.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_spaces.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../categories/models/category_model.dart';
import '../../../../categories/view/categories/widgets/category_search_header.dart';

class ProductsHeaderSection extends StatelessWidget {
  final TextEditingController queryController;
  final ValueNotifier<bool> isSearch;
  final CategoryModel category;

  const ProductsHeaderSection({
    super.key,
    required this.queryController,
    required this.isSearch,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductVM>(
      builder: (context, productsVM, child) {
        final colorIcon =
            context.isDark ? ColorManager.white : ColorManager.black;

        void navigateToSortProducts() {
          final products = productsVM.searchedProducts.isNotEmpty
              ? productsVM.searchedProducts
              : productsVM.productsByCategory;

          if (products.isEmpty) {
            context.showBarMessage(context.tr.noProductsInThisCategory,
                isError: true);
            return;
          }

          context.to(SortProductsScreen(
            products: products,
            category: category,
          ));
        }

        return Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
          child: Column(
            children: [
              SizedBox(
                height: 35.h,
                child: Row(
                  children: [
                    Expanded(
                      child: categoriesScreenButtonWidget(
                        context,
                        onPressed: () {
                          context.to(BulkEditProductsScreen(
                            category: category,
                          ));
                        },
                        text: context.tr.bulkEdit,
                        icon: Icon(
                          CupertinoIcons.square_pencil,
                          color: context.appTheme.primaryColorDark,
                        ),
                      ),
                    ),
                    context.smallGap,
                    Expanded(
                      child: categoriesScreenButtonWidget(
                        context,
                        onPressed: navigateToSortProducts,
                        text: context.tr.sortProducts,
                        icon: Icon(
                          CupertinoIcons.sort_down,
                          color: context.appTheme.primaryColorDark,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              context.mediumGap,
              BaseTextField(
                controller: queryController,
                onChanged: (value) {
                  if (value.isEmpty) {
                    isSearch.value = false;
                    productsVM.filterSearchedList(
                        query: '', isAll: false, context: context);
                    return;
                  }

                  isSearch.value = true;

                  queryController.text = value;

                  productsVM.filterSearchedList(
                      query: value, isAll: false, context: context);
                },
                suffixIcon: Icon(
                        isSearch.value ? Icons.close : CupertinoIcons.search,
                        color: colorIcon)
                    .onTap(() {
                  queryController.clear();

                  isSearch.value = false;

                  productsVM.filterSearchedList(
                      query: '', isAll: false, context: context);
                }),
                hint: context.tr.searchProduct,
                withoutEnter: true,
              ),
            ],
          ),
        );
      },
    );
  }
}
