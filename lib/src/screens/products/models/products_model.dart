import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared_models/base_media_model.dart';
import '../../categories/models/category_model.dart';

List<ProductModel> responseToProductModel(List<Never> response) {
  final allProducts = <ProductModel>[];

  for (var product in response) {
    allProducts.add(ProductModel.fromJson(product));
  }

  return allProducts;
}

class ProductModel {
  final int? id;
  final String? documentId;
  final String? englishTitle;
  final String? arabicTitle;
  final int? inventory;
  final String? englishDescription;
  final String? arabicDescription;
  final num? totalPrice;
  final BaseMediaModel? thumbnail;
  final List<BaseMediaModel>? images;
  final num? salePrice;
  final bool isSale;
  final bool isInStock;
  final bool isFeatured;
  final bool isActive;
  final bool inventoryEnabled;
  final bool isSizeColorInventory;
  final CategoryModel? category;
  final List<ExtraSettingsModel> sizes;
  final List<ExtraSettingsModel> colors;
  final int? sort;

  ProductModel({
    this.id,
    this.englishDescription,
    this.arabicDescription,
    this.documentId,
    this.englishTitle,
    this.arabicTitle,
    this.category,
    this.inventory,
    this.totalPrice,
    this.thumbnail,
    this.images,
    this.salePrice,
    this.sizes = const [],
    this.colors = const [],
    this.isInStock = true,
    this.isActive = false,
    this.isSale = false,
    this.isSizeColorInventory = false,
    this.isFeatured = false,
    this.inventoryEnabled = false,
    this.sort,
  });

  num get actualPrice => isSale ? (salePrice ?? 0) : (totalPrice ?? 0);

  String nameByLang(BuildContext context) {
    final isEng = context.readIsEng;

    return isEng
        ? (englishTitle ?? arabicTitle ?? '')
        : (arabicTitle ?? englishTitle ?? '');
  }

  //! json constructor---------------------------------------
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    final productCategory = json[ApiStrings.categories].runtimeType == int
        ? []
        : (json[ApiStrings.categories] as List?) ?? [];

    final category = CategoryModel.fromJson(productCategory.firstOrNull ?? {});

    final images = json[ApiStrings.images] ?? [];

    final imagesList = List<BaseMediaModel>.from(
        images.map((image) => BaseMediaModel.fromJson(image)));

    final thumbnail = imagesList.isEmpty ? null : imagesList.first;

    final colors = json[ApiStrings.colors] ?? [];

    final colorsList = List<ExtraSettingsModel>.from(
        colors.map((e) => ExtraSettingsModel.fromProductJson(e))).toList();

    final sizes = json[ApiStrings.sizes] ?? [];

    final sizesList = List<ExtraSettingsModel>.from(
        sizes.map((e) => ExtraSettingsModel.fromProductJson(e))).toList();

    final fromLocalThumbnail = json[ApiStrings.thumbnail] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.thumbnail])
        : null;

    return ProductModel(
        id: json[ApiStrings.id],
        documentId: json[ApiStrings.documentId],
        englishTitle: json[ApiStrings.title],
        inventoryEnabled: json[ApiStrings.inventoryEnabled] ?? false,
        isSizeColorInventory: json[ApiStrings.isSizeColorInventory] ?? false,
        arabicTitle: json[ApiStrings.arabicTitle],
        totalPrice: json[ApiStrings.price] ?? 0,
        thumbnail: thumbnail ?? fromLocalThumbnail,
        salePrice: json[ApiStrings.salePrice] ?? 0,
        inventory: json[ApiStrings.inventory],
        isSale: json[ApiStrings.isSale] ?? false,
        images: imagesList,
        englishDescription: json[ApiStrings.description],
        arabicDescription: json[ApiStrings.arabicDescription],
        isInStock: !(json[ApiStrings.outOfStock] ?? false),
        isFeatured: json[ApiStrings.isFeatured] ?? false,
        isActive: json[ApiStrings.isActive] ?? false,
        colors: colorsList,
        sizes: sizesList,
        sort: json[ApiStrings.sort],
        category: category);
  }

  ProductModel copyWith(
      {int? id,
      String? documentId,
      String? title,
      String? arabicTitle,
      String? description,
      String? arabicDescription,
      BaseMediaModel? thumbnail,
      num? totalPrice,
      BaseMediaModel? image,
      num? salePrice,
      bool? isInStock,
      bool? isSale,
      bool? inventoryEnabled,
      bool? isSizeColorInventory,
      List<BaseMediaModel>? images,
      List<ExtraSettingsModel>? colors,
      List<ExtraSettingsModel>? sizes,
      String? size,
      String? color,
      int? inventory,
      bool? isFeatured,
      bool? isActive,
      int? sort,
      CategoryModel? category}) {
    return ProductModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      totalPrice: totalPrice ?? this.totalPrice,
      englishTitle: title ?? this.englishTitle,
      arabicTitle: arabicTitle ?? this.arabicTitle,
      englishDescription: description ?? this.englishDescription,
      arabicDescription: arabicDescription ?? this.arabicDescription,
      thumbnail: thumbnail ?? this.thumbnail,
      isSale: isSale ?? this.isSale,
      isInStock: isInStock ?? this.isInStock,
      salePrice: salePrice ?? this.salePrice,
      inventory: inventory ?? this.inventory,
      category: category ?? this.category,
      isFeatured: isFeatured ?? this.isFeatured,
      images: images ?? this.images,
      colors: colors ?? this.colors,
      sizes: sizes ?? this.sizes,
      isActive: isActive ?? this.isActive,
      inventoryEnabled: inventoryEnabled ?? this.inventoryEnabled,
      isSizeColorInventory: isSizeColorInventory ?? this.isSizeColorInventory,
      sort: sort ?? this.sort,
    );
  }

  //! to json
  Map<String, dynamic> toJson({
    bool fromLocal = false,
  }) {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) data[ApiStrings.id] = id;

    if (documentId != null) {
      data[ApiStrings.documentId] = documentId;
    }

    data[ApiStrings.images] = fromLocal
        ? images?.map((v) => v.toJson()).toList()
        : images?.map((v) => v.id).toList();

    data[ApiStrings.title] = englishTitle;

    data[ApiStrings.arabicTitle] = arabicTitle;

    data[ApiStrings.description] = englishDescription;

    data[ApiStrings.arabicDescription] = arabicDescription;

    data[ApiStrings.price] = totalPrice;
    data[ApiStrings.salePrice] = salePrice ?? totalPrice;
    data[ApiStrings.isSale] = isSale;
    data[ApiStrings.outOfStock] = !isInStock;
    data[ApiStrings.categories] = category?.documentId;
    data[ApiStrings.inventory] = inventory;
    data[ApiStrings.isFeatured] = isFeatured;
    data[ApiStrings.isActive] = isActive;
    data[ApiStrings.inventoryEnabled] = inventoryEnabled;
    data[ApiStrings.isSizeColorInventory] = isSizeColorInventory;
    if (sort != null) data[ApiStrings.sort] = sort;

    if (thumbnail != null) data[ApiStrings.thumbnail] = thumbnail?.toJson();

    data[ApiStrings.colors] = colors.map((v) => v.toProductJson()).toList();

    data[ApiStrings.sizes] = sizes.map((v) => v.toProductJson()).toList();

    return data;
  }

  //! To Status Json
  Map<String, dynamic> toStatusJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (documentId != null) {
      data[ApiStrings.documentId] = documentId;
    }

    data[ApiStrings.isActive] = isActive;

    return data;
  }
}
