import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/widgets/top_section/vendor_card_details/widgets/vendor_card_status_widgets/home_vendor_status_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/widgets/order_card/order_card.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

import '../../../core/shared_widgets/shimmer_loading/home_loading/orders_loading.dart';
import '../../categories/view/add_and_edit_category/widgets/main_category_info_dialog_widget.dart';
import '../../drawer/app_drawer.dart';

class HomePage extends HookWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final orderVM = context.watch<OrderVM>();

    return Scaffold(
        drawer: AppDrawer(),
        body: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: RefreshIndicator(
            color: ColorManager.primaryColor,
            onRefresh: () async {
              orderVM.getHomeOrders(context);
              orderVM.getOrderStatistics(context);
            },
            child: ListView(
              children: [
                HomeVendorStatusCardWidget(),
                if (orderVM.homeOrders.isEmpty && !orderVM.isLoading) ...[
                  EmptyDataWidget(
                    message: context.tr.noOrdersFound,
                  )
                ] else ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.mediumPadding),
                    child:
                        Text(context.tr.latestOrders, style: context.subTitle),
                  ),
                  if (orderVM.isLoading) ...[
                    context.mediumGap,
                    OrdersLoading()
                  ] else
                    ListView.separated(
                        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          final order = orderVM.homeOrders[index];
                          return OrderCard(order: order);
                        },
                        separatorBuilder: (context, index) => context.mediumGap,
                        itemCount: orderVM.homeOrders.length),
                ]
              ],
            ),
          ),
        ));
  }
}

class WaveContainer extends StatelessWidget {
  final Widget child;
  final List<Color>? colors;
  final Color? color;

  const WaveContainer({
    super.key,
    required this.child,
    this.colors,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: SuperWavyClipper(),
      child: Container(
        padding: EdgeInsets.only(
          left: AppSpaces.mediumPadding,
          right: AppSpaces.mediumPadding,
          bottom: AppSpaces.xlLargePadding + 5.h,
        ),
        width: double.infinity,
        color: colors == null ? color : null,
        decoration: colors != null
            ? BoxDecoration(
                gradient: LinearGradient(colors: colors!),
              )
            : null,
        child: child,
      ),
    );
  }
}

class SuperWavyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height - 20); // Start a bit above the bottom

    double waveWidth = size.width / 6;
    double waveHeight = 20; // Reduced from 40 to 20

    for (int i = 0; i < 6; i++) {
      double x1 = waveWidth * (i + 0.5);
      double y1 = (i % 2 == 0) ? size.height : size.height - waveHeight * 2;
      double x2 = waveWidth * (i + 1);
      double y2 = size.height - 20;
      path.quadraticBezierTo(x1, y1, x2, y2);
    }

    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
