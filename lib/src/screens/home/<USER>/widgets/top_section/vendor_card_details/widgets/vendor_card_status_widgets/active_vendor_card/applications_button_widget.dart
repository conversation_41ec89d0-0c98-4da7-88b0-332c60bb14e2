import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../../../../../core/resources/app_spaces.dart'
    show AppSpaces;

class ApplicationsButtonWidget extends StatelessWidget {
  const ApplicationsButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.watch<AuthVM>();

    final isPlayStoreLinkNotEmpty =
        authVM.currentVendor?.playStoreLink?.isNotEmpty ?? false;

    final isAppStoreLinkNotEmpty =
        authVM.currentVendor?.appStoreLink?.isNotEmpty ?? false;

    return Row(
      children: [
        if (isPlayStoreLinkNotEmpty)
          ApplicationButtonWidget(
            title: context.tr.playStore,
            icon: Assets.playStore,
            link: authVM.currentVendor?.playStoreLink ?? '',
            isPlayStore: true,
          ),
        if (isAppStoreLinkNotEmpty && isPlayStoreLinkNotEmpty)
          context.mediumGap,
        if (isAppStoreLinkNotEmpty)
          ApplicationButtonWidget(
            title: context.tr.appStore,
            icon: Assets.appStore,
            link: authVM.currentVendor?.appStoreLink ?? '',
          ),
      ],
    );
  }
}

class ApplicationButtonWidget extends StatelessWidget {
  final String title;
  final String icon;
  final String link;
  final bool isPlayStore;

  const ApplicationButtonWidget(
      {super.key,
      required this.title,
      required this.icon,
      required this.link,
      this.isPlayStore = false});

  @override
  Widget build(BuildContext context) {
    final qrKey = GlobalKey();

    return Expanded(
      child: ElevatedButton(
          style: ButtonStyle(
            elevation: WidgetStatePropertyAll(0),
            backgroundColor: WidgetStatePropertyAll(
              Color(0x5ed9d9d9),
            ),
            shape: WidgetStatePropertyAll(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppRadius.baseRadius,
                ),
              ),
            ),
          ),
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) {
                return BaseDialog(
                    withCloseButton: true,
                    child: Container(
                      padding: const EdgeInsets.all(16.0),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: context.appTheme.cardColor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(AppRadius.tabBarRadius),
                          topRight: Radius.circular(AppRadius.tabBarRadius),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          RepaintBoundary(
                            key: qrKey,
                            child: Container(
                              padding: EdgeInsets.all(AppSpaces.mediumPadding),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(
                                  AppRadius.baseRadius,
                                ),
                              ),
                              height: 250,
                              child: PrettyQrView.data(
                                data: link,
                                decoration: PrettyQrDecoration(
                                  image: PrettyQrDecorationImage(
                                    scale: .15,
                                    image: AssetImage(isPlayStore
                                        ? Assets.playStore
                                        : Assets.appStore),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          context.largeGap,
                          DialogButtonWidget(
                            title: isPlayStore
                                ? context.tr.playStore
                                : context.tr.appStore,
                            icon: Icons.arrow_forward_ios_rounded,
                            onPressed: () {
                              openURL(link);
                            },
                          ),
                          context.smallGap,
                          Row(
                            children: [
                              Expanded(
                                child: DialogButtonWidget(
                                  title: context.tr.shareQr,
                                  icon: Icons.qr_code,
                                  isFittedBox: true,
                                  onPressed: () {
                                    shareQrCode(
                                      context: context,
                                      link: '',
                                      qrKey: qrKey,
                                    );
                                  },
                                ),
                              ),
                              context.smallGap,
                              Expanded(
                                child: DialogButtonWidget(
                                  title: context.tr.shareLink,
                                  icon: Icons.link,
                                  isFittedBox: true,
                                  onPressed: () {
                                    Share.share(link);
                                  },
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ));
              },
            );
          },
          child: Center(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  icon,
                  height: 20.r,
                  errorBuilder: (context, error, stackTrace) {
                    return const SizedBox.shrink();
                  },
                ),
                context.xSmallGap,
                Text(title, style: context.whiteLabelMedium),
              ],
            ),
          )),
    );
  }
}

class DialogButtonWidget extends StatelessWidget {
  final String title;
  final IconData icon;
  final Function() onPressed;
  final bool isFittedBox;

  const DialogButtonWidget(
      {super.key,
      required this.title,
      required this.icon,
      required this.onPressed,
      this.isFittedBox = false});

  @override
  Widget build(BuildContext context) {
    final widget = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        context.smallGap,
        Text(
          title,
          style: context.labelLarge.copyWith(fontWeight: FontWeight.w400),
        ),
        context.smallGap,
        Icon(icon, color: context.appTheme.primaryColorDark, size: 16.r),
      ],
    );
    return ElevatedButton(
        style: ButtonStyle(
          elevation: WidgetStatePropertyAll(0),
          backgroundColor: WidgetStatePropertyAll(
            ColorManager.textFieldColor(context),
          ),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppRadius.baseRadius,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        child: isFittedBox
            ? FittedBox(
                fit: BoxFit.scaleDown,
                child: widget,
              )
            : widget);
  }
}
