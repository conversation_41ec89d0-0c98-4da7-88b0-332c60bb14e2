import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/widgets/top_section/vendor_card_details/widgets/vendor_card_status_widgets/active_vendor_card/vendor_website_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../../../../core/consts/app_constants.dart';
import '../../../../../../../../../core/resources/theme/theme.dart';
import '../../../../../../../../auth/models/vendor_model.dart';
import 'applications_button_widget.dart';

class ActiveVendorCardWidget extends HookWidget {
  final VendorModel? vendorData;
  const ActiveVendorCardWidget({super.key, required this.vendorData});

  @override
  Widget build(BuildContext context) {
    final websiteLink =
        vendorData?.websiteLink ?? AppConsts.vendorWebsiteLink();

    return Consumer<OrderVM>(
      builder: (context, orderVM, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppSpaces.smallPadding),
              decoration: BoxDecoration(
                color: Color(0x5ed9d9d9),
                borderRadius: BorderRadius.circular(AppRadius.baseRadius),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  VendorWebsiteLink(
                    websiteLink: websiteLink,
                  ),
                  Icon(Icons.arrow_forward_ios_rounded,
                      color: Colors.white, size: 15)
                ],
              ),
            ),
            context.smallGap,
            ApplicationsButtonWidget(),
            context.mediumGap,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                VendorStatisticsWidget(
                  title: context.tr.totalOrdersHome,
                  value: orderVM.orderStatistics.totalOrders,
                  withCurrency: false,
                ),
                VendorStatisticsWidget(
                  title: context.tr.totalProfit,
                  value: orderVM.orderStatistics.totalEarnings,
                  withCurrency: true,
                ),
                VendorStatisticsWidget(
                  title: context.tr.dailyOrders,
                  value: orderVM.orderStatistics.dailyOrders,
                  withCurrency: false,
                ),
                VendorStatisticsWidget(
                  title: context.tr.dailyProfit,
                  value: orderVM.orderStatistics.dailyEarnings,
                  withCurrency: true,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}

class VendorStatisticsWidget extends StatelessWidget {
  final String title;
  final num value;
  final bool withCurrency;

  const VendorStatisticsWidget(
      {super.key,
      required this.title,
      required this.value,
      required this.withCurrency});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(title,
            textAlign: TextAlign.center,
            style: context.labelMedium.copyWith(
                fontWeight: FontWeight.w500, color: ColorManager.white)),
        context.smallGap,
        Container(
          height: 2,
          width: 40,
          color: ColorManager.white.withAlpha(90),
        ),
        context.smallGap,
        Text(
          value.formatNumber(context, withCurrency: withCurrency),
          textAlign: TextAlign.center,
          style: context.labelMedium
              .copyWith(fontWeight: FontWeight.bold, color: ColorManager.white),
        ),
      ],
    );
  }
}
