import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shimmer_loading/home_loading/vendor_details_card_loading.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/widgets/top_section/vendor_card_details/widgets/vendor_card_status_widgets/expired_vendor_card/expired_vendor_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/notification/view/notifications_screen.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../../../../../generated/assets.dart';
import '../../../../../../../../core/resources/theme/theme.dart';
import '../../../../../../../../core/shared_widgets/icon_widget/icon_widget.dart';
import '../../../../../../../notification/view_model/notification_view_model.dart';
import '../../../../../home_page.dart';
import '../home_status_text_widget.dart';
import 'active_vendor_card/active_vendor_card_widget.dart';
import 'free_vendor_card/free_vendor_card_widget.dart';

class HomeVendorStatusCardWidget extends StatelessWidget {
  const HomeVendorStatusCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthVM, ProductVM>(
      builder: (context, authVM, productVM, child) {
        return HookBuilder(builder: (context) {
          final vendorData = authVM.currentVendor;

          final productVM = context.read<ProductVM>();

          final canViewLinkActions = useState<bool>(false);

          useEffect(() {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              canViewLinkActions.value =
                  await productVM.isOneProductExist(context);
              // vendorsVM.getVendorOrdersCount(context);
            });
            return () {};
          }, []);

          if (authVM.isLoading) {
            return const VendorDetailsCardLoading();
          }

          final isFree = authVM.currentVendor?.vendorType == VendorType.free;

          final isActive = isFree ||
              (vendorData?.expireDate?.isAfter(DateTime.now()) ?? true);

          final isExpired =
              vendorData?.expireDate?.isBefore(DateTime.now()) ?? true;

          return Column(
            children: [
              WaveContainer(
                colors: isActive
                    ? ColorManager.activeGradientBackground
                    : ColorManager.expiredGradientBackground,
                child: Column(
                  children: [
                    context.xLargeGap,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Builder(
                          builder: (context) => IconButton(
                            onPressed: () {
                              Scaffold.of(context).openDrawer();
                            },
                            icon: Icon(Icons.menu, color: ColorManager.white),
                          ),
                        ),
                        HomeStatusTextWidget(vendorData),
                        Consumer<NotificationVM>(
                          builder: (context, notificationsVM, child) {
                            return HookBuilder(builder: (context) {
                              useEffect(() {
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  notificationsVM.getNotifications(context);
                                });

                                return () {};
                              }, []);

                              return IconButton(
                                icon: Badge(
                                  isLabelVisible:
                                      notificationsVM.unReadNotifications > 0,
                                  label: notificationsVM.unReadNotifications > 0
                                      ? Text(
                                          notificationsVM.unReadNotifications
                                              .toString(),
                                          style: context.hint.copyWith(
                                              color: Colors.white,
                                              fontSize: 10),
                                        )
                                      : null,
                                  backgroundColor: ColorManager.red,
                                  child: IconWidget(
                                      icon: Assets.iconsNotifications,
                                      color: ColorManager.white),
                                ),
                                onPressed: () =>
                                    context.to(const NotificationScreen()),
                                color: Colors.black,
                              );
                            });
                          },
                        ),
                        // IconButton(
                        //   onPressed: () {
                        //
                        //     context.to(NotificationScreen());
                        //   },
                        //   icon: Icon(Icons.notifications_none_outlined,
                        //       color: ColorManager.white),
                        // ),
                      ],
                    ),
                    context.mediumGap,
                    if (isFree)
                      FreeVendorCardWidget(
                        canViewLinkActions: canViewLinkActions.value,
                      )
                    else if (isActive)
                      ActiveVendorCardWidget(vendorData: vendorData)
                    else if (isExpired)
                      const ExpiredVendorCardWidget(),
                  ],
                ),
              ),
            ],
          );
        });
      },
    );
  }
}
