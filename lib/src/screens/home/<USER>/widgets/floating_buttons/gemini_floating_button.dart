import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:lottie/lottie.dart';

import '../../../../../admin/vendors/view/vendors_screen/vendors_screen.dart';
import '../../../../gemini/view/gemini_screen.dart';

class GeminiFloatingButton extends StatelessWidget {
  const GeminiFloatingButton({super.key});

  @override
  Widget build(BuildContext context) {
    final isAdmin = VendorModelHelper.isAdmin();

    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: 35.h,
          width: kIsWeb ? 50.w : 120.w,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            // color: ColorManager.secondaryColor.withOpacity(0.8),
            gradient: const LinearGradient(
              colors: [Color(0xFF00B686), Color(0xFF00838F)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            // Theme.of(context).primaryColor,
            borderRadius: const BorderRadius.all(
              Radius.circular(50),
            ),

            boxShadow: [
              BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 7)),
              BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.4),
                  blurRadius: 10,
                  offset: const Offset(0, 3))
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            Text(
              isAdmin ? 'Vendors' : 'AI',
              style: context.whiteTitle.copyWith(
                fontFamily: 'Poppins',
              ),
            ),
            if (!isAdmin)
              Lottie.asset(
                'assets/animated/ai.json',
                fit: BoxFit.fill,
                height: 70.h,
              )
          ],
        ),
        Container(
          height: 35.h,
          width: 120.w,
          decoration: const BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.all(
              Radius.circular(50),
            ),
          ),
        ).onTap(() {
          context.to(isAdmin ? const VendorsScreen() : const GeminiScreen());
        }),
      ],
    ).sized(
        // width: 220.w,
        );
  }
}
