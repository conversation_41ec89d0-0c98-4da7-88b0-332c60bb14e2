class AboutVendorModel {
  final int? id;
  final String? documentId;
  final String about;
  final String privacyPolicy;
  final String whatsapp;
  final String facebookLink;
  final String instagramLink;
  final String tiktokLink;
  final String? youtubeLink;

  AboutVendorModel({
    this.id,
    this.documentId,
    this.about = '',
    this.privacyPolicy = '',
    this.whatsapp = '',
    this.facebookLink = '',
    this.instagramLink = '',
    this.tiktokLink = '',
    this.youtubeLink = '',
  });

  factory AboutVendorModel.fromJson(Map<String, dynamic> json) {
    return AboutVendorModel(
      id: json['id'],
      documentId: json['documentId'],
      about: json['about'] ?? '',
      privacyPolicy: json['privacy'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      facebookLink: json['facebook'] ?? '',
      instagramLink: json['instagram'] ?? '',
      tiktokLink: json['tiktok'] ?? '',
      youtubeLink: json['youtube'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        if (about.isNotEmpty) 'about': about,
        if (privacyPolicy.isNotEmpty) 'privacy': privacyPolicy,
        if (whatsapp.isNotEmpty) 'whatsapp': whatsapp,
        if (facebookLink.isNotEmpty) 'facebook': facebookLink,
        if (instagramLink.isNotEmpty) 'instagram': instagramLink,
        if (tiktokLink.isNotEmpty) 'tiktok': tiktokLink,
        if (youtubeLink != null && youtubeLink!.isNotEmpty)
          'youtube': youtubeLink,
      };
}
