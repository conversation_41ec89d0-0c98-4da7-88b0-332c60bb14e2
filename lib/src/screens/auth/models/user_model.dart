import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';

class UserModel {
  final int? id;
  final String? documentId;
  final String? username;
  final String? email;
  final String? displayName;
  final String? phone;
  final String? deviceToken;

  const UserModel(
      {this.documentId,
      this.username,
      this.id,
      this.email,
      this.displayName,
      this.phone,
      this.deviceToken});

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        documentId: json[ApiStrings.documentId],
        id: json[ApiStrings.id],
        username: json[ApiStrings.username] ?? '',
        email: json[ApiStrings.email] ?? '',
        phone: json[ApiStrings.phone] ?? '',
        displayName: json[ApiStrings.displayName] ?? '',
        deviceToken: json[ApiStrings.deviceToken] ?? '',
      );
}
