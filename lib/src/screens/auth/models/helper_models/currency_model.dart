import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';

class CurrencyModel {
  final int? id;
  final String? currencyEn;
  final String? currencyAr;
  final String? symbol;

  CurrencyModel({
    this.id,
    this.currencyEn,
    this.currencyAr,
    this.symbol,
  });

  factory CurrencyModel.fromJson(Map<String, dynamic> json) {
    final symbol = json[ApiStrings.symbol];

    return CurrencyModel(
      id: json[ApiStrings.id],
      currencyEn: symbol == null || symbol.isEmpty
          ? json[ApiStrings.currencyEn]
          : symbol,
      currencyAr: symbol == null || symbol.isEmpty
          ? json[ApiStrings.currencyAr]
          : symbol,
      symbol: symbol,
    );
  }

  Map<String, dynamic> toJson() => {
        ApiStrings.currencyEn: currencyEn,
        ApiStrings.currencyAr: currencyAr,
        ApiStrings.symbol: symbol,
      };
}
