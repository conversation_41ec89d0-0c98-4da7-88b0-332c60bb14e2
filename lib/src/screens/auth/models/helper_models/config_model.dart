import 'package:idea2app_vendor_app/src/screens/shipping/model/country_model.dart';

import '../../../../core/consts/api_strings.dart';
import 'currency_model.dart';

class ConfigModel {
  final int? id;
  final String? documentId;
  final int? vendorId;
  final List<CurrencyModel> currencies;
  final List<CountryModel> countries;
  final List<CurrencyModel> sizes;
  final bool showPricing;
  final bool isActiveWorkingTime;
  final num? minimumOrderCost;
  final bool? orderRingingBell;
  final String? primaryColor;
  final String? defaultLanguage;

  ConfigModel({
    this.id,
    this.documentId,
    this.vendorId,
    this.minimumOrderCost,
    this.currencies = const [],
    this.sizes = const [],
    this.countries = const [],
    this.showPricing = true,
    this.isActiveWorkingTime = true,
    this.orderRingingBell,
    this.primaryColor,
    this.defaultLanguage,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) {
    return ConfigModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      primaryColor: json[ApiStrings.primaryColor],
      defaultLanguage: json[ApiStrings.defaultLanguage] ?? 'en',
      minimumOrderCost: json[ApiStrings.minimumOrderCost] ?? 0,
      isActiveWorkingTime: json[ApiStrings.isActiveWorkingTime] ?? true,
      countries: json[ApiStrings.countries]
          .map<CountryModel>((e) => CountryModel.fromJson(e))
          .toList(),
      currencies: json[ApiStrings.currencies]
          .map<CurrencyModel>((e) => CurrencyModel.fromJson(e))
          .toList(),
      showPricing: json[ApiStrings.showPricing] ?? true,
      orderRingingBell: json[ApiStrings.orderRingingBell],
    );
  }

  Map<String, dynamic> toJson({
    bool fromLocal = false,
  }) {
    return {
      ApiStrings.currencies: fromLocal
          ? currencies.map((e) => e.toJson()).toList()
          : currencies.map((e) => e.id).toList(),
      ApiStrings.countries: fromLocal
          ? countries.map((e) => e.toJson()).toList()
          : countries.map((e) => e.id).toList(),
      if (vendorId != null) ApiStrings.vendor: vendorId,
      if (documentId != null) ApiStrings.documentId: documentId,
      ApiStrings.showPricing: showPricing,
      ApiStrings.isActiveWorkingTime: isActiveWorkingTime,
      if (minimumOrderCost != null)
        ApiStrings.minimumOrderCost: minimumOrderCost,
      if (orderRingingBell != null)
        ApiStrings.orderRingingBell: orderRingingBell,
      if (primaryColor != null) ApiStrings.primaryColor: primaryColor,
      if (defaultLanguage != null) ApiStrings.defaultLanguage: defaultLanguage,
    };
  }

  ConfigModel copyWith({
    int? id,
    String? documentId,
    int? vendorId,
    String? primaryColor,
    String? defaultLanguage,
    num? minimumOrderCost,
    List<CurrencyModel>? currencies,
    List<CurrencyModel>? sizes,
    List<CountryModel>? countries,
    bool? showPricing,
    bool? isActiveWorkingTime,
    bool? orderRingingBell,
  }) {
    return ConfigModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      vendorId: vendorId ?? this.vendorId,
      minimumOrderCost: minimumOrderCost ?? this.minimumOrderCost,
      currencies: currencies ?? this.currencies,
      primaryColor: primaryColor ?? this.primaryColor,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      sizes: sizes ?? this.sizes,
      countries: countries ?? this.countries,
      showPricing: showPricing ?? this.showPricing,
      isActiveWorkingTime: isActiveWorkingTime ?? this.isActiveWorkingTime,
      orderRingingBell: orderRingingBell ?? this.orderRingingBell,
    );
  }
}

class SizeModel {
  final String? name;
  final List? categories;

  SizeModel({
    this.name,
    this.categories,
  });

  factory SizeModel.fromJson(Map<String, dynamic> json) {
    return SizeModel(
      name: json[ApiStrings.name],
      categories: json[ApiStrings.name],
    );
  }
}
