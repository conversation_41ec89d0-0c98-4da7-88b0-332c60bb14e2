import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/data/local/local_keys.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:universal_platform/universal_platform.dart';

String getCurrentDeviceType() {
  return kIsWeb
      ? "Web"
      : Platform.isIOS
      ? "iOS"
      : "Android";
}

extension VendorModelHelper on VendorModel {
  static Map<String, dynamic> currentVendor() {
    final vendor = jsonDecode(
      GetStorageHandler.getLocalData(key: LocalKeys.vendorData) ?? '{}',
    );

    return vendor ?? {};
  }

  static VendorModel currentVendorModel() {
    final vendor = currentVendor();

    return VendorModel.fromJson(vendor);
  }

  static currentVendorId() {
    final vendor = currentVendor();

    final vendorId = vendor['id'];

    return vendorId;
  }

  static isAdmin() {
    final vendorName = currentVendorBusinessName();

    return vendorName == 'default';
  }

  static currentVendorBusinessName() {
    final vendor = currentVendor();

    final vendorId = vendor['business_name'];

    return vendorId;
  }

  static currentVendorDocumentId() {
    final vendor = currentVendor();

    final vendorId = vendor['documentId'];

    return vendorId;
  }

  static int currentVendorCountryId() {
    final vendor = currentVendor();

    final countries = (vendor['countries'] ?? []) as List;

    final countryId = countries.firstOrNull;

    log('COUNTRY_ID $countryId');

    return countryId ?? 1;
  }

  static String currentVendorType() {
    final vendor = currentVendor();

    final vendorType = vendor['type'] ?? 'free';

    return vendorType;
  }

  static VendorType getVendorType(String type) {
    switch (type) {
      case 'free':
        return VendorType.free;
      case 'deleted':
        return VendorType.deleted;
      case 'monthly':
        return VendorType.monthly;
      case 'quarter':
        return VendorType.quarter;
      case 'semester':
        return VendorType.semester;
      case 'annually':
        return VendorType.annually;
      default:
        return VendorType.free;
    }
  }

  static String vendorTypeToString(VendorType type) {
    switch (type) {
      case VendorType.free:
        return 'free';
      case VendorType.deleted:
        return 'deleted';
      case VendorType.monthly:
        return 'monthly';
      case VendorType.quarter:
        return 'quarter';
      case VendorType.semester:
        return 'semester';
      case VendorType.annually:
        return 'annually';
      default:
        return 'free';
    }
  }

  static String vendorTypePlan(VendorType? type, BuildContext context) {
    if (context.isEng) {
      switch (type) {
        case VendorType.free:
          return '${context.tr.free} ${context.tr.plan}';
        case VendorType.deleted:
          return '${context.tr.deleted} ${context.tr.plan}';
        case VendorType.monthly:
          return '${context.tr.monthly} ${context.tr.plan}';
        case VendorType.quarter:
          return '${context.tr.threeMonths} ${context.tr.plan}';
        case VendorType.semester:
          return '${context.tr.sixMonths} ${context.tr.plan}';
        case VendorType.annually:
          return '${context.tr.annually} ${context.tr.plan}';
        default:
          return '${context.tr.free} ${context.tr.plan}';
      }
    }
    switch (type) {
      case VendorType.free:
        return '${context.tr.plan} ${context.tr.free}';
      case VendorType.deleted:
        return '${context.tr.plan} ${context.tr.deleted}';
      case VendorType.monthly:
        return '${context.tr.plan} ${context.tr.monthly}';
      case VendorType.quarter:
        return '${context.tr.plan} ${context.tr.threeMonths}';
      case VendorType.semester:
        return '${context.tr.plan} ${context.tr.sixMonths}';
      case VendorType.annually:
        return '${context.tr.plan} ${context.tr.annually}';
      default:
        return '${context.tr.plan} ${context.tr.free}';
    }
  }
}
