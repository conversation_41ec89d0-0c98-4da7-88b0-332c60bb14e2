import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';

enum PricingType {
  web,
  android,
  ios,
  androidAndIos,
  fullPackage,
  oneTimePricing
}

extension PricingTypeStringExtension on PricingType {
  String get toPricingTypeIcon {
    switch (this) {
      case PricingType.web:
        return Assets.webSvg;
      case PricingType.android:
        return Assets.androidSvg;
      case PricingType.ios:
        return Assets.appleSvg;
      case PricingType.androidAndIos:
        return Assets.starsSvg;
      case PricingType.fullPackage:
        return Assets.starsSvg;
      case PricingType.oneTimePricing:
        return Assets.starsSvg;
    }
  }
}

class PricingModel {
  final int? id;
  final String? documentId;
  final String? name;
  final num? price;
  final num? priceDollar;
  final num? ordersCount;
  final num? extraOrderPrice;
  final String? nameAr;
  final String? description;
  final String? descriptionAr;
  final num? beforeSalePrice;
  final int? days;

  PricingModel(
      {this.id,
      this.documentId,
      this.name,
      this.price,
      this.priceDollar,
      this.ordersCount,
      this.extraOrderPrice,
      this.nameAr,
      this.description,
      this.descriptionAr,
      this.days,
      this.beforeSalePrice});

  PricingType get toPricingType {
    switch (name) {
      case 'Web':
        return PricingType.web;
      case 'Android - 3 Month':
      case 'Android - Monthly':
      case 'One Time Pricing - Android':
        return PricingType.android;
      case 'iOS - 3 Month':
      case 'iOS - Monthly':
      case 'One Time Pricing - iOS':
        return PricingType.ios;
      case 'Android & iOS - Monthly':
        return PricingType.androidAndIos;
      case 'Full Package - 3 Month':
      case 'Full Package - Monthly':
        return PricingType.fullPackage;
      case 'One Time Pricing':
      case 'Full Package - One Time Pricing':
        return PricingType.oneTimePricing;
      default:
        return PricingType.web;
    }
  }

  VendorType get toVendorType {
    switch (name) {
      case 'Free':
        return VendorType.free;

      case 'Monthly':
      case 'Android - Monthly':
      case 'iOS - Monthly':
      case 'Android & iOS - Monthly':
      case 'Full Package - Monthly':
        return VendorType.monthly;
      case 'Quarter':
      case 'Android - 3 Month':
      case 'iOS - 3 Month':
      case 'Full Package - 3 Month':
        return VendorType.quarter;
      case 'Semester':
        return VendorType.semester;
      case 'Annually':
        return VendorType.annually;
      default:
        return VendorType.free;
    }
  }

  factory PricingModel.fromJson(Map<String, dynamic> json) {
    return PricingModel(
        id: json[ApiStrings.id],
        documentId: json[ApiStrings.documentId],
        name: json[ApiStrings.name] ?? '',
        price: json['sale_price'] ?? 0,
        days: json['days'] ?? 0,
        priceDollar: json[ApiStrings.priceDollar] ?? 0,
        ordersCount: json[ApiStrings.ordersCount] ?? 0,
        extraOrderPrice: json[ApiStrings.extraOrderPrice] ?? 0,
        nameAr: json['name_ar'] ?? '',
        description: json['description'] ?? '',
        descriptionAr: json['description_ar'] ?? '',
        beforeSalePrice: json[ApiStrings.price] ?? 0);
  }

  Map<String, dynamic> toJson() => {
        ApiStrings.name: name,
        ApiStrings.price: price,
        ApiStrings.priceDollar: priceDollar,
        ApiStrings.ordersCount: ordersCount,
      };
}
