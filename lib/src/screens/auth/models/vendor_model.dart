import 'dart:developer';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/about_vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/config_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/pricing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/qr_landing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_advanced_model.dart';
import 'package:idea2app_vendor_app/src/screens/payment/model/payment_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/country_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/shipping_model.dart';

enum VendorType {
  free,
  deleted,
  monthly,
  quarter,
  semester,
  annually,
}

enum VendorStatus {
  all,
  active,
  expired,
}

class VendorModel {
  final String? documentId;
  final int? id;
  final String? password;
  final String? fcmToken;
  final String? name;
  final String? email;
  final bool? isActive;
  final String? address;
  final String? phone;
  final String? businessName;
  final BaseMediaModel? logo;
  final DateTime? createdAt;
  final DateTime? expireDate;
  final DateTime? startDate;
  final num? paidAmount;
  final List<CountryModel>? countries;
  final ShippingModel? shippings;
  final PricingModel? pricing;
  final PaymentModel? payments;
  final VendorType? vendorType;
  final String? businessType;
  final String? websiteLink;
  final ConfigModel? config;
  final AboutVendorModel? about;
  final QrLandingModel? qrLanding;
  final String? playStoreLink;
  final String? appStoreLink;
  final List<VendorWebViewFeaturesModel> vendorAdvanced;

  const VendorModel({
    this.documentId,
    this.id,
    this.password,
    this.shippings,
    this.payments,
    this.logo,
    this.createdAt,
    this.fcmToken,
    this.businessName,
    this.qrLanding,
    this.vendorType,
    this.countries,
    this.name,
    this.email,
    this.pricing,
    this.isActive = true,
    this.address,
    this.phone,
    this.businessType,
    this.websiteLink,
    this.expireDate,
    this.startDate,
    this.paidAmount,
    this.config,
    this.about,
    this.playStoreLink,
    this.appStoreLink,
    this.vendorAdvanced = const [],
  });

  bool get isFree => vendorType == VendorType.free;

  bool get showPricing => config?.showPricing ?? true;

  bool get isClothes =>
      businessType?.toLowerCase() == 'clothes' ||
      businessType?.toLowerCase() == 'others';

  bool get isMarket => businessType?.toLowerCase() == 'market';

  bool get isAccessories => businessType?.toLowerCase() == 'accessories';

  bool get isAppStoreOrPlayStoreLinkNotEmpty =>
      (playStoreLink?.isNotEmpty ?? false) ||
      (appStoreLink?.isNotEmpty ?? false);

  factory VendorModel.fromJson(Map<String, dynamic> json) {
    final vendorModelFeaturesList =
        json[ApiStrings.vendorWebViewFeatures] as List?;

    final vendorAdvanced = vendorModelFeaturesList
            ?.map((e) => VendorWebViewFeaturesModel.fromJson(e))
            .toList() ??
        [];

    return VendorModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      password: json[ApiStrings.password] ?? '',
      logo: json[ApiStrings.logo] != null
          ? BaseMediaModel.fromJson(json[ApiStrings.logo])
          : null,
      vendorType:
          VendorModelHelper.getVendorType(json[ApiStrings.type] ?? 'free'),
      config: json[ApiStrings.config] != null
          ? ConfigModel.fromJson(json[ApiStrings.config])
          : null,
      pricing: json[ApiStrings.pricing] != null
          ? PricingModel.fromJson(json[ApiStrings.pricing])
          : null,
      name: json[ApiStrings.name] ?? '',
      email: json[ApiStrings.email] ?? '',
      isActive: json[ApiStrings.active] ?? true,
      address: json[ApiStrings.address] ?? '',
      phone: json[ApiStrings.phone] ?? '',
      businessName: json[ApiStrings.businessName] ?? '',
      fcmToken: json[ApiStrings.deviceToken] ?? '',
      businessType: json[ApiStrings.businessType] ?? '',
      playStoreLink: json[ApiStrings.playStoreLink] ?? '',
      appStoreLink: json[ApiStrings.appStoreLink] ?? '',
      websiteLink: json[ApiStrings.websiteLink] == null ||
              json[ApiStrings.websiteLink].toString().isEmpty
          ? null
          : json[ApiStrings.websiteLink],

      paidAmount: json[ApiStrings.paidAmount] ?? 0.0,
      startDate: json[ApiStrings.startDate] != null
          ? DateTime.parse(json[ApiStrings.startDate]).toLocal()
          : null,
      expireDate: json[ApiStrings.expireDate] != null
          ? DateTime.parse(json[ApiStrings.expireDate]).toLocal()
          : null,
      createdAt: json[ApiStrings.createdAt] != null
          ? DateTime.parse(json[ApiStrings.createdAt]).toLocal()
          : null,
      about: json[ApiStrings.about] != null
          ? AboutVendorModel.fromJson(json[ApiStrings.about])
          : null,

      qrLanding: json[ApiStrings.qrLanding] != null
          ? QrLandingModel.fromJson(json[ApiStrings.qrLanding])
          : null,

      vendorAdvanced: vendorAdvanced,
      // countries: countries
    );
  }

  Map<String, dynamic> toJson({
    bool fromLocal = false,
  }) {
    return {
      // if (id != null) "id": id,
      if (documentId != null) "documentId": documentId,
      if (logo != null) "logo": logo?.toJson(),
      if (password != null && password!.isNotEmpty) "password": password,
      if (name != null && name!.isNotEmpty) "name": name,
      if (email != null && email!.isNotEmpty) "email": email,
      if (isActive != null) "active": isActive,
      "address": address,
      "phone": phone,
      if (fcmToken != null) "device_token": fcmToken,
      if (businessName != null && businessName!.isNotEmpty)
        "business_name": businessName!.endsWith('-')
            ? businessName!.substring(0, businessName!.length - 1)
            : businessName,
      if (businessType != null) "business_type": businessType,
      if (vendorType != null)
        "type": VendorModelHelper.vendorTypeToString(vendorType!),
      if (websiteLink != null) "website_link": websiteLink,
      if (config != null)
        "config": fromLocal ? config!.toJson(fromLocal: true) : config!.id,
      if (shippings != null)
        "shippings": fromLocal ? shippings!.toJson() : [shippings!.id],
      if (pricing != null)
        "pricing": fromLocal ? pricing!.toJson() : [pricing?.id],
      if (payments != null)
        "payments": fromLocal ? payments!.toJson() : [payments!.id],
      "device_type": getCurrentDeviceType(),
      if (expireDate != null) "expire_date": expireDate?.toIso8601String(),
      if (startDate != null) "start_date": startDate?.toIso8601String(),
      if (paidAmount != null) "paid_amount": paidAmount,
      if (about != null) "about": about?.toJson(),
      if (createdAt != null && fromLocal)
        "createdAt": createdAt?.toIso8601String(),
      if (qrLanding != null) "qr_landing": qrLanding?.toJson(),
      if (playStoreLink != null) ApiStrings.playStoreLink: playStoreLink,
      if (appStoreLink != null) ApiStrings.appStoreLink: appStoreLink,

      // if (vendorAdvanced.isNotEmpty)
      //   ApiStrings.vendorWebViewFeatures:
      //       vendorAdvanced.map((e) => e.toJson()).toList(),
    };
  }

  //? Copy with
  VendorModel copyWith({
    String? documentId,
    int? id,
    String? password,
    String? fcmToken,
    String? name,
    String? email,
    bool? isActive,
    String? address,
    String? phone,
    String? businessName,
    BaseMediaModel? logo,
    DateTime? expireDate,
    List<CountryModel>? countries,
    ShippingModel? shippings,
    PaymentModel? payments,
    VendorType? vendorType,
    String? businessType,
    String? websiteLink,
    ConfigModel? config,
    PricingModel? pricing,
    DateTime? startDate,
    num? paidAmount,
    AboutVendorModel? about,
    DateTime? createdAt,
    QrLandingModel? qrLanding,
    String? playStoreLink,
    String? appStoreLink,
    List<VendorWebViewFeaturesModel>? vendorAdvanced,
  }) {
    return VendorModel(
      documentId: documentId ?? this.documentId,
      id: id ?? this.id,
      password: password ?? this.password,
      fcmToken: fcmToken ?? this.fcmToken,
      qrLanding: qrLanding ?? this.qrLanding,
      name: name ?? this.name,
      email: email ?? this.email,
      isActive: isActive ?? this.isActive,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      businessName: businessName ?? this.businessName,
      logo: logo ?? this.logo,
      expireDate: expireDate ?? this.expireDate,
      countries: countries ?? this.countries,
      shippings: shippings ?? this.shippings,
      payments: payments ?? this.payments,
      vendorType: vendorType ?? this.vendorType,
      businessType: businessType ?? this.businessType,
      websiteLink: websiteLink ?? this.websiteLink,
      config: config ?? this.config,
      paidAmount: paidAmount ?? this.paidAmount,
      startDate: startDate ?? this.startDate,
      pricing: pricing ?? this.pricing,
      about: about ?? this.about,
      createdAt: createdAt ?? this.createdAt,
      playStoreLink: playStoreLink ?? this.playStoreLink,
      appStoreLink: appStoreLink ?? this.appStoreLink,
      vendorAdvanced: vendorAdvanced ?? this.vendorAdvanced,
    );
  }
}
