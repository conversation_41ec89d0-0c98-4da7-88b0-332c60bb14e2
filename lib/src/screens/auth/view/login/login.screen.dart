import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/common/base_safe_area.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:provider/provider.dart';

import '../../../../../generated/assets.dart';
import '../../../../core/consts/app_constants.dart';
import '../../../drawer/widgets/settings_widgets.dart';
import '../../../shared/app_settings/view_model/app_settings_view_model.dart';
import 'widgets/login_body.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        exit(0);
      },
      child: Consumer<AppSettingsVM>(
        builder: (context, settingsVM, child) {
          return Scaffold(
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              title: SettingsWidget(
                iconPath: Assets.iconsLanguage,
                header: context.tr.languages,
                trailingWidget: BaseDropDown<Locale>(
                  value: settingsVM.locale,
                  onChanged: (value) {
                    settingsVM.updateLanguage(value!);
                  },
                  data: AppConsts.supportedLocales
                      .map((loc) => DropdownMenuItem(
                            value: loc,
                            child: Text(
                              context.langText(loc.languageCode),
                              style: context.labelMedium,
                            ),
                          ))
                      .toList(),
                ).sized(height: 25.h),
              ),
              automaticallyImplyLeading: false,
            ),
            body: const BaseSafeArea(
              child: LoginBody(),
            ),
          );
        },
      ),
    );
  }
}
