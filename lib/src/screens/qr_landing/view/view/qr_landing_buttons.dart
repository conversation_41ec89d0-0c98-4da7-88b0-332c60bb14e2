import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

class QrLandingButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final Function() onPressed;
  const QrLandingButton(
      {super.key,
      required this.title,
      required this.icon,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: ButtonStyle(
            padding: WidgetStatePropertyAll(
                EdgeInsets.symmetric(horizontal: AppSpaces.smallPadding)),
            backgroundColor: WidgetStatePropertyAll(ColorManager.primaryColor)),
        onPressed: onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
                backgroundColor: ColorManager.grey.withOpacity(.4),
                radius: 15,
                child: Icon(icon, color: Colors.white, size: 20)),
            context.smallGap,
            Text(
              title,
              style: context.whiteLabelLarge,
            )
          ],
        ));
  }
}
