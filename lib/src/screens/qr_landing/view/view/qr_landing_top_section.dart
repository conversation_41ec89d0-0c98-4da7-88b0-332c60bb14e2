import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/buttons/base_text_button.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/qr_landing/view/view/qr_landing_buttons.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../core/resources/theme/theme.dart';
import '../../../settings/view/contact_us.dart';

class QrLandingTopSection extends HookWidget {
  const QrLandingTopSection({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final vendor = authVM.currentVendor;
    final link = vendor?.qrLanding?.link ??
        'https://qr.idea2app.tech/${authVM.currentVendor?.businessName}';

    final showLogo = useState(true);
    final logoSizes = [ context.tr.large, context.tr.small];
    final logoSize = useState<String>(logoSizes[0]);

    final qrCircleKey = GlobalKey();
    final qrSquareKey = GlobalKey();
    final qrTabIndex = useState(0);

    final tabsList = [
      context.tr.qrCircle,
      context.tr.qrSquare,
    ];

    return Container(
      padding: EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.longFieldRadius),
        color: context.isDark
            ? Colors.grey.withOpacity(.1)
            : Colors.grey.withOpacity(0.15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (authVM.isLoading)
            SizedBox(
              height: 50,
            )
          else
            Center(
              child: BaseTextButton(
                withUnderline: true,
                title: link,
                onTap: () => openURL(link),
              ),
            ),
          Row(
            children: [
              Expanded(
                child: QrLandingButton(
                  title: context.tr.qr,
                  icon: Icons.qr_code,
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) {
                        return Container(
                          padding: const EdgeInsets.all(16.0),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: context.appTheme.cardColor,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(AppRadius.tabBarRadius),
                              topRight: Radius.circular(AppRadius.tabBarRadius),
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (qrTabIndex.value == 1)
                                RepaintBoundary(
                                  key: qrSquareKey,
                                  child: Container(
                                    padding:
                                        EdgeInsets.all(AppSpaces.mediumPadding),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(
                                        AppRadius.baseRadius,
                                      ),
                                    ),
                                    height: 300,
                                    child: PrettyQrView.data(
                                      data: link,
                                      decoration: showLogo.value
                                          ? PrettyQrDecoration(
                                              image: PrettyQrDecorationImage(
                                                fit: BoxFit.contain,

                                                position:
                                                    PrettyQrDecorationImagePosition
                                                        .foreground,


                                                scale: logoSize.value ==
                                                        context.tr.large
                                                    ? .3 
                                                    : .15,
                                                image: NetworkImage(
                                                  authVM.currentVendor?.logo 
                                                          ?.url ??
                                                      'https://www.shutterstock.com/image-illustration/big-size-text-written-on-600nw-2213200223.jpg',
                                              
                                                ),
                                              ),
                                            )
                                          : null,
                                    ),
                                  ),
                                )
                              else
                                RepaintBoundary(
                                    key: qrCircleKey,
                                    child: QrImageView(
                                      data: link,
                                      size: 280.0,
                                      backgroundColor: Colors.white,
                                      errorCorrectionLevel:
                                          QrErrorCorrectLevel.Q,
                                      eyeStyle: const QrEyeStyle(
                                        color: Colors.black,
                                      ),
                                      gapless: false,
                                      dataModuleStyle: const QrDataModuleStyle(
                                        dataModuleShape:
                                            QrDataModuleShape.circle,
                                        color: Colors.black87,
                                      ),
                                    )),
                              context.mediumGap,
                              QrLandingButton(
                                title: context.tr.share,
                                icon: Icons.share,
                                onPressed: () {
                                  shareQrCode(
                                    context: context,
                                    link: '',
                                    qrKey: qrTabIndex.value == 0
                                        ? qrCircleKey
                                        : qrSquareKey,
                                  );
                                },
                              )
                            ],
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
              context.smallGap,
              Expanded(
                child: QrLandingButton(
                  title: context.tr.share,
                  icon: CupertinoIcons.arrow_turn_up_right,
                  onPressed: () => Share.share(link),
                ),
              ),
            ],
          ),
          context.mediumGap,
          Text(
            context.tr.qrSettings,
            style: context.labelLarge,
          ),
          DefaultTabController(
              length: 2,
              child: TabBar(
                  onTap: (index) {
                    qrTabIndex.value = index;
                  },
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorColor: context.appTheme.primaryColorDark,
                  dividerColor: ColorManager.textFieldColor(context),
                  tabs: tabsList
                      .map(
                        (e) => Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(e, style: context.labelLarge),
                        ),
                      )
                      .toList())),
          if (qrTabIndex.value == 1) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.tr.showLogo,
                  style: context.labelMedium,
                ),
                SwitchButtonWidget(
                  value: showLogo,
                  onChanged: (value) {
                    showLogo.value = value;
                  },
                ),
              ],
            ),
            if (showLogo.value)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.tr.logoSize,
                    style: context.labelMedium,
                  ),
                  BaseDropDown<String>(
                    onChanged: (value) {
                      logoSize.value = value;
                    },
                    data: logoSizes
                        .map((e) => DropdownMenuItem(
                              value: e,
                              child: Text(e),
                            ))
                        .toList(),
                    value: logoSize.value,
                  ),
                ],
              ),
          ]
        ],
      ),
    );
  }
}
