import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/qr_landing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/qr_landing/view/view/qr_landing_top_section.dart'
    show QrLandingTopSection;
import 'package:provider/provider.dart';

import '../../auth/models/helper_models/vendor_helper_model.dart';

class QrLandingScreen extends HookWidget {
  const QrLandingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final vendorQrLanding = VendorModelHelper.currentVendorModel().qrLanding;

    final qrType = useState<QrType?>(vendorQrLanding?.type ?? QrType.social);

    final titleController =
        useTextEditingController(text: vendorQrLanding?.title);
    final descriptionController = useTextEditingController(
      text: vendorQrLanding?.description,
    );

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.qrLanding,
        haveBackButton: true,
      ),
      body: Consumer<AuthVM>(
        builder: (context, authVM, child) {
          return Form(
            child: ListView(
              padding: EdgeInsets.all(AppSpaces.mediumPadding),
              children: [
                QrLandingTopSection(),
                context.mediumGap,
                Text(context.tr.qrType, style: context.labelLarge),
                context.smallGap,
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
                  decoration: BoxDecoration(
                      color: ColorManager.textFieldColor(context),
                      borderRadius: BorderRadius.circular(
                          AppRadius.extraLargeContainerRadius)),
                  child: BaseDropDown(
                      hint: context.tr.chooseQRType,
                      onChanged: (value) {
                        qrType.value = value;
                      },
                      isExpanded: true,
                      data: QrType.values
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text(e.name.capitalizeFirstLetter()),
                            ),
                          )
                          .toList(),
                      value: qrType.value),
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.title,
                  hint: context.tr.title,
                  withOptional: true,
                  isRequired: false,
                  controller: titleController,
                ),
                context.fieldsGap,
                BaseTextField(
                  title: context.tr.description,
                  hint: context.tr.description,
                  isRequired: false,
                  withOptional: true,
                  maxLines: 4,
                  radius: AppRadius.baseRadius,
                  controller: descriptionController,
                ),
                context.largeGap,
                Button(
                  label: context.tr.save,
                  isLoading: authVM.isLoading,
                  onPressed: () async {
                    final qrLanding = QrLandingModel(
                      title: titleController.text,
                      description: descriptionController.text,
                      type: qrType.value ?? QrType.social,
                    );

                    final vendor = authVM.currentVendor?.copyWith(
                      qrLanding: qrLanding,
                    );

                    await authVM.updateVendorData(context,
                        vendor: vendor!, isBack: false);
                  },
                )
              ],
            ),
          );
        },
      ),
    );
  }
}

// Widget buildClipboardIcon() {
//   final authVM = useContext().read<AuthVM>();
//   return InkWell(
//     onTap: () {
//       if (authVM.currentVendor?.websiteLink != null) {
//         Clipboard.setData(
//             ClipboardData(text: authVM.currentVendor!.websiteLink!));
//       }
//     },
//     child: CircleAvatar(
//       radius: 15.r,
//       backgroundColor: ColorManager.cardColor,
//       child: Icon(
//         CupertinoIcons.doc_on_clipboard,
//         size: 17.r,
//         color: ColorManager.black,
//       ),
//     ),
//   );
// }
