import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';
import '../../models/category_model.dart';
import 'widgets/add_main_category_fields.dart';

class AddMainCategoryScreen extends HookWidget {
  final bool isFromStepsDialog;

  const AddMainCategoryScreen({super.key, this.isFromStepsDialog = false});

  @override
  Widget build(BuildContext context) {
    final englishNameController = useTextEditingController();
    final arabicNameController = useTextEditingController();

    Log.w("Building AddMainCategoryScreen");

    final mainCategoryVM = context.read<MainCategoryVM>();
    final categoryVM = context.read<CategoryVM>();
    final mediaVM = context.read<MediaVM>();
    final formKey = useState(GlobalKey<FormState>());
    final selectedSubCategories = useState<List<CategoryModel>>([]);

    void addMainCategory() async {
      Log.w(
          "arabic Name ${arabicNameController.text}, englishName ${englishNameController.text}");
      if (englishNameController.text.isEmpty &&
          arabicNameController.text.isEmpty) {
        context.showBarMessage(context.tr.youMustAddOneOfThoseNames,
            isError: true);
        return;
      }

      await mainCategoryVM.addMainCategory(
        context,
        name: englishNameController.text,
        arabicName: arabicNameController.text,
        pickedImage: mediaVM.filesPaths.first,
        subCategories: selectedSubCategories.value,
      );

      mediaVM.clearFiles();
      categoryVM.getCategories(context, withLoading: true);
    }

    void validateAndAddMainCategory() async {
      if (mediaVM.filesPaths.isEmpty) {
        context.showBarMessage(
          context.tr.pleasePickImage,
          isError: true,
        );
        return;
      }

      if (!formKey.value.currentState!.validate()) {
        return;
      }

      if (selectedSubCategories.value.isEmpty) {
        context.showBarMessage(
          context.tr.pleaseSelectSubCategories,
          isError: true,
        );
        return;
      }

      addMainCategory();
    }

    return WillPopScope(
      onWillPop: () {
        context.read<MediaVM>().clearFiles();
        return Future.value(true);
      },
      child: Form(
        key: formKey.value,
        child: Consumer<MainCategoryVM>(
          builder: (context, mainCategoryVM, child) {
            return Column(
              children: [
                Column(
                  children: [
                    // Add Main Category Widgets (Image, Name)
                    AddMainCategoryFields(
                      englishNameController: englishNameController,
                      arabicNameController: arabicNameController,
                    ),

                    context.largeGap,

                    CategoriesDropDown(
                      label: context.tr.subCategories,
                      selectedMultiCategories: selectedSubCategories,
                      documentIds: [],
                      isMulti: true,
                      categoriesList: categoryVM.categories,
                    ),

                    context.xLargeGap,

                    // Add Main Category Button
                    Button(
                      isLoading: mainCategoryVM.isLoading,
                      label: context.tr.add,
                      onPressed: validateAndAddMainCategory,
                    )
                  ],
                ).paddingAll(AppSpaces.mediumPadding)
              ],
            );
          },
        ),
      ),
    );
  }
}
