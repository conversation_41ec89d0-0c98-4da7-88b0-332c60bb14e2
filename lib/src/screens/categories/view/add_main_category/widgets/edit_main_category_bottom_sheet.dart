import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';
import '../../../models/category_model.dart';
import '../../../view_model/category_view_model.dart';
import 'add_main_category_fields.dart';

class EditMainCategoryBottomSheet extends HookWidget {
  final MainCategoryModel category;

  const EditMainCategoryBottomSheet({
    Key? key,
    required this.category,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final englishNameController =
        useTextEditingController(text: category.englishName);
    final arabicNameController =
        useTextEditingController(text: category.arabicName);

    final mainCategoryVM = context.read<MainCategoryVM>();
    final mediaVM = context.read<MediaVM>();
    final formKey = useState(GlobalKey<FormState>());
    final selectedSubCategories =
        useState<List<CategoryModel>>(List.from(category.categories));

    final categoryVM = context.read<CategoryVM>();

    void editMainCategory() async {
      Log.w(
          "arabic Name ${arabicNameController.text}, englishName ${englishNameController.text}");
      if (englishNameController.text.isEmpty &&
          arabicNameController.text.isEmpty) {
        context.showBarMessage(context.tr.youMustAddOneOfThoseNames,
            isError: true);
        return;
      }

      await mainCategoryVM.editMainCategory(
        context,
        cat: category.copyWith(
          englishName: englishNameController.text,
          arabicName: arabicNameController.text,
          categories: selectedSubCategories.value,
        ),
        pickedImage: mediaVM.filesPaths.firstOrNull,
      );

      mediaVM.clearFiles();
      categoryVM.getCategories(context);
    }

    void validateAndEditMainCategory() async {
      if (!formKey.value.currentState!.validate()) {
        return;
      }

      editMainCategory();
    }

    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: context.appTheme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.baseContainerRadius),
          topRight: Radius.circular(AppRadius.baseContainerRadius),
        ),
      ),
      child: Form(
        key: formKey.value,
        child: Consumer<MainCategoryVM>(
          builder: (context, mainCategoryVM, child) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 5,
                    margin: EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),

                  // Title
                  Text(
                    context.tr.editMainCategory,
                    style: context.title,
                  ).paddingOnly(bottom: AppSpaces.mediumPadding),

                  // Edit Main Category Widgets (Image, Name)
                  AddMainCategoryFields(
                    englishNameController: englishNameController,
                    arabicNameController: arabicNameController,
                    category: category,
                  ),

                  context.largeGap,

                  CategoriesDropDown(
                    label: context.tr.subCategories,
                    selectedMultiCategories: selectedSubCategories,
                    documentIds: category.categories
                        .map((e) => e.documentId ?? '')
                        .toList(),
                    isMulti: true,
                  ),

                  context.xLargeGap,

                  // Edit Main Category Button
                  Button(
                          isLoading: mainCategoryVM.isLoading,
                          label: context.tr.edit,
                          onPressed: validateAndEditMainCategory)
                      .paddingOnly(bottom: AppSpaces.largePadding),
                ],
              ).paddingAll(AppSpaces.mediumPadding),
            );
          },
        ),
      ),
    );
  }
}
