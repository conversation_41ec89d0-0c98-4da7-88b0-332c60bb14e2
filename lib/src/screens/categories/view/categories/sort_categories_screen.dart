// import 'package:flutter/material.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
// import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
//
// class SortCategoriesScreen extends StatefulWidget {
//   final List<CategoryModel> categories;
//   const SortCategoriesScreen({super.key, required this.categories});
//
//   @override
//   State<SortCategoriesScreen> createState() => _SortCategoriesScreenState();
// }
//
// class _SortCategoriesScreenState extends State<SortCategoriesScreen> {
//   late List<CategoryModel> sortedCategories;
//
//   @override
//   void initState() {
//     super.initState();
//     sortedCategories = List.from(widget.categories);
//   }
//
//   void _onReorder(int oldIndex, int newIndex) {
//     setState(() {
//       if (oldIndex < newIndex) {
//         newIndex -= 1;
//       }
//       final item = sortedCategories.removeAt(oldIndex);
//       sortedCategories.insert(newIndex, item);
//
//       // Update sort numbers
//       for (var i = 0; i < sortedCategories.length; i++) {
//         sortedCategories[i] = sortedCategories[i].copyWith(sortNumber: i + 1);
//       }
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(context.tr.sortCategories, style: context.title),
//         actions: [
//           TextButton(
//             onPressed: () {
//               Navigator.pop(context, sortedCategories);
//             },
//             child: Text(context.tr.save, style: context.labelMedium),
//           ),
//         ],
//       ),
//       body: Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.all(AppSpaces.mediumPadding),
//             child: Text(
//               context.tr.sortCategoriesDescription,
//               style: context.labelMedium,
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             child: ReorderableListView(
//               padding: EdgeInsets.all(AppSpaces.mediumPadding),
//               onReorder: _onReorder,
//               children: sortedCategories.indexed
//                   .map((e) => ListTile(
//                         key: ValueKey(e.$2.documentId),
//                         leading: Container(
//                           width: 40,
//                           height: 40,
//                           decoration: BoxDecoration(
//                             color:
//                                 context.appTheme.primaryColor.withOpacity(0.1),
//                             shape: BoxShape.circle,
//                           ),
//                           child: Center(
//                             child: Text(
//                               (e.$1 + 1).toString(),
//                               style: context.labelMedium.copyWith(
//                                 color: context.appTheme.primaryColor,
//                               ),
//                             ),
//                           ),
//                         ),
//                         title: Text(
//                           e.$2.englishName ?? '',
//                           style: context.labelMedium,
//                         ),
//                         trailing: Icon(
//                           Icons.drag_handle,
//                           color: context.appTheme.primaryColor,
//                         ),
//                       ))
//                   .toList(),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
