import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/categories_without_main.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/category_search_header.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/empty_search_results.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/categories/widgets/main_category_section.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/theme/theme.dart';
import '../../base_add_category_screen.dart';
import 'category_search_header.dart';

class CategoryGridView extends HookWidget {
  const CategoryGridView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final searchController = useTextEditingController();

    return Consumer2<CategoryVM, MainCategoryVM>(
      builder: (context, categoryVM, mainCategoryVM, child) {
        //* Separate categories into those with main_category and those without
        final categoriesWithMain = categoryVM.categories
            .where((cat) => cat.mainCategory != null)
            .toList();

        final categoriesWithoutMain = categoryVM.categories
            .where((cat) => cat.mainCategory == null)
            .toList();

        final searchedWithoutMain = categoryVM.searchedCategories.isNotEmpty
            ? categoryVM.searchedCategories
                .where((cat) => cat.mainCategory == null)
                .toList()
            : categoriesWithoutMain;

        //* Group categories by main category
        final Map<int?, List<CategoryModel>> groupedByMainCategory = {};
        if (categoryVM.searchedCategories.isEmpty ||
            searchController.text.isEmpty) {
          for (var category in categoriesWithMain) {
            final mainCategoryId = category.mainCategory?.id;
            if (!groupedByMainCategory.containsKey(mainCategoryId)) {
              groupedByMainCategory[mainCategoryId] = [];
            }
            groupedByMainCategory[mainCategoryId]!.add(category);
          }
        } else {
          for (var category in categoryVM.searchedCategories
              .where((cat) => cat.mainCategory != null)) {
            final mainCategoryId = category.mainCategory?.id;
            if (!groupedByMainCategory.containsKey(mainCategoryId)) {
              groupedByMainCategory[mainCategoryId] = [];
            }
            groupedByMainCategory[mainCategoryId]!.add(category);
          }
        }

        if (categoryVM.categories.isEmpty || categoryVM.isLoading) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              EmptyDataWidget(
                isLoading: categoryVM.isLoading,
                message: context.tr.noCategories,
              ),
              if (!categoryVM.isLoading)
                Padding(
                  padding: EdgeInsets.all(AppSpaces.mediumPadding),
                  child: categoriesScreenButtonWidget(
                    context,
                    onPressed: () {
                      context.to(const BaseAddCategoryScreen());
                    },
                    text: context.tr.addCategory,
                    icon: CircleAvatar(
                      radius: 8.r,
                      backgroundColor: ColorManager.primaryColor,
                      child: Icon(
                        Icons.add,
                        color: ColorManager.white,
                        size: 14.r,
                      ),
                    ),
                  ),
                ),
            ],
          );
        }

        if (categoryVM.searchedCategories.isEmpty &&
            searchController.text.isNotEmpty) {
          return EmptySearchResults(
            searchController: searchController,
            categoryVM: categoryVM,
          );
        }

        return ListView(
          children: [
            //! Search
            CategorySearchHeader(
              searchController: searchController,
              categoryVM: categoryVM,
            ),
            context.mediumGap,

            if (groupedByMainCategory.isNotEmpty) ...[
              //* Main Category List
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: groupedByMainCategory.length,
                itemBuilder: (context, index) {
                  final mainCategoryId =
                      groupedByMainCategory.keys.elementAt(index);
                  final categoriesInGroup =
                      groupedByMainCategory[mainCategoryId]!;
                  final mainCategory = categoriesInGroup.first.mainCategory!;

                  return MainCategorySection(
                    mainCategory: mainCategory,
                    categoriesInGroup: categoriesInGroup,
                    mainCategoryVM: mainCategoryVM,
                  );
                },
              ),
              Divider(),
              context.smallGap,

              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
                child: Text(context.tr.categoriesWithoutMainCategory,
                    style: context.subTitle),
              ),
              context.smallGap,
            ],

            //! categories without main category
            CategoriesWithoutMain(categories: searchedWithoutMain),
          ],
        );
      },
    );
  }
}
