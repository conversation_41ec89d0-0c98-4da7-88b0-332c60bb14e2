import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/add_main_category/widgets/edit_main_category_bottom_sheet.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart' hide AppSpaces, AppRadius;

import '../../../view_model/category_view_model.dart';
import 'category_card_widget.dart';

class MainCategorySection extends StatelessWidget {
  final MainCategoryModel mainCategory;
  final List<CategoryModel> categoriesInGroup;
  final MainCategoryVM mainCategoryVM;

  const MainCategorySection({
    super.key,
    required this.mainCategory,
    required this.categoriesInGroup,
    required this.mainCategoryVM,
  });

  @override
  Widget build(BuildContext context) {
    final categoryVM = context.read<CategoryVM>();

    final deleteTitle = context.tr.areYouSureYouWantToDeleteThisMainCategory(
        mainCategory.nameByLang(context));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
          child: Column(
            children: [
              Row(
                children: [
                  BaseCachedImage(
                    mainCategory.featureImage?.url ?? '',
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                    radius: AppRadius.mainContainerRadius,
                  ),
                  context.smallGap,
                  Text(
                    context.isEng
                        ? mainCategory.englishName ?? context.tr.mainCategory
                        : mainCategory.arabicName ?? context.tr.mainCategory,
                    style: context.subTitle,
                  ),
                  Spacer(),
                  mainCategoryActionButtonWidget(
                    context: context,
                    onTap: () {
                      _showEditMainCategoryBottomSheet(context, mainCategory);
                    },
                    text: context.tr.edit,
                    icon: Icons.edit,
                  ),
                  context.smallGap,
                  mainCategoryActionButtonWidget(
                    context: context,
                    color: ColorManager.errorColor,
                    onTap: () {
                      showAlertDialog(
                        context,
                        child: Text(deleteTitle, style: context.labelLarge),
                        isLoading: mainCategoryVM.isLoading,
                        onConfirm: () async {
                          await mainCategoryVM.deleteMainCategory(
                            context,
                            category: mainCategory,
                          );

                          categoryVM.getCategories(context, withLoading: true);
                        },
                      );
                    },
                    text: context.tr.delete,
                    icon: Icons.delete,
                  )
                ],
              ),
              Divider(),
            ],
          ),
        ),
        context.mediumGap,
        SizedBox(
          height: 200,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
            itemCount: categoriesInGroup.length,
            separatorBuilder: (context, index) => context.mediumGap,
            itemBuilder: (context, index) {
              final category = categoriesInGroup[index];
              return SizedBox(
                width: 200,
                child: CategoryCardWidget(category: category),
              );
            },
          ),
        ),
        context.largeGap,
      ],
    );
  }

  void _showEditMainCategoryBottomSheet(
      BuildContext context, MainCategoryModel category) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditMainCategoryBottomSheet(category: category),
    );
  }
}

Widget mainCategoryActionButtonWidget({
  required BuildContext context,
  required VoidCallback onTap,
  required String text,
  required IconData icon,
  Color? color,
}) {
  return CircleAvatar(
    radius: 15.r,
    backgroundColor: color ?? ColorManager.darkGrey.withAlpha(51),
    child: IconButton(
      icon: Icon(icon, size: 16.r),
      onPressed: onTap,
      color: ColorManager.white,
    ),
  ).frosted(
    blur: 0.9,
    frostColor: color ?? Colors.grey.withAlpha(25),
    borderRadius: BorderRadius.circular(20),
  );
}
