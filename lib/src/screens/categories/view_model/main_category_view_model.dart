import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/bottom_nav_provider.dart';
import 'package:provider/provider.dart';

import '../../home/<USER>/main_screen.dart';
import '../models/category_model.dart';
import '../repository/main_category_repository.dart';

class MainCategoryVM extends BaseVM {
  MainCategoryRepository mainCategoryRepository;

  MainCategoryVM(this.mainCategoryRepository);

  var mainCategories = <MainCategoryModel>[];

  var searchedMainCategories = <MainCategoryModel>[];

  int tabIndex = 0;

  void setTabIndex(int index) {
    tabIndex = index;

    notifyListeners();
  }

  getSearchedMainCategories(String query) {
    searchedMainCategories = mainCategories
        .where((element) =>
            (element.englishName?.toLowerCase().contains(query.toLowerCase()) ??
                false) ||
            (element.arabicName?.toLowerCase().contains(query.toLowerCase()) ??
                false))
        .toList();

    notifyListeners();
  }

  void clearMainCategories() {
    mainCategories.clear();

    notifyListeners();
  }

  bool isCategoriesNotEmpty = false;

  //! Get main categories ====================================
  Future<void> getMainCategories(
    BuildContext context, {
    bool withLoading = true,
  }) async {
    await baseFunction(context, () async {
      mainCategories = await mainCategoryRepository.getMainCategories();
    }, isLoading: withLoading);
  }

  //! Add category ====================================
  Future<void> addMainCategory(BuildContext context,
      {required String name,
      required String arabicName,
      required List<CategoryModel> subCategories,
      required String pickedImage}) async {
    await baseFunction(
      context,
      () async {
        final cat = MainCategoryModel(
          arabicName: arabicName,
          englishName: name,
          categories: subCategories,
        );

        await mainCategoryRepository.addMainCategory(
            cat: cat, pickedImage: pickedImage);
      },
      type: FlushBarType.add,
    );
  }

  //! Edit category ====================================
  Future<void> editMainCategory(BuildContext context,
      {required MainCategoryModel cat, required String? pickedImage}) async {
    await baseFunction(
      context,
      () async {
        await mainCategoryRepository.editMainCategory(
            cat: cat, pickedImage: pickedImage);
      },
      type: FlushBarType.update,
    );
  }

  //! Delete category ====================================
  Future<void> deleteMainCategory(BuildContext context,
      {required MainCategoryModel category}) async {
    await baseFunction(
      context,
      () async {
        context.back();

        await mainCategoryRepository.deleteMainCategory(category: category);
      },
      type: FlushBarType.delete,
    );
  }

  void _navigateToCatScreen(BuildContext context) {
    context.read<BottomNavbarVM>().setCurrentIndex(2);
    context.toReplacement(const MainScreen());
  }
}
