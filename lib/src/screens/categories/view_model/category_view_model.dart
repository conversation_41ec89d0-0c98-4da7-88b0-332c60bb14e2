import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/bottom_nav_provider.dart';
import 'package:provider/provider.dart';

import '../../home/<USER>/main_screen.dart';
import '../models/category_model.dart';
import '../repository/category_repository.dart';

class CategoryVM extends BaseVM {
  CategoryRepository categoryRepository;

  CategoryVM(this.categoryRepository);

  var categories = <CategoryModel>[];

  var searchedCategories = <CategoryModel>[];

  bool get isThereOneMainCategory =>
      categories.any((element) => element.mainCategory != null);

  getSearchedCategories(String query) {
    searchedCategories = categories
        .where((element) =>
            (element.englishName?.toLowerCase().contains(query.toLowerCase()) ??
                false) ||
            (element.arabicName?.toLowerCase().contains(query.toLowerCase()) ??
                false))
        .toList();

    notifyListeners();
  }

  void clearCategories() {
    categories.clear();

    notifyListeners();
  }

  bool isCategoriesNotEmpty = false;

  //! Get categories ====================================
  Future<void> getCategories(
    BuildContext context, {
    bool withLoading = true,
  }) async {
    await baseFunction(context, () async {
      categories = await categoryRepository.getCategories();

      isAllCategoriesSortNumberEqualNull();
      addSortNumbersToCategories(categories);
    }, isLoading: withLoading);
  }

  //! Add category ====================================
  Future<void> addCategory(BuildContext context,
      {required String name,
      required String arabicName,
      required String pickedImage}) async {
    await baseFunction(context, () async {
      final cat = CategoryModel(
        arabicName: arabicName,
        englishName: name,
        sortNumber: categories.length + 1,
      );

      await categoryRepository.addCategory(cat: cat, pickedImage: pickedImage);
    }, type: FlushBarType.add, additionalFunction: getCategories);
  }

  //! Edit category ====================================
  Future<void> editCategory(BuildContext context,
      {required CategoryModel cat,
      String? pickedImage,
      bool getCategoriesAfterEdit = true}) async {
    await baseFunction(context, () async {
      await categoryRepository.editCategory(cat: cat, pickedImage: pickedImage);
    },
        type: getCategoriesAfterEdit ? FlushBarType.update : null,
        additionalFunction: getCategoriesAfterEdit ? getCategories : null);
  }

  //! Delete category ====================================
  Future<void> deleteCategory(BuildContext context,
      {required CategoryModel category}) async {
    await baseFunction(
      context,
      () async {
        context.back();

        await getCategories(context, withLoading: false);

        final categoryData = categories.firstWhereOrNull(
          (element) => element.documentId == category.documentId,
        );

        if (categoryData == null) {
          return;
        }

        await categoryRepository.deleteCategory(category: categoryData);

        await getCategories(context);
      },
      type: FlushBarType.delete,
      additionalFunction: _navigateToCatScreen,
    );
  }

  void _navigateToCatScreen(BuildContext context) {
    context.read<BottomNavbarVM>().setCurrentIndex(2);
    context.toReplacement(const MainScreen());
  }

  bool isAllCategoriesSortNumberEqualNull() {
    final isAllCategoriesSortNumberEqualNull =
        categories.every((element) => element.sortNumber == null);

    Log.f(
        "isAllCategoriesSortNumberEqualNull: $isAllCategoriesSortNumberEqualNull");

    return isAllCategoriesSortNumberEqualNull;
  }

  Future<void> addSortNumbersToCategories(
      List<CategoryModel> categories) async {
    if (isAllCategoriesSortNumberEqualNull() && categories.isNotEmpty) {
      for (int i = 0; i < categories.length; i++) {
        categories[i] = categories[i].copyWith(sortNumber: i + 1);
        await categoryRepository.editCategory(cat: categories[i]);
      }

      Log.f(
          "categories SortNumber: ${categories.map((e) => e.sortNumber).toList()}");
    }

    notifyListeners();
  }
}
