import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/shipping_card_widget.dart';
import 'package:provider/provider.dart';

import '../../view_model/shipping_view_model.dart';

class ShippingList extends HookWidget {
  const ShippingList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ShippingVM>(
      builder: (context, shippingVM, child) {
        return HookBuilder(builder: (context) {
          final costValues = useState<Map<String?, ValueNotifier<String>>>({});
          final isActiveValues =
              useState<Map<String?, ValueNotifier<bool>>>({});

          void setValues() {
            for (final city in shippingVM.citiesList) {
              costValues.value[city.city?.documentId] =
                  ValueNotifier<String>(city.cost?.toString() ?? '');
              isActiveValues.value[city.city?.documentId] =
                  ValueNotifier<bool>(city.freeShipping);
            }
          }

          useEffect(() {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              shippingVM.getShipping(context).then((value) {
                setValues();
              });
            });

            return () {};
          }, []);

          if (shippingVM.citiesList.isEmpty ||
              costValues.value.isEmpty ||
              isActiveValues.value.isEmpty ||
              shippingVM.isLoading) {
            return const Center(child: LoadingWidget());
          }

          return ListView.separated(
              padding: const EdgeInsets.all(AppSpaces.largePadding),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                final city = shippingVM.citiesList.reversed.toList()[index];
                return ShippingCardWidget(
                  cityCost: city,
                  isActive: isActiveValues.value[city.city?.documentId]!,
                  costValue: costValues.value[city.city?.documentId]!,
                );
              },
              separatorBuilder: (context, index) => Column(
                    children: [
                      context.smallGap,
                      Divider(),
                      context.smallGap,
                    ],
                  ),
              itemCount: shippingVM.citiesList.length);
        });
      },
    );
  }
}

//
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
// import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
// import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/shipping_card_widget.dart';
// import 'package:provider/provider.dart';
//
// import '../../view_model/shipping_view_model.dart';
//
// class ShippingList extends HookWidget {
//   const ShippingList({
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<ShippingVM>(
//       builder: (context, shippingVM, child) {
//         return HookBuilder(builder: (context) {
//           final costValues = useState<Map<String?, ValueNotifier<String>>>({});
//
//           void setValues() {
//             for (final city in shippingVM.citiesList) {
//               costValues.value[city.city?.documentId] =
//                   ValueNotifier<String>(city.cost?.toString() ?? '0');
//             }
//           }
//
//           useEffect(() {
//             WidgetsBinding.instance.addPostFrameCallback((_) {
//               shippingVM.getShipping(context).then((value) {
//                 setValues();
//               });
//             });
//
//             return () {};
//           }, []);
//
//           if (shippingVM.citiesList.isEmpty ||
//               costValues.value.isEmpty ||
//               shippingVM.isLoading) {
//             return const Center(child: LoadingWidget());
//           }
//
//           return ListView.separated(
//               padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//               shrinkWrap: true,
//               itemBuilder: (context, index) => ShippingCardWidget(
//                 cityCost: shippingVM.citiesList[index],
//                 costValue: costValues
//                     .value[shippingVM.citiesList[index].city?.documentId]!,
//               ),
//               separatorBuilder: (context, index) => context.mediumGap,
//               itemCount: shippingVM.citiesList.length);
//         });
//       },
//     );
//   }
// }
