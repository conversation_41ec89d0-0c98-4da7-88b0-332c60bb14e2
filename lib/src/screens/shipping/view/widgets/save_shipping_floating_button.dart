import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view_model/shipping_view_model.dart';
import 'package:provider/provider.dart';

class SaveShippingFloatingButton extends StatelessWidget {
  final VoidCallback? onSave;

  const SaveShippingFloatingButton({super.key, this.onSave});

  @override
  Widget build(BuildContext context) {
    return Consumer<ShippingVM>(builder: (context, shippingVM, child) {
      return Button(
        haveElevation: false,
        label: context.tr.save,
        onPressed: () {
          if (onSave != null) {
            onSave!();
          }

          shippingVM.editShipping(context: context);
        },
        icon: const Icon(
          size: 18,
          Icons.done_all,
          color: ColorManager.white,
        ),
        isLoading: shippingVM.isLoading,
      ).sized(
        height: shippingVM.isLoading ? 30 : 45,
      );
    });
  }
}
