import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';
import 'package:provider/provider.dart';

import '../../../auth/view_model/auth_view_model.dart';

class AreaCardWidget extends HookWidget {
  final AreaModel area;
  final ValueChanged<AreaModel> onUpdate; // Callback for updates

  const AreaCardWidget({super.key, required this.area, required this.onUpdate});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final currentCurrency = authVM.currentCurrency(context);
    final costController =
        useTextEditingController(text: area.cost?.toString() ?? '');
    final isFreeShipping = useState<bool>(area.isFreeShipping ?? false);
    final isActive = useState<bool>(area.isActive ?? true);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          color: context.appTheme.cardColor,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius)),
      padding: EdgeInsets.all(AppSpaces.mediumPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${area.nameEn} - ${area.nameAr}', style: context.title),
          Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(context.tr.active, style: context.labelLarge),
              SwitchButtonWidget(
                value: isActive,
                onChanged: (value) {
                  isActive.value = value;
                  area.isActive = value;
                  onUpdate(area); // Notify the update
                },
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(context.tr.freeShipping, style: context.labelLarge),
              SwitchButtonWidget(
                value: isFreeShipping,
                onChanged: (value) {
                  isFreeShipping.value = value;
                  area.isFreeShipping = value;
                  onUpdate(area); // Notify the update
                },
              ),
            ],
          ),
          BaseTextField(
            textInputType: TextInputType.number,
            controller: costController,
            onChanged: (value) {
              area.cost = num.tryParse(value);

              Log.f("sdhkvbdjfhv ${value}");
              onUpdate(area); // Notify the update
            },
            suffixIcon: Padding(
              padding: EdgeInsets.all(15),
              child: Text(currentCurrency, style: context.labelMedium),
            ),
          ),
        ],
      ),
    );
  }
}
