import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/add_areas_from_suggestions_widget.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/app_constants.dart';
import '../../auth/view_model/auth_view_model.dart';
import '../view_model/shipping_view_model.dart';

class AddAreasScreen extends HookWidget {
  final String cityName;
  final String cityDocumentIdId;

  final List<AreaModel> vendorAreas;

  const AddAreasScreen({
    super.key,
    required this.cityName,
    required this.vendorAreas,
    required this.cityDocumentIdId,
  });

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final currentCurrency = authVM.currentCurrency(context);
    final areas = AppConsts.getAreasByCityName(cityName, context);
    final filteredAreas = useState<List<AreaModel>>([]);
    final englishNameController = useTextEditingController();
    final arabicNameController = useTextEditingController();
    final costController = useTextEditingController();
    final isActive = useState<bool>(true);
    final isFreeShipping = useState<bool>(false);
    final formKey = useState(GlobalKey<FormState>());

    final selectedAreas = useState<List<AreaModel>>([]);

    final selectedAll = useState<bool>(false);

    useEffect(
      () {
        filteredAreas.value = areas
            .where((area) {
              return area != null &&
                  !vendorAreas
                      .any((vendorArea) => vendorArea.nameEn == area.nameEn);
            })
            .cast<AreaModel>()
            .toList();

        return () {};
      },
    );

    return Consumer<ShippingVM>(
      builder: (context, shippingVM, child) {
        return Scaffold(
            appBar: MainAppBar(
              title: context.tr.addArea,
              haveBackButton: true,
            ),
            body: Form(
              key: formKey.value,
              child: ListView(
                padding: EdgeInsets.all(AppSpaces.mediumPadding),
                children: [
                  if (areas.isNotEmpty) ...[
                    Row(
                      children: [
                        Text(
                          context.tr.suggestions,
                          style: context.subHeadLine,
                        ),
                        context.smallGap,
                        Icon(Icons.add_circle)
                      ],
                    ),
                    context.mediumGap,
                    SizedBox(
                      height: 40.h,
                      child: Button(
                        color: ColorManager.textFieldColor(context),
                        label: context.tr.selectAreas,
                        onPressed: () {
                          showModalBottomSheet(
                            backgroundColor: context.appTheme.cardColor,
                            showDragHandle: true,
                            isScrollControlled: true,
                            context: context,
                            builder: (context) {
                              return FractionallySizedBox(
                                heightFactor: 0.95,
                                child: ValueListenableBuilder(
                                  valueListenable: selectedAreas,
                                  builder: (context, value, child) =>
                                      HookBuilder(
                                    builder: (context) {
                                      final searchController =
                                          useTextEditingController();
                                      final searchQuery = useState('');
                                      final filteredResults =
                                          filteredAreas.value.where((area) {
                                        final query =
                                            searchQuery.value.toLowerCase();
                                        return area.nameEn
                                                    ?.toLowerCase()
                                                    .contains(query) ==
                                                true ||
                                            area.nameAr
                                                    ?.toLowerCase()
                                                    .contains(query) ==
                                                true;
                                      }).toList();

                                      return Column(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    AppSpaces.mediumPadding),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                CircleAvatar(
                                                  backgroundColor: context
                                                          .isDark
                                                      ? ColorManager.darkGrey
                                                      : ColorManager.grey,
                                                  child: IconButton(
                                                    onPressed: () =>
                                                        context.back(),
                                                    icon: Icon(
                                                      Icons
                                                          .keyboard_arrow_down_sharp,
                                                      color: context.isDark
                                                          ? Colors.white
                                                          : Colors.black,
                                                    ),
                                                  ),
                                                ),
                                                context.mediumGap,
                                                TextField(
                                                  controller: searchController,
                                                  decoration: InputDecoration(
                                                    hintText: context.tr.search,
                                                    prefixIcon:
                                                        Icon(Icons.search),
                                                    border: OutlineInputBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              AppRadius
                                                                  .baseRadius),
                                                    ),
                                                  ),
                                                  onChanged: (value) {
                                                    searchQuery.value = value;
                                                  },
                                                ),
                                                if (searchController
                                                    .text.isEmpty) ...[
                                                  context.mediumGap,
                                                  Row(
                                                    children: [
                                                      Checkbox(
                                                        activeColor:
                                                            ColorManager.white,
                                                        value:
                                                            selectedAll.value,
                                                        onChanged: (value) {
                                                          selectedAll.value =
                                                              value ?? false;
                                                          if (value == true) {
                                                            selectedAreas
                                                                    .value =
                                                                filteredResults;
                                                          } else {
                                                            selectedAreas
                                                                .value = [];
                                                          }
                                                        },
                                                      ),
                                                      Text(
                                                        context.tr.selectAll,
                                                        style: context.subTitle,
                                                      ),
                                                    ],
                                                  ),
                                                ]
                                              ],
                                            ),
                                          ),
                                          if (filteredResults.isEmpty)
                                            Expanded(
                                                child: EmptyDataWidget(
                                              message: context.tr.noAreasFound,
                                            ))
                                          else
                                            Expanded(
                                              child: ListView(
                                                padding: EdgeInsets.all(
                                                    AppSpaces.smallPadding),
                                                children:
                                                    filteredResults.map((e) {
                                                  final isAreaSelected =
                                                      selectedAreas.value.any(
                                                          (a) =>
                                                              a.nameEn ==
                                                              e.nameEn);
                                                  return InkWell(
                                                    onTap: () {
                                                      final current = [
                                                        ...selectedAreas.value
                                                      ];
                                                      final exists =
                                                          current.any((a) =>
                                                              a.nameEn ==
                                                              e.nameEn);

                                                      if (exists) {
                                                        current.removeWhere(
                                                            (a) =>
                                                                a.nameEn ==
                                                                e.nameEn);
                                                      } else {
                                                        current.add(e);
                                                      }

                                                      selectedAreas.value =
                                                          current;
                                                    },
                                                    child: AnimatedContainer(
                                                      duration: Duration(
                                                          milliseconds: 500),
                                                      padding: EdgeInsets.all(
                                                          AppSpaces
                                                              .mediumPadding),
                                                      margin: EdgeInsets.only(
                                                          bottom: AppSpaces
                                                              .smallPadding),
                                                      decoration: BoxDecoration(
                                                        color: isAreaSelected
                                                            ? ColorManager
                                                                .lightPrimaryColor
                                                            : ColorManager
                                                                .textFieldColor(
                                                                    context),
                                                        borderRadius: BorderRadius
                                                            .circular(AppRadius
                                                                .baseRadius),
                                                      ),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            '${e.nameEn ?? ''} - ${e.nameAr ?? ''}',
                                                            style: context
                                                                .whiteLabelLarge,
                                                          ),
                                                          if (isAreaSelected)
                                                            Icon(
                                                              Icons
                                                                  .check_circle,
                                                              color:
                                                                  ColorManager
                                                                      .white,
                                                            )
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          );
                          // showModalBottomSheet(
                          //   backgroundColor: context.appTheme.cardColor,
                          //   showDragHandle: true,
                          //   isScrollControlled: true,
                          //   context: context,
                          //   builder: (context) {
                          //     return FractionallySizedBox(
                          //       heightFactor: 0.90,
                          //       child: ValueListenableBuilder(
                          //           valueListenable: selectedAreas,
                          //           builder: (context, _, __) {
                          //             return Column(
                          //               children: [
                          //                 Padding(
                          //                   padding: EdgeInsets.symmetric(
                          //                       horizontal:
                          //                           AppSpaces.mediumPadding),
                          //                   child: Column(
                          //                     crossAxisAlignment:
                          //                         CrossAxisAlignment.end,
                          //                     children: [
                          //                       CircleAvatar(
                          //                         backgroundColor: context
                          //                                 .isDark
                          //                             ? ColorManager.darkGrey
                          //                             : ColorManager.grey,
                          //                         child: IconButton(
                          //                             onPressed: () =>
                          //                                 context.back(),
                          //                             icon: Icon(
                          //                               Icons
                          //                                   .keyboard_arrow_down_sharp,
                          //                               color: context.isDark
                          //                                   ? Colors.white
                          //                                   : Colors.black,
                          //                             )),
                          //                       ),
                          //                       context.mediumGap,
                          //                       Row(
                          //                         children: [
                          //                           Checkbox(
                          //                             activeColor:
                          //                                 ColorManager.white,
                          //                             value: selectedAll.value,
                          //                             onChanged: (value) {
                          //                               selectedAll.value =
                          //                                   value ?? false;
                          //                               if (value == true) {
                          //                                 selectedAreas.value =
                          //                                     filteredAreas
                          //                                         .value;
                          //                               } else {
                          //                                 selectedAreas.value =
                          //                                     [];
                          //                               }
                          //                             },
                          //                           ),
                          //                           Text(
                          //                             context.tr.selectAll,
                          //                             style: context.subTitle,
                          //                           ),
                          //                         ],
                          //                       ),
                          //                     ],
                          //                   ),
                          //                 ),
                          //                 Expanded(
                          //                   child: ListView(
                          //                     padding: EdgeInsets.all(
                          //                         AppSpaces.smallPadding),
                          //                     children:
                          //                         filteredAreas.value.map((e) {
                          //                       final isAreaSelected =
                          //                           selectedAreas.value.any(
                          //                               (a) =>
                          //                                   a.nameEn ==
                          //                                   e.nameEn);
                          //                       return InkWell(
                          //                         onTap: () {
                          //                           final current = [
                          //                             ...selectedAreas.value
                          //                           ];
                          //                           final exists = current.any(
                          //                               (a) =>
                          //                                   a.nameEn ==
                          //                                   e.nameEn);
                          //
                          //                           if (exists) {
                          //                             current.removeWhere((a) =>
                          //                                 a.nameEn == e.nameEn);
                          //                           } else {
                          //                             current.add(e);
                          //                           }
                          //
                          //                           selectedAreas.value =
                          //                               current;
                          //                         },
                          //                         child: AnimatedContainer(
                          //                           duration: Duration(
                          //                               milliseconds: 500),
                          //                           padding: EdgeInsets.all(
                          //                               AppSpaces
                          //                                   .mediumPadding),
                          //                           margin: EdgeInsets.only(
                          //                               bottom: AppSpaces
                          //                                   .smallPadding),
                          //                           decoration: BoxDecoration(
                          //                             color: isAreaSelected
                          //                                 ? ColorManager
                          //                                     .lightPrimaryColor
                          //                                 : ColorManager
                          //                                     .textFieldColor(
                          //                                         context),
                          //                             borderRadius:
                          //                                 BorderRadius.circular(
                          //                                     AppRadius
                          //                                         .baseRadius),
                          //                           ),
                          //                           child: Row(
                          //                             mainAxisAlignment:
                          //                                 MainAxisAlignment
                          //                                     .spaceBetween,
                          //                             children: [
                          //                               Text(
                          //                                 '${e.nameEn ?? ''} - ${e.nameAr ?? ''}',
                          //                                 style: context
                          //                                     .whiteLabelLarge,
                          //                               ),
                          //                               if (isAreaSelected)
                          //                                 Icon(
                          //                                   Icons.check_circle,
                          //                                   color: ColorManager
                          //                                       .white,
                          //                                 )
                          //                             ],
                          //                           ),
                          //                         ),
                          //                       );
                          //                     }).toList(),
                          //                   ),
                          //                 ),
                          //               ],
                          //             );
                          //           }),
                          //     );
                          //   },
                          // );
                        },
                        haveElevation: false,
                        radius: AppRadius.baseRadius,
                        isWhiteText: context.isDark ? true : false,
                        isBold: false,
                      ),
                    ),
                  ],
                  if (selectedAreas.value.isEmpty) ...[
                    context.largeGap,
                    BaseTextField(
                      title: context.tr.englishName,
                      controller: englishNameController,
                    ),
                    context.fieldsGap,
                    BaseTextField(
                      title: context.tr.arabicName,
                      controller: arabicNameController,
                    ),
                    context.fieldsGap,
                    BaseTextField(
                      title: context.tr.cost,
                      controller: costController,
                      hint: context.tr.cost,
                    ),
                    context.fieldsGap,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr.active,
                          style: context.labelMedium,
                        ),
                        SwitchButtonWidget(
                          value: isActive,
                          onChanged: (value) {
                            isActive.value = value;
                          },
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.tr.freeShipping,
                          style: context.labelMedium,
                        ),
                        SwitchButtonWidget(
                          value: isFreeShipping,
                          onChanged: (value) {
                            isFreeShipping.value = value;
                          },
                        )
                      ],
                    ),
                    context.xLargeGap,
                  ] else ...[
                    AddAreasFromSuggestionsWidget(selectedAreas: selectedAreas)
                  ]
                ],
              ),
            ),
            bottomNavigationBar: selectedAreas.value.isEmpty
                ? Padding(
                    padding: EdgeInsets.all(AppSpaces.mediumPadding),
                    child: Button(
                      label: context.tr.add,
                      isLoading: shippingVM.isLoading,
                      onPressed: () async {
                        if (!formKey.value.currentState!.validate()) return;

                        final newArea = AreaModel(
                          nameEn: englishNameController.text,
                          nameAr: arabicNameController.text,
                          cost: num.tryParse(costController.text),
                          isFreeShipping: isFreeShipping.value,
                          isActive: isActive.value,
                        );

                        shippingVM.updateCityAreas(
                            cityId: cityDocumentIdId,
                            areas: [...vendorAreas, newArea]);

                        if (!context.mounted) return;
                        await shippingVM.editShipping(
                            context: context, isEdit: false);
                      },
                    ),
                  )
                : Padding(
                    padding: EdgeInsets.all(AppSpaces.mediumPadding),
                    child: Button(
                      label: context.tr.add,
                      isLoading: shippingVM.isLoading,
                      onPressed: () async {
                        final hasInvalid = selectedAreas.value.any(
                          (area) => area.cost == null || area.cost! <= 0,
                        );
                        if (hasInvalid) {
                          context.showBarMessage(
                              context.tr.youMustButCostForAllAreasYouSelected,
                              isError: true,
                              duration: 2);

                          return;
                        }

                        shippingVM.updateCityAreas(
                            cityId: cityDocumentIdId,
                            areas: [...vendorAreas, ...selectedAreas.value]);

                        if (!context.mounted) return;
                        await shippingVM.editShipping(
                            context: context, isEdit: false);
                      },
                    ),
                  ));
      },
    );
  }
}

// BaseSearchDropDown(
//     showSearch: false,
//     isRequired: false,
//     itemModelAsName: (area) => (area as AreaModel).nameEn,
//     onChanged: (value) {
//       selectedArea.value = value;
//
//       englishNameController.text = value?.nameEn ?? '';
//       arabicNameController.text = value?.nameAr ?? '';
//     },
//     data: filteredAreas.value,
//     selectedValue: selectedArea.value),
