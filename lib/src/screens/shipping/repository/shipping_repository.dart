import 'dart:async';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';

import '../model/city_cost_model.dart';
import '../model/shipping_model.dart';

class ShippingRepository {
  final BaseApiServices _networkApiServices;

  ShippingRepository(this._networkApiServices);

  //! Get shipping ==================================
  Future<ShippingModel?> getShipping() async {
    try {
      final response = await _networkApiServices.getResponse(
        ApiEndPoints.shipping,
      );

      final shippingData = (response as List)
          .map((shipping) => ShippingModel.fromJson(shipping))
          .toList();

      final vendorShipping = shippingData.firstOrNull;

      return vendorShipping;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  Future<List<CityCostModel>> getCities() async {
    try {
      final response = await _networkApiServices.getResponse(
        ApiEndPoints.cities,
        withOutVendor: true,
      );
      final citiesData = (response as List)
          .map((cities) => CityCostModel.fromCityJson(cities))
          .toList();

      return citiesData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  //! Edit Shipping ================================
  Future<void> editShipping({required ShippingModel? shippingModel}) async {
    try {
      final data = await _networkApiServices.putResponse(
        ApiEndPoints.shipping,
        data: shippingModel?.toCityCostJson() ?? {},
      );

      final documentId = data[ApiStrings.documentId] as String?;

      await _networkApiServices.connectRelation(
        data: {
          "shippings": {
            "set": [documentId],
          },
        },
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
