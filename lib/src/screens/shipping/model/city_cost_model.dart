import '../../../core/consts/api_strings.dart';
import 'city_model.dart';

class CityCostModel {
  final int? id;
  final String? documentId;
  final num? cost;
  final CityModel? city;
  final bool freeShipping;
  final List<AreaModel>? areas;

  CityCostModel({
    this.id,
    this.documentId,
    this.cost,
    this.city,
    this.areas = const [],
    this.freeShipping = false,
  });

  factory CityCostModel.fromJson(Map<String, dynamic> json) {
    final areas = json[ApiStrings.areas] as List?;

    final areasList = areas?.map((e) => AreaModel.fromJson(e)).toList();
    final city = json[ApiStrings.city] == null
        ? null
        : CityModel.fromJson(json[ApiStrings.city]);

    return CityCostModel(
      id: json[ApiStrings.id],
      freeShipping: json[ApiStrings.freeShipping] ?? false,
      documentId: json[ApiStrings.documentId],
      cost: json[ApiStrings.cost] ?? 0.0,
      city: city,
      areas: areasList,
    );
  }

  factory CityCostModel.fromCityJson(Map<String, dynamic> json) {
    final areas = json[ApiStrings.areas] as List?;

    final areasList = areas?.map((e) => AreaModel.fromJson(e)).toList();
    return CityCostModel(
        id: json[ApiStrings.id],
        documentId: json[ApiStrings.documentId],
        freeShipping: json[ApiStrings.freeShipping] ?? false,
        areas: areasList,
        city: CityModel(
          id: json[ApiStrings.id],
          documentId: json[ApiStrings.documentId],
          name: json[ApiStrings.name],
        ));
  }

  //! Copy with
  CityCostModel copyWith({
    int? id,
    String? documentId,
    num? cost,
    CityModel? city,
    bool? freeShipping,
    List<AreaModel>? areas,
  }) {
    return CityCostModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      cost: cost ?? this.cost,
      city: city ?? this.city,
      freeShipping: freeShipping ?? this.freeShipping,
      areas: areas ?? this.areas,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.city: city?.id,
      ApiStrings.cost: cost ?? 0.0,
      ApiStrings.freeShipping: freeShipping,
      ApiStrings.areas: areas?.map((e) => e.toJson()).toList() ?? [],
    };
  }
}

class AreaModel {
  final int? id;
  final String? documentId;
  final String? nameEn;
  final String? nameAr;
  bool? isFreeShipping;
  bool? isActive;
  num? cost;

  AreaModel(
      {this.id,
      this.documentId,
      this.nameEn,
      this.nameAr,
      this.isFreeShipping,
      this.isActive,
      this.cost});

  factory AreaModel.fromJson(Map<String, dynamic> json) {
    return AreaModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      nameEn: json[ApiStrings.name],
      nameAr: json[ApiStrings.arabicName],
      isFreeShipping: json[ApiStrings.freeShipping],
      cost: json[ApiStrings.cost],
      isActive: json[ApiStrings.isActive],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // if (id != null) ApiStrings.id: id,
      // ApiStrings.documentId: documentId,
      ApiStrings.name: nameEn,
      ApiStrings.arabicName: nameAr,
      ApiStrings.freeShipping: isFreeShipping,
      ApiStrings.cost: cost,
      ApiStrings.isActive: isActive,
    };
  }

  AreaModel copyWith({
    int? id,
    String? documentId,
    String? nameEn,
    String? nameAr,
    bool? isFreeShipping,
    bool? isActive,
    num? cost,
  }) {
    return AreaModel(
      id: id ?? this.id,
      documentId: documentId ?? this.documentId,
      nameEn: nameEn ?? this.nameEn,
      nameAr: nameAr ?? this.nameAr,
      isFreeShipping: isFreeShipping ?? this.isFreeShipping,
      cost: cost ?? this.cost,
      isActive: isActive ?? this.isActive,
    );
  }
}
