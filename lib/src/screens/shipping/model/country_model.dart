import '../../../core/consts/api_strings.dart';

class CountryModel {
  final int? id;
  final String? documentId;
  final String? name;

  CountryModel({
    this.id,
    this.documentId,
    this.name,
  });

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      name: json[ApiStrings.name],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (documentId != null) ApiStrings.documentId: documentId,
      if (name != null) ApiStrings.name: name,
    };
  }
}
