import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/connectivity_internet/connectivity_internet.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view_model/settings_view_model.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../app.dart';
import '../core/resources/app_spaces.dart';
import '../core/shared_widgets/shared_widgets.dart';
import 'shared/app_settings/view_model/app_settings_view_model.dart';

class CheckUpdateWidget extends StatelessWidget {
  const CheckUpdateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamProvider<NetworkStatus>(
      create: (context) =>
          NetworkStatusService().networkStatusController.stream,
      initialData: NetworkStatus.Online,
      child: NetworkAwareWidget(
        onlineChild: const _CheckUpdateScreen(),
        offlineChild: Scaffold(
            body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Lottie.asset(
                Assets.animatedNoInternet,
                height: 100.h,
                width: 100.w,
              ),
              context.largeGap,
              Text(
                context.read<AppSettingsVM>().locale.languageCode == 'en'
                    ? 'No Internet Connection'
                    : 'لا يوجد اتصال بالإنترنت',
                style: context.title,
              ),
            ],
          ).paddingAll(AppSpaces.largePadding),
        )),
      ),
    );
  }
}

class _CheckUpdateScreen extends HookWidget {
  const _CheckUpdateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsVM>(
      builder: (context, settingsVM, child) {
        if (settingsVM.settings?.version == settingsVM.appVersion) {
          return const SelectedScreen();
        } else {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Stack(
              alignment: Alignment.center,
              children: [
                const SelectedScreen(),

                //! Update App Dialog
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.black.withOpacity(0.5),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          margin: const EdgeInsets.all(AppSpaces.mediumPadding),
                          padding:
                              const EdgeInsets.all(AppSpaces.mediumPadding),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: context.appTheme.cardColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // lottie
                              Lottie.asset(
                                'assets/animated/update.json',
                              ),
                              Text('Update Available', style: context.title),
                              context.mediumGap,

                              Text(
                                'A new version of the app is available. Please update the app to continue using it.',
                                style: context.subTitle,
                                textAlign: TextAlign.center,
                              ),
                              context.largeGap,
                              Button(
                                onPressed: () {
                                  final url = Platform.isIOS
                                      ? 'https://apps.apple.com/us/app/idea2app-vendor/id6479177341?platform=iphone'
                                      : 'https://play.google.com/store/apps/details?id=com.idea2app.vendor_app';
                                  launchUrl(
                                    Uri.parse(url),
                                  );
                                },
                                label: 'Update Now',
                              )
                            ],
                          ),
                        ),
                      ]),
                )
              ],
            ),
          );
        }
      },
    );
  }
}
