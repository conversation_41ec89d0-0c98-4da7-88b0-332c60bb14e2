import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/user_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/user_order_model.dart';
import 'package:idea2app_vendor_app/src/screens/users/view/users_screen.dart';

import '../repository/user_repo.dart';

class UserVM extends BaseVM {
  final UserRepo userRepo;

  UserVM({required this.userRepo});

  var usersData = <UserModel>[];
  var userOrders = <UserOrderModel>[];

  Future<void> getUsers(BuildContext context) async {
    return await baseFunction(context, () async {
      usersData = await userRepo.getUsers();
    });
  }

  Future<void> getUserOrders(BuildContext context) async {
    return await baseFunction(context, () async {
      userOrders = await userRepo.getUserOrders();
    });
  }

  List<UserModel> searchUsers(BuildContext context, String query) {
    if (query.trim().isEmpty) {
      return usersData;
    } else {
      var searchedUsers = usersData
          .where((element) =>
              element.username!.toLowerCase().contains(query.toLowerCase()) ||
              element.displayName!
                  .toLowerCase()
                  .contains(query.toLowerCase()) ||
              element.email!.toLowerCase().contains(query.toLowerCase()) ||
              element.phone!.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return searchedUsers;
    }
  }

  List<UserOrderModel> searchUsersOrders(BuildContext context, String query) {
    if (query.trim().isEmpty) {
      return userOrders;
    } else {
      var searchedUsers = userOrders
          .where((element) =>
              element.guestName.toLowerCase().contains(query.toLowerCase()) ||
              element.phoneNumber.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return searchedUsers;
    }
  }

  void notify() {
    notifyListeners();
  }

  Future<void> sendNotification(
    BuildContext context, {
    required String title,
    required String body,
    required String userTokenOrTopic,
    bool isTopic = true,
  }) async {
    return await baseFunction(context, () async {
      await NotificationService.sendNotification(
        title: title,
        body: body,
        userTokenOrTopic: userTokenOrTopic,
        isTopic: isTopic,
      );

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.sendSuccessfully);
    });
  }
}
