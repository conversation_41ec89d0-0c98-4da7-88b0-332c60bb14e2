import 'dart:async';
import 'dart:io';

import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/user_model.dart';

import '../../../core/data/remote/app_exception.dart';
import '../../../core/utils/logger.dart';
import '../../auth/models/user_order_model.dart';

class UserRepo {
  final BaseApiServices networkApiServices;

  UserRepo({required this.networkApiServices});

  Future<List<UserModel>> getUsers() async {
    try {
      final response = await networkApiServices.getResponse(ApiEndPoints.users);

      final usersData =
          (response as List).map((e) => UserModel.fromJson(e)).toList();

      return usersData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  Future<List<UserOrderModel>> getUserOrders() async {
    try {
      final response = await networkApiServices
          .getResponse(ApiEndPoints.ordersUsers, sortByCreatedAt: false);

      final ordersData =
          (response as List).map((e) => UserOrderModel.fromJson(e)).toList();

      return ordersData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e, s) {
      Log.e("getUserOrders $e\n$s");
      rethrow;
    }
  }
}
