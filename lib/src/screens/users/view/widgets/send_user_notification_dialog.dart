import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../auth/models/helper_models/vendor_helper_model.dart';
import '../../view_model/user_view_model.dart';

class SendUserNotificationDialog extends HookWidget {
  final bool isTopic;
  final String userTokenOrTopic;
  const SendUserNotificationDialog({
    super.key,
    this.userTokenOrTopic = '',
    required this.isTopic,
  });

  @override
  Widget build(BuildContext context) {
    final titleController = useTextEditingController();
    final descriptionController = useTextEditingController();
    final formKey = useState(GlobalKey<FormState>());

    return Consumer<UserVM>(
      builder: (context, userVM, child) {
        Future<void> sendNotification() async {
          if (!formKey.value.currentState!.validate()) return;

          if (isTopic) {
            await userVM.sendNotification(context,
                isTopic: true,
                userTokenOrTopic:
                    '${VendorModelHelper.currentVendorBusinessName()}-Customer',
                title: titleController.text,
                body: descriptionController.text);
          } else {
            await userVM.sendNotification(context,
                isTopic: false,
                userTokenOrTopic: userTokenOrTopic ?? '',
                title: titleController.text,
                body: descriptionController.text);
          }
        }

        return BaseDialog(
            withCloseButton: true,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
              child: Form(
                key: formKey.value,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // * Header ===========================
                    Text(
                      context.tr.sendNotification,
                      style: context.subHeadLine,
                    ),

                    context.largeGap,
                    // * Title ===========================
                    BaseTextField(
                      title: context.tr.title,
                      controller: titleController,
                      hint: context.tr.title,
                    ),
                    context.largeGap,

                    // * Description ===========================
                    BaseTextField(
                      title: context.tr.description,
                      hint: context.tr.description,
                      maxLines: 4,
                      radius: AppRadius.longFieldRadius,
                      controller: descriptionController,
                    ),

                    context.largeGap,

                    // * Add Product Button ========================
                    Button(
                      isLoading: userVM.isLoading,
                      isOutLine: userVM.isLoading,
                      label: context.tr.send,
                      onPressed: () async => await sendNotification(),
                      isWhiteText: true,
                    )
                  ],
                ),
              ),
            ));
      },
    );
  }
}
