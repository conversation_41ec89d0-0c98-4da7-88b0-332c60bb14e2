import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:provider/provider.dart';

import '../../../core/shared_widgets/animated/empty_data_widget.dart';
import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../../auth/models/helper_models/vendor_helper_model.dart';
import '../../home/<USER>/main_screen.dart';
import '../view_model/user_view_model.dart';
import 'widgets/send_user_notification_dialog.dart';
import 'widgets/user_card_widget.dart';

class UsersScreen extends HookWidget {
  const UsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final userVM = context.read<UserVM>();
    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);
    final query = useState('');

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        userVM.getUsers(context);
      });

      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        haveBackButton: true,
        title: currentAdminVendor != null
            ? '${currentAdminVendor?.name} ${context.tr.users}'
            : context.tr.users,
        onBackPressed: () {
          if (currentAdminVendor != null) {
            context.back();
            return;
          }
          context.toReplacement(const MainScreen());
        },
      ),
      body: Consumer<UserVM>(
        builder: (context, userVM, child) {
          final users = userVM.searchUsers(context, query.value);

          if (userVM.isLoading) {
            return const Center(child: LoadingWidget());
          }

          // if (userVM.usersData.isEmpty) {
          //   return Center(
          //     child: EmptyDataWidget(
          //       message: context.tr.noUsersFound,
          //     ),
          //   );
          // }
          return Column(
            children: [
              Button(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) {
                    return SendUserNotificationDialog(
                      isTopic: true,
                    );
                  },
                ),
                label: context.tr.sendNotification,
              ).paddingAll(16),
              BaseTextField(
                controller: queryController,
                onChanged: (value) {
                  query.value = value;

                  isSearch.value = value.isNotEmpty;

                  userVM.notify();
                },
                suffixIcon: Icon(
                  isSearch.value ? Icons.close : CupertinoIcons.search,
                ).onTap(() {
                  query.value = '';

                  queryController.clear();

                  isSearch.value = false;

                  userVM.notify();
                }),
                hint: context.tr.search,
                withoutEnter: true,
              ).paddingSymmetric(horizontal: AppSpaces.smallPadding),
              context.mediumGap,
              if ((users.isEmpty && isSearch.value) || userVM.usersData.isEmpty)
                Center(
                  child: Center(
                    child: EmptyDataWidget(
                      message: context.tr.noUsersFound,
                    ),
                  ),
                ),
              Expanded(
                child: ListView.separated(
                    padding: const EdgeInsets.all(AppSpaces.smallPadding),
                    itemBuilder: (context, index) {
                      return UserCardWidget(user: users[index]);
                    },
                    separatorBuilder: (context, index) => context.mediumGap,
                    itemCount: users.length),
              ),
            ],
          );
        },
      ),
    );
  }
}
