import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view/widgets/cart_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../../core/resources/app_radius.dart';
import '../../../core/resources/app_spaces.dart';
import '../../../core/resources/theme/theme.dart';
import '../../../core/shared_widgets/shared_widgets.dart';

class CartScreen extends HookWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cartVM = context.read<CartVM>();
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        cartVM.getCart();
      });

      return () {};
    }, []);

    return Consumer2<CartVM, OrderVM>(
      builder: (context, cartVM, orderVM, child) {
        final total = cartVM.cartList.fold(0.0,
            (previousValue, element) => previousValue + element.totalPrice!);

        final productsTotalQuantity = cartVM.cartList.fold(
            0,
            (previousValue, element) =>
                previousValue + (element.quantity ?? 1));

        final isCartListEmpty = cartVM.cartList.isEmpty;

        return Scaffold(
          bottomNavigationBar: isCartListEmpty
              ? null
              : Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding,
                    vertical: AppSpaces.largePadding,
                  ),
                  child: Button(
                    isLoading: orderVM.isLoading,
                    isOutLine: orderVM.isLoading ? true : false,
                    label: context.tr.checkout,
                    onPressed: () {
                      final orderId = Random().nextInt(999999);

                      final order = OrderModel(
                        orderId: orderId,
                        total: total,
                        products: cartVM.cartList,
                      );

                      //! Put to change inventory Quantity
                      context.read<OrderVM>().decreaseStockQuantity(
                            context,
                            products: cartVM.cartList,
                          );

                      context
                          .read<OrderVM>()
                          .makeStoreOrder(context, order: order);
                    },
                    radius: AppRadius.buttonRadius,
                  ),
                ),
          appBar: MainAppBar(
            title: context.tr.cart,
            haveBackButton: true,
          ),
          body: isCartListEmpty
              ? const EmptyDataWidget()
              : Column(
                  children: [
                    if (!isCartListEmpty)
                      TotalWidget(totalPriceAndTotalProducts: (
                        total,
                        productsTotalQuantity
                      )),
                    Expanded(
                      child: ListView.separated(
                          padding:
                              const EdgeInsets.all(AppSpaces.mediumPadding),
                          itemBuilder: (context, index) =>
                              CartCardWidget(cart: cartVM.cartList[index]),
                          separatorBuilder: (context, index) =>
                              context.mediumGap,
                          itemCount: cartVM.cartList.length),
                    ),
                  ],
                ),
        );
      },
    );
  }
}

class TotalWidget extends StatelessWidget {
  final (num, int) totalPriceAndTotalProducts;

  const TotalWidget({super.key, required this.totalPriceAndTotalProducts});

  @override
  Widget build(BuildContext context) {
    final totalPrice = totalPriceAndTotalProducts.$1;
    final totalProducts = totalPriceAndTotalProducts.$2;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
            vertical: AppSpaces.smallPadding,
          ),
          decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(.15),
              borderRadius: BorderRadius.circular(AppRadius.buttonRadius)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.tr.total,
                style: context.title,
              ),
              context.smallGap,
              Text(
                totalPrice.toCurrency(context),
                style: context.title,
              ),
            ],
          ),
        ),
        context.smallGap,
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
            vertical: AppSpaces.smallPadding,
          ),
          decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(.15),
              borderRadius: BorderRadius.circular(AppRadius.buttonRadius)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.tr.totalProducts,
                style: context.title,
              ),
              context.smallGap,
              Text(
                totalProducts.toString(),
                style: context.title,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
