import 'package:idea2app_vendor_app/src/core/shared_models/base_model.dart';

import '../../../core/consts/api_strings.dart';

class PromoCodesModel extends BaseModel {
  final String promo;
  final num discount;
  final bool isActive;
  final bool isPercent;
  final bool showInList;

  const PromoCodesModel({
    super.documentId,
    super.id,
    super.createdAt,
    this.promo = '',
    this.isActive = false,
    this.isPercent = false,
    this.showInList = false,
    this.discount = 0,
  });

  factory PromoCodesModel.fromJson(Map<String, dynamic> json) {
    return PromoCodesModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      discount: json[ApiStrings.discount],
      isActive: json[ApiStrings.active],
      isPercent: json[ApiStrings.isPercent],
      showInList: json[ApiStrings.showInList],
      promo: json[ApiStrings.promo] ?? '',
      createdAt: DateTime.parse(json[ApiStrings.createdAt]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (documentId != null) ApiStrings.documentId: documentId,
      if (promo.isNotEmpty) ApiStrings.promo: promo,
      if (discount != 0) ApiStrings.discount: discount,
      ApiStrings.active: isActive,
      ApiStrings.isPercent: isPercent,
      ApiStrings.showInList: showInList,
    };
  }

  Map<String, dynamic> toActiveJson() {
    return {
      if (documentId != null) ApiStrings.documentId: documentId,
      ApiStrings.active: isActive,
      ApiStrings.showInList: isActive ? showInList : false,
    };
  }

  PromoCodesModel copyWith({
    String? promo,
    num? discount,
    bool? isActive,
    bool? isPercent,
    bool? showInList,
  }) {
    return PromoCodesModel(
      id: id,
      documentId: documentId,
      promo: promo ?? this.promo,
      discount: discount ?? this.discount,
      isActive: isActive ?? this.isActive,
      isPercent: isPercent ?? this.isPercent,
      showInList: showInList ?? this.showInList,
      createdAt: createdAt,
    );
  }
}
