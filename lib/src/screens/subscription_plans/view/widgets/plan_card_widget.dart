import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/pricing_model.dart';

class PlanCardWidget extends HookWidget {
  final PricingModel pricing;
  final ValueNotifier<PricingModel?> selectedPricing;

  const PlanCardWidget(
      {super.key, required this.pricing, required this.selectedPricing});

  @override
  Widget build(BuildContext context) {
    final isFullPackage = pricing.name?.contains('Full Package') ?? false;

    final isSelect = selectedPricing.value?.id == pricing.id;

    final saleColor = context.isDark
        ? ColorManager.white.withOpacity(0.5)
        : ColorManager.black.withOpacity(0.5);
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.baseRadius),
          color: context.appTheme.cardColor,
          gradient: isSelect
              ? LinearGradient(
                  transform: const GradientRotation(0.5),
                  colors: isFullPackage
                      ? [
                          const Color(0xFFC9963D),
                          const Color(0xfffcdb89),
                          const Color(0xFFC9963D),
                        ]
                      : ColorManager.gradientContainer,
                )
              : null),
      child: TextButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          elevation: 0,
          overlayColor: ColorManager.darkGrey,
          disabledBackgroundColor: ColorManager.grey,
          foregroundColor: ColorManager.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.baseRadius),
          ),
        ),
        onPressed: () {
          selectedPricing.value = pricing;
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (pricing.toPricingType == PricingType.androidAndIos)
                Column(
                  children: [
                    SvgPicture.asset(Assets.androidSvg,
                        width: 25,
                        height: 25,
                        color: isFullPackage && isSelect
                            ? const Color(0xFF343131)
                            : isSelect
                                ? Colors.white
                                : context.isDark
                                    ? Colors.white
                                    : const Color(0xFF343131)),
                    context.smallGap,
                    SvgPicture.asset(Assets.appleSvg,
                        width: 25,
                        height: 25,
                        color: isFullPackage && isSelect
                            ? const Color(0xFF343131)
                            : isSelect
                                ? Colors.white
                                : context.isDark
                                    ? Colors.white
                                    : const Color(0xFF343131)),
                  ],
                )
              else
                SvgPicture.asset(pricing.toPricingType.toPricingTypeIcon ?? '',
                    width: 25,
                    height: 25,
                    color: isFullPackage && isSelect
                        ? const Color(0xFF343131)
                        : isSelect
                            ? Colors.white
                            : context.isDark
                                ? Colors.white
                                : const Color(0xFF343131)),
              context.smallGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      (context.isEng ? pricing.name : pricing.nameAr) ?? '',
                      style: context.labelLarge.copyWith(
                          fontWeight:
                              isSelect ? FontWeight.bold : FontWeight.w400,
                          color: isSelect
                              ? isFullPackage
                                  ? const Color(0xFF343131)
                                  : Colors.white
                              : null,
                          fontSize: isSelect ? 18 : 16),
                    ),
                    context.xSmallGap,
                    Row(
                      children: [
                        Text(
                          pricing.price.toCurrency(context) ?? '',
                          textAlign: TextAlign.left,
                          style: context.headLine.copyWith(
                            fontWeight:
                                isSelect ? FontWeight.bold : FontWeight.w500,
                            color: isSelect
                                ? isFullPackage
                                    ? const Color(0xFF343131)
                                    : Colors.white
                                : null,
                          ),
                        ),
                        context.smallGap,
                        Text(
                          pricing.beforeSalePrice.toCurrency(context),
                          style: context.title.copyWith(
                              color: isFullPackage && isSelect
                                  ? ColorManager.darkGrey.withOpacity(0.7)
                                  : isSelect
                                      ? Colors.white
                                      : saleColor,
                              decoration: TextDecoration.lineThrough,
                              fontWeight: FontWeight.w400,
                              decorationThickness: 2,
                              decorationColor: isFullPackage && isSelect
                                  ? ColorManager.darkGrey.withOpacity(0.7)
                                  : isSelect
                                      ? Colors.white.withOpacity(0.7)
                                      : saleColor),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isFullPackage && isSelect
                          ? const Color(0xFF343131)
                          : isSelect
                              ? Colors.white
                              : context.isDark
                                  ? Colors.white
                                  : const Color(0xFF343131),
                      width: 1,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 7,
                    backgroundColor: isFullPackage && isSelect
                        ? const Color(0xFF343131)
                        : isSelect
                            ? Colors.white
                            : Colors.transparent,
                  ))
            ],
          ),
        ),
      ),
    );
  }
}
