import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';

class SubscriptionPlansTapBarWidget extends StatelessWidget {
  final List<String> tabs;
  final int currentIndex;
  final Function(int)? onTab;

  const SubscriptionPlansTapBarWidget({
    super.key,
    required this.tabs,
    required this.currentIndex,
    required this.onTab,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DefaultTabController(
          initialIndex: currentIndex,
          length: tabs.length,
          child: TabBar(
            physics: const BouncingScrollPhysics(),
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            splashBorderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
            tabAlignment: TabAlignment.start,
            unselectedLabelStyle: context.labelLarge,
            onTap: onTab,
            indicatorColor: Colors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            unselectedLabelColor: context.isDark
                ? ColorManager.grey.withOpacity(0.5)
                : ColorManager.secondaryColor,
            labelPadding: const EdgeInsets.all(8),
            labelColor: ColorManager.white,
            labelStyle: context.subTitle.copyWith(fontWeight: FontWeight.w400),
            isScrollable: true,
            tabs: tabs.indexed.map((e) {
              final index = e.$1;
              return WidgetAnimator(
                delay: Duration(milliseconds: 300 * index),
                child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    decoration: BoxDecoration(
                        color: currentIndex == index
                            ? ColorManager.primaryColor
                            : Theme.of(context).cardColor,
                        borderRadius:
                            BorderRadius.circular(AppRadius.tabBarRadius)),
                    child: Text(
                      e.$2,
                      style: context.labelLarge.copyWith(
                          color: currentIndex == index
                              ? ColorManager.white
                              : null),
                    )),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
