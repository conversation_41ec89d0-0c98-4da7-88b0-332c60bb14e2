import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/product_details/product_details_screen.dart';
import 'package:provider/provider.dart';

import '../../../../core/consts/api_strings.dart';
import '../../../../core/resources/app_radius.dart';
import '../../../../core/resources/app_spaces.dart';
import '../../../../core/resources/theme/theme.dart';
import '../../../../core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import '../../../products/view_model/products_view_model.dart';
import 'add_to_cart/add_to_cart_dialog.dart';

class StoreProductCardWidget extends HookWidget {
  final ProductModel productModel;

  const StoreProductCardWidget({super.key, required this.productModel});

  @override
  Widget build(BuildContext context) {
    final valueNotifiers = {
      ApiStrings.colors: useState<List<ExtraSettingsModel>>(
          [productModel.colors.firstOrNull ?? ExtraSettingsModel()]),
      ApiStrings.sizes: useState<List<ExtraSettingsModel>>(
          [productModel.sizes.firstOrNull ?? ExtraSettingsModel()]),
    };

    final isSale = productModel.isSale;
    final isActive = useState(productModel.isInStock);
    final productVM = context.read<ProductVM>();

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        color: Theme.of(context).cardColor,
      ),
      child: Stack(
        children: [
          InkWell(
            onTap: () => context.to(ProductsDetailsScreen(
                productModel: productModel,
                categoryModel: productModel.category!)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Product image
                SizedBox(
                  height: 170.h,
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(
                          AppRadius.baseContainerRadius,
                        ),
                        child: Image.network(
                          productModel.thumbnail?.url ?? '',
                          width: double.infinity,
                          fit: BoxFit.cover,
                          height: 170.h,
                        ),
                      ),
                    ],
                  ),
                ),

                context.smallGap,

                //! product title & price & sale price
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productVM.getProductTitle(
                              product: productModel, context: context) ??
                          '',
                      style: context.labelLarge.copyWith(
                          overflow: TextOverflow.ellipsis,
                          fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    context.xSmallGap,

                    //! price and sale price
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: context.isEng
                          ? Alignment.centerLeft
                          : Alignment.centerRight,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (isSale) ...[
                            Text(
                              productModel.salePrice.toCurrency(context),
                              style: context.title.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            context.smallGap,
                          ],
                          Text(
                            productModel.totalPrice.toCurrency(context),
                            style: isSale
                                ? context.greyLabelMedium.copyWith(
                                    fontWeight: isSale ? null : FontWeight.bold,
                                    decoration: isSale
                                        ? TextDecoration.lineThrough
                                        : null,
                                    decorationColor: context.isDark
                                        ? ColorManager.grey.withOpacity(0.7)
                                        : ColorManager.darkGrey
                                            .withOpacity(0.5),
                                  )
                                : context.title.copyWith(
                                    fontWeight: isSale ? null : FontWeight.bold,
                                    decoration: isSale
                                        ? TextDecoration.lineThrough
                                        : null,
                                  ),
                          ),
                        ],
                      ),
                    ).sized(
                      width: context.width * 0.5,
                    ),

                    context.smallGap,
                  ],
                ).paddingOnly(
                    left: AppSpaces.smallPadding,
                    right: AppSpaces.smallPadding),
              ],
            ),
          ),

          // //! Is Active
          Positioned(
              top: 0,
              left: 0,
              child: SwitchButtonWidget(
                value: isActive,
                onChanged: (value) async {
                  await productVM.editStatusProduct(
                    context,
                    product: productModel,
                    status: value,
                  );

                  isActive.value = value;
                },
              )),

          //! Add To Cart
          Positioned(
              top: 5,
              right: 5,
              child: CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                child: IconButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return BaseDialog(
                            backgroundColor: context.appTheme.cardColor,
                            isLoading: false,
                            child: AddToCartWidgetDialog(
                              productModel: productModel,
                              valueNotifiers: valueNotifiers,
                            ),
                          );
                        },
                      );
                    },
                    icon: const Icon(
                      Icons.add_shopping_cart_rounded,
                      size: 20,
                      color: ColorManager.white,
                    )),
              ))
        ],
      ),
    );
  }
}
