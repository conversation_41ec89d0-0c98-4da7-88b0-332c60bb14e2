import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/tab_bar_widgets/tab_bar_widget.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/home_tab_bar_provider.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_radius.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/animated/entrance_fader.dart';
import '../../../view_model/stote_tab_bar_view_model.dart';

class StoreTapBarWidget extends StatelessWidget {
  final List<String> tabs;
  final Function(int)? onTab;

  const StoreTapBarWidget({
    super.key,
    required this.tabs,
    required this.onTab,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<StoreTabBarVM>(
      builder: (context, storeTabBarVM, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DefaultTabController(
              initialIndex: 0,
              length: tabs.length,
              child: TabBar(
                splashBorderRadius: BorderRadius.circular(AppRadius.baseRadius),
                tabAlignment: TabAlignment.start,
                unselectedLabelStyle: context.labelLarge,
                onTap: onTab,
                indicatorSize: TabBarIndicatorSize.label,
                unselectedLabelColor: context.isDark
                    ? ColorManager.grey.withOpacity(0.5)
                    : ColorManager.secondaryColor,
                indicatorColor: ColorManager.primaryColor,
                labelPadding: const EdgeInsets.all(8),
                labelColor: ColorManager.white,
                labelStyle:
                    context.subTitle.copyWith(fontWeight: FontWeight.w400),
                isScrollable: true,
                tabs: tabs.indexed.map((e) {
                  final index = e.$1;

                  final isFocused = storeTabBarVM.currentIndex == index;

                  return Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    child: Text(e.$2,
                        style: isFocused
                            ? context.title
                                .copyWith(color: ColorManager.primaryColor)
                            : null),
                  );
                }).toList(),
              ),
            ),
          ],
        );
      },
    );
  }
}
