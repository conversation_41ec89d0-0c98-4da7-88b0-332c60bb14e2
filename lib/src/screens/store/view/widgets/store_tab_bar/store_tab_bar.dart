import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/category_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/store_tab_bar/store_tab_bar_widget.dart';
import 'package:provider/provider.dart';

import '../../../view_model/stote_tab_bar_view_model.dart';

class StoreTabBar extends HookWidget {
  final List<CategoryModel> tabs;
  final String query;

  const StoreTabBar({super.key, required this.tabs, required this.query});

  @override
  Widget build(BuildContext context) {
    final categoriesNameList = tabs.map((e) {
      return e.englishName!;
    }).toList();

    final list = useState([context.tr.all]);

    useEffect(() {
      if (list.value.length <= 1) {
        list.value.addAll(categoriesNameList);
      }
      return () {};
    });

    return Consumer<StoreTabBarVM>(
      builder: (context, storeTabBarVM, child) {
        return StoreTapBarWidget(
          tabs: list.value,
          onTab: (index) {
            final isCurrentIndex = storeTabBarVM.currentIndex == index;

            if (isCurrentIndex) {
              return;
            }

            storeTabBarVM.setCurrentIndex(index);

            if (index == 0) {
              context.read<ProductVM>().getAllProducts(context, query: query);
            } else {
              context.read<ProductVM>().getProductsByCategory(context,
                  catId: tabs[index - 1].documentId!, query: query);
            }
          },
        );
      },
    );
  }
}
