import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/store_product_card_widget.dart';
import 'package:provider/provider.dart';

import '../../../products/view_model/products_view_model.dart';
import '../../view_model/stote_tab_bar_view_model.dart';

class StoreProductsGridView extends HookWidget {
  final TextEditingController query;

  const StoreProductsGridView({super.key, required this.query});

  @override
  Widget build(BuildContext context) {
    return Consumer2<StoreTabBarVM, ProductVM>(
      builder: (context, storeTabBarVM, productsVM, child) {
        final isAll = storeTabBarVM.currentIndex == 0;

        final products =
            isAll ? productsVM.allProducts : productsVM.productsByCategory;

        final searchedList = productsVM.searchedProducts;

        if (productsVM.isLoading) {
          return const LoadingWidget();
        }

        if (products.isEmpty || searchedList.isEmpty) {
          final message = searchedList.isEmpty
              ? context.tr.noProductsFound
              : context.tr.noProductsInThisCategory;

          return EmptyDataWidget(
            message: message,
          );
        }

        return AutoHeightGridView(
            itemCount: searchedList.length,
            crossAxisCount: 2,
            mainAxisSpacing: 15.h,
            crossAxisSpacing: 10,
            builder: (BuildContext context, int index) {
              final product = searchedList[index];

              return StoreProductCardWidget(
                productModel: product,
              );
            });
      },
    );
  }
}
