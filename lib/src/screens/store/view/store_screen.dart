import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view/cart_screen.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/main_screen.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/home_tab_bar_provider.dart';
import 'package:idea2app_vendor_app/src/screens/invoices/view/store_invoices_screen.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/store_tab_bar/store_tab_bar.dart';
import 'package:idea2app_vendor_app/src/screens/store/view_model/stote_tab_bar_view_model.dart';
import 'package:provider/provider.dart';

import '../../products/view_model/products_view_model.dart';
import 'widgets/store_products_grid_view.dart';

class StoreScreen extends HookWidget {
  const StoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final productsVM = context.read<ProductVM>();
    final catVM = context.read<CategoryVM>();
    final storeTabBarVM = context.read<StoreTabBarVM>();
    final cartVM = context.read<CartVM>();

    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);

    void getData() {
      storeTabBarVM.setCurrentIndex(0);
      productsVM.getAllProducts(context);
      cartVM.getCart();

      if (catVM.categories.isEmpty) {
        catVM.getCategories(context);
      }
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getData();
      });
      return () {};
    }, []);
    return Consumer3<CategoryVM, HomeTabBarVM, CartVM>(
      builder: (context, categoryVM, homeTabBarVM, cartVM, child) {
        final colorIcon =
            context.isDark ? ColorManager.white : ColorManager.black;
        return Scaffold(
          appBar: MainAppBar(
            haveBackButton: true,
            title: context.tr.store,
            onBackPressed: () {
              context.read<OrderVM>().getOrders(context);

              context.toReplacement(const MainScreen());
            },
            actionWidget: Row(
              children: [
                CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  radius: 20,
                  child: IconButton(
                    onPressed: () => context.to(const StoreInvoicesScreen()),
                    icon: const Icon(
                      Icons.inventory_outlined,
                      color: ColorManager.white,
                    ),
                  ),
                ),
                context.mediumGap,
                Stack(
                  alignment: Alignment.topRight,
                  children: [
                    CircleAvatar(
                      backgroundColor: ColorManager.primaryColor,
                      radius: 20,
                      child: IconButton(
                        onPressed: () => context.to(const CartScreen()),
                        icon: const Icon(
                          Icons.shopping_cart,
                          color: ColorManager.white,
                        ),
                      ),
                    ),
                    if (cartVM.cartList.isNotEmpty)
                      CircleAvatar(
                        radius: 8.r,
                        backgroundColor: ColorManager.black,
                        child: Text(
                          cartVM.counter.toString(),
                          style: context.whiteLabelMedium,
                        ),
                      )
                  ],
                ),
              ],
            ),
          ),
          body: Consumer2<CategoryVM, StoreTabBarVM>(
            builder: (context, categoryVM, storeTabBar, child) {
              return Column(
                children: [
                  StoreTabBar(
                    tabs: categoryVM.categories,
                    query: queryController.text,
                  ),
                  context.mediumGap,
                  BaseTextField(
                    controller: queryController,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        isSearch.value = false;
                        productsVM.filterSearchedList(
                            query: '',
                            isAll: storeTabBar.currentIndex == 0,
                            context: context);
                        return;
                      }

                      isSearch.value = true;

                      queryController.text = value;

                      productsVM.filterSearchedList(
                          query: value,
                          isAll: storeTabBar.currentIndex == 0,
                          context: context);
                    },
                    suffixIcon: Icon(
                            isSearch.value
                                ? Icons.close
                                : CupertinoIcons.search,
                            color: colorIcon)
                        .onTap(() {
                      queryController.clear();

                      isSearch.value = false;

                      productsVM.filterSearchedList(
                          query: '',
                          isAll: storeTabBar.currentIndex == 0,
                          context: context);
                    }),
                    hint: context.tr.searchProduct,
                    withoutEnter: true,
                  ),
                  context.largeGap,
                  Expanded(
                      child: StoreProductsGridView(
                    query: queryController,
                  ))
                ],
              ).paddingSymmetric(horizontal: AppSpaces.mediumPadding);
            },
          ),
        );
      },
    ).onWillPopMainScreen(context);
  }
}
