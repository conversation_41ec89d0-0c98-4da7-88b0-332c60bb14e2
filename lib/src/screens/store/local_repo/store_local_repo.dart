import 'dart:developer';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';

import '../../../core/data/local/shared_preferences/get_storage.dart';

class StoreLocalRepo {
  Future<void> saveProductToCart(
      {required ProductQuantityModel productQuantity}) async {
    try {
      List<ProductQuantityModel> oldCartData = await getCart();

      bool isFound = false;

      for (int i = 0; i < oldCartData.length; i++) {
        if (productQuantity.id == oldCartData[i].id) {
          isFound = true;

          final updatedQuantity =
              productQuantity.quantity! + oldCartData[i].quantity!;

          final productPrice = productQuantity.actualPrice;

          final totalPrice = productPrice * updatedQuantity;

          oldCartData[i] = productQuantity.copyWith(
            quantity: updatedQuantity,
            totalPrice: totalPrice,
          );

          break;
        }
      }

      if (isFound) {
        await GetStorageHandler.setLocalData(
            key: ApiStrings.cart,
            value: oldCartData.map((e) => e.toLocalJson()).toList());
      } else {
        await GetStorageHandler.setLocalData(key: ApiStrings.cart, value: [
          ...oldCartData.map((e) => e.toLocalJson()),
          productQuantity.toLocalJson()
        ]);
      }
    } catch (e) {
      Log.e('ERROR OCCURRED WHILE SAVING TO CART ============ ${e.toString()}');
      rethrow;
    }
  }

  Future<List<ProductQuantityModel>> getCart() async {
    final res = await GetStorageHandler.getLocalData(key: ApiStrings.cart);
    Log.w('res ======= ${res}');

    final cartData =
        (res as List?)?.map((e) => ProductQuantityModel.fromJson(e)).toList();

    Log.w('cartData In Get ======= ${cartData}');
    return cartData ?? [];
  }

  //! Update Quantity ======================================
  Future<void> updateQuantity(
      {required ProductQuantityModel productQuantity,
      bool isIncrease = true}) async {
    try {
      List<ProductQuantityModel> oldCartData = await getCart();

      log('asfsafa ${productQuantity.price}');
      for (int i = 0; i < oldCartData.length; i++) {
        if (productQuantity.id == oldCartData[i].id) {
          late int updatedQuantity;

          if (isIncrease) {
            updatedQuantity = oldCartData[i].quantity! + 1;
          } else {
            updatedQuantity = oldCartData[i].quantity! - 1;
          }

          final productPrice = productQuantity.actualPrice;

          final totalPrice = productPrice * updatedQuantity;

          oldCartData[i] = productQuantity.copyWith(
              quantity: updatedQuantity, totalPrice: totalPrice);

          break;
        }
      }

      await GetStorageHandler.setLocalData(
          key: ApiStrings.cart,
          value: oldCartData.map((e) => e.toLocalJson()).toList());
    } catch (e) {
      Log.e('ERROR OCCURRED WHILE SAVING TO CART ============ ${e.toString()}');
      rethrow;
    }
  }

  //! Delete Product From Cart  ======================================
  Future<void> deleteFromCart({required String id}) async {
    try {
      final oldCartData = await getCart();

      final updatedList = oldCartData
          .where((e) => e.id != id)
          .map((e) => e.toLocalJson())
          .toList();

      await GetStorageHandler.setLocalData(
        key: ApiStrings.cart,
        value: updatedList,
      );
    } catch (e) {
      Log.e('Delete Failed =========== ${e.toString()}');
    }
  }
}
