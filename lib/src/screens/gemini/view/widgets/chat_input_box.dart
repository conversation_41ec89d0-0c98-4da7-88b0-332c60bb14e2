import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

class ChatInputBox extends StatelessWidget {
  final TextEditingController? controller;
  final VoidCallback? onSend, onClickCamera;

  const ChatInputBox({
    super.key,
    this.controller,
    this.onSend,
    this.onClickCamera,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: context.appTheme.cardColor,
      margin: const EdgeInsets.only(
        bottom: AppSpaces.mediumPadding,
        left: AppSpaces.smallPadding,
        right: AppSpaces.smallPadding,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
            vertical: AppSpaces.mediumPadding / 1.5,
            horizontal: AppSpaces.xSmallPadding),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (onClickCamera != null)
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.xSmallPadding),
                child: Icon<PERSON><PERSON>on(
                    highlightColor: ColorManager.primaryColor.withOpacity(0.1),
                    onPressed: onClickCamera,
                    color: Theme.of(context).colorScheme.onSecondary,
                    icon: Icon(
                      Icons.photo_filter,
                      color: ColorManager.primaryColor,
                      size: 28,
                    )),
              ),
            Expanded(
                child: TextField(
              controller: controller,
              minLines: 1,
              maxLines: 6,
              cursorColor: Theme.of(context).colorScheme.inversePrimary,
              textInputAction: TextInputAction.newline,
              keyboardType: TextInputType.multiline,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.mediumPadding - 4,
                  vertical: AppSpaces.xSmallPadding,
                ),
                hintText: context.tr.aiHintMessage,
                hintStyle: context.hint,
                border: InputBorder.none,
              ),
              onTapOutside: (event) =>
                  FocusManager.instance.primaryFocus?.unfocus(),
            )),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.xSmallPadding),
              child: FloatingActionButton.small(
                key: const Key('sendButton'),
                backgroundColor: ColorManager.primaryColor,
                onPressed: onSend,
                child: const Icon(
                  Icons.send_rounded,
                  color: Colors.white,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
