import '../../../core/consts/api_strings.dart';
import '../../../core/shared_models/base_media_model.dart';

class ExpensesModel {
  final int? id;
  final String? documentId;
  final String title;
  final num price;
  final bool isPaid;
  final String createdAt;
  final BaseMediaModel? image;

  const ExpensesModel({
    this.documentId,
    this.id,
    this.title = '',
    this.isPaid = false,
    this.price = 0,
    this.createdAt = '',
    this.image,
  });

  factory ExpensesModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.attachment] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.attachment])
        : null;
    return ExpensesModel(
      id: json[ApiStrings.id],
      documentId: json[ApiStrings.documentId],
      price: json[ApiStrings.price],
      isPaid: json[ApiStrings.isPaid],
      title: json[ApiStrings.title] ?? '',
      image: image,
      createdAt: json[ApiStrings.createdAt] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (documentId != null) ApiStrings.documentId: documentId,
      if (title.isNotEmpty) ApiStrings.title: title,
      if (price != 0) ApiStrings.price: price,
      ApiStrings.isPaid: isPaid,
    };
  }
}
