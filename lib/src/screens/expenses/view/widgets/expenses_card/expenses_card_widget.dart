import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/expenses/controller/expenses_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/consts/app_constants.dart';
import '../../../../../core/resources/app_radius.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/dialogs/show_dialog.dart';
import '../../../model/expenses_model.dart';
import '../add_expenses_dialog.dart';

class ExpensesCardWidget extends StatelessWidget {
  final ExpensesModel expense;

  const ExpensesCardWidget({super.key, required this.expense});

  @override
  Widget build(BuildContext context) {
    // final expensesCtrl = ref.watch(expensesControllerNotifierProvider(context));
    final isImageFound = expense.image?.url != null;
    final expensesVM = context.read<ExpensesVM>();

    return Container(
      decoration: BoxDecoration(
          color: context.appTheme.cardColor,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: context.isDark
              ? ConstantsWidgets.darkBoxShadowFromBottom
              : ConstantsWidgets.boxShadowFromBottom),
      child: Column(
        children: [
          if (isImageFound)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppRadius.baseContainerRadius),
                topRight: Radius.circular(AppRadius.baseContainerRadius),
              ),
              child: Image.network(
                expense.image?.url ?? AppConsts.placeholderImage,
                width: double.infinity,
                height: 150,
                fit: BoxFit.cover,
              ),
            ),
          ListTile(
            selectedTileColor: context.appTheme.cardColor,
            tileColor: context.appTheme.cardColor,
            shape: RoundedRectangleBorder(
              borderRadius: isImageFound
                  ? const BorderRadius.only(
                      bottomRight:
                          Radius.circular(AppRadius.baseContainerRadius),
                      bottomLeft:
                          Radius.circular(AppRadius.baseContainerRadius),
                    )
                  : BorderRadius.circular(AppRadius.baseContainerRadius),
            ),
            title: Text(
              expense.title,
              style: context.title.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              expense.price.toString().toCurrency(context),
              style: context.subHeadLine
                  .copyWith(color: ColorManager.primaryColor),
            ),
            leading: CircleAvatar(
              backgroundColor: expense.isPaid
                  ? ColorManager.primaryColor
                  : ColorManager.errorColor,
              radius: 20,
              child: IconButton(
                  onPressed: () async {
                    await context.read<ExpensesVM>().editExpensesStatus(context,
                        isPaid: !expense.isPaid,
                        documentId: expense.documentId!);
                  },
                  icon: const Icon(
                    Icons.playlist_add_check_outlined,
                    color: ColorManager.white,
                  )),
            ),
            trailing: SizedBox(
              width: kIsWeb ? 80 : 80.w,
              child: Row(
                children: [
                  const CircleAvatar(
                          backgroundColor: ColorManager.successColor,
                          child: BaseLottieWidget.icon(
                              'assets/animated/edit.json',
                              width: 20,
                              height: 20))
                      .onTapWithRipple(() {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AddExpensesDialog(
                          expenses: expense,
                        );
                      },
                    );
                  }),
                  context.smallGap,
                  //? Delete Button
                  const CircleAvatar(
                          backgroundColor: ColorManager.errorColor,
                          child: BaseLottieWidget.icon(Assets.animatedDelete,
                              width: 20, height: 20))
                      .onTapWithRipple(() {
                    showAlertDialog(context,
                        child: Text(
                            context.tr.areYouSureYouWantToDeleteThisExpense,
                            style: context.labelLarge), onConfirm: () async {
                      final expensesVM = context.read<ExpensesVM>();

                      await expensesVM.deleteExpenses(context,
                          expense: expense);
                    });
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
