import 'dart:async';

import '../../../core/data/remote/app_exception.dart';
import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../model/expenses_model.dart';

class ExpensesRepository {
  final BaseApiServices networkApiServices;

  ExpensesRepository({required this.networkApiServices});

  Future<List<ExpensesModel>> getExpenses() async {
    final response =
        await networkApiServices.getResponse(ApiEndPoints.expenses);

    final expensesData =
        (response as List).map((e) => ExpensesModel.fromJson(e)).toList();

    return expensesData;
  }

  Future<void> addEditExpenses({
    required ExpensesModel expenses,
    bool isEdit = false,
    required String filePath,
  }) async {
    try {
      if (isEdit) {
        await networkApiServices.putResponse(ApiEndPoints.expenses,
            fieldName: 'attachment',
            fileResult: [filePath],
            data: expenses.toJson());
      } else {
        await networkApiServices.postResponse(ApiEndPoints.expenses,
            fieldName: 'attachment',
            fileResult: [filePath],
            data: expenses.toJson());
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<void> editExpensesStatus({required ExpensesModel expenses}) async {
    try {
      await networkApiServices.putResponse(ApiEndPoints.expenses,
          data: expenses.toJson());
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  Future<void> deleteExpenses({
    required ExpensesModel expense,
  }) async {
    try {
      await networkApiServices.deleteResponse(
        '${ApiEndPoints.expenses}/${expense.documentId}',
      );
      if (expense.image != null) {
        await networkApiServices.deleteImage(imageId: expense.image?.id);
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
