import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/model/subscription_request_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/view_model/subscription_request_view_model.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

class SubscriptionCardBottomSection extends StatelessWidget {
  final SubscriptionRequestModel subscriptionRequest;

  const SubscriptionCardBottomSection(
      {super.key, required this.subscriptionRequest});

  @override
  Widget build(BuildContext context) {
    final isLoadingAuth = context.watch<AuthVM>().isLoading;

    return Consumer<SubscriptionRequestVM>(
      builder: (context, subscriptionRequestVM, child) {
        return Row(
          children: [
            Expanded(
                child: Button(
                    label: context.tr.submit,
                    onPressed: () {
                      QuickAlert.show(
                        context: context,
                        confirmBtnColor: ColorManager.primaryColor,
                        title: context.tr.approveRequest,
                        text: context.tr
                            .areYouSureYouWantToApproveThisSubscriptionRequest,
                        type: QuickAlertType.success,
                        showCancelBtn: true,
                        confirmBtnText: context.tr.approve,
                        onConfirmBtnTap: () async {
                          context.back();
                          final isFromAdmin =
                              subscriptionRequest.paidAmount != null;

                          await subscriptionRequestVM
                              .approveSubscriptionRequest(
                            context,
                            subscriptionRequest: subscriptionRequest,
                            cannotRenew: isFromAdmin &&
                                subscriptionRequestVM.currentIndex == 0,
                          );

                          subscriptionRequestVM
                              .getSubscriptionRequests(context);
                        },
                      );
                    })),
            context.smallGap,
            CircleAvatar(
              backgroundColor: ColorManager.errorColor,
              child: IconButton(
                  onPressed: () => showAlertDialog(
                          isLoading: isLoadingAuth,
                          context,
                          child: Text(
                              context.tr
                                  .areYouSureYouWantToDeleteThisSubscriptionRequest,
                              style: context.labelLarge), onConfirm: () async {
                        context.back();

                        await subscriptionRequestVM.deleteSubscriptionRequest(
                          context,
                          documentId: subscriptionRequest.documentId,
                        );

                        subscriptionRequestVM.getSubscriptionRequests(context);
                      }),
                  icon: const BaseLottieWidget.icon(
                    Assets.animatedDelete,
                    height: AppSpaces.iconSize,
                  )),
            ),
          ],
        );
      },
    );
  }
}
