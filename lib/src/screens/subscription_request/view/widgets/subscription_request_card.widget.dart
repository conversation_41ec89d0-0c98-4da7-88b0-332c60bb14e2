import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/vendor_card/widgets/vendor_card_middle_section.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/widgets/vendor_card/widgets/vendor_card_top_section.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/model/subscription_request_model.dart';

import 'subscription_card_bottom_section.dart';

class SubscriptionCardWidget extends StatelessWidget {
  final SubscriptionRequestModel subscriptionRequest;

  const SubscriptionCardWidget({super.key, required this.subscriptionRequest});

  @override
  Widget build(BuildContext context) {
    final vendor = subscriptionRequest.vendor ?? const VendorModel();
    final paymentMethodToIcon =
        subscriptionRequest.paymentMethod == 'vodafone_cash'
            ? Assets.vCash
            : Assets.instapayCash;

    return Column(
      children: [
        // * Vendor Details
        Container(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          decoration: BoxDecoration(
            color: context.appTheme.cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppRadius.tabBarRadius),
              topRight: Radius.circular(AppRadius.tabBarRadius),
            ),
            boxShadow: context.isDark
                ? ConstantsWidgets.darkBoxShadowFromBottom
                : ConstantsWidgets.boxShadow,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // * Top Section (Vendor Image, Vendor Name, Vendor Type)
              VendorCardTopSection(
                vendor: vendor,
              ),

              const Divider(),

              context.xSmallGap,

              // * Middle Section (Vendor Type, Remaining Amount - Phone - Email)
              VendorCardMiddleSection(
                vendor: vendor,
              ),
            ],
          ),
        ),

        // * Subscription Request Details
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          decoration: BoxDecoration(
            color: context.appTheme.cardColor,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(AppRadius.tabBarRadius),
              bottomRight: Radius.circular(AppRadius.tabBarRadius),
            ),
            boxShadow: context.isDark
                ? ConstantsWidgets.darkBoxShadowFromBottom
                : ConstantsWidgets.boxShadow,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    subscriptionRequest
                        .createdAt.formatDateToStringWithTimeAndDayLocal,
                    style: context.labelLarge.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.asset(
                        paymentMethodToIcon,
                        fit: BoxFit.cover,
                        width: 100,
                        height: 35,
                      ))
                ],
              ),
              context.mediumGap,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(subscriptionRequest.pricingModel?.name ?? '',
                      style: context.labelLarge
                          .copyWith(fontWeight: FontWeight.w500)),
                  Text(
                      subscriptionRequest.pricingModel?.price
                              .toCurrency(context) ??
                          '',
                      style:
                          context.title.copyWith(fontWeight: FontWeight.w500)),
                ],
              ),
              if (subscriptionRequest.paidAmount != null) ...[
                context.mediumGap,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(context.tr.remainingAmount,
                        style: context.labelLarge
                            .copyWith(fontWeight: FontWeight.w500)),
                    Text(
                        subscriptionRequest.remainingAmount.toCurrency(context),
                        style: context.title
                            .copyWith(fontWeight: FontWeight.w500)),
                  ],
                ),
              ],
              context.smallGap,
              if (subscriptionRequest.paymentAttachment != null)
                GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return Dialog(
                          child: ClipRRect(
                            borderRadius:
                                BorderRadius.circular(AppRadius.baseRadius),
                            child: Image.network(
                              subscriptionRequest.paymentAttachment?.url ?? '',
                            ),
                          ),
                        );
                      },
                    );
                  },
                  child: SizedBox(
                    height: 140,
                    width: double.infinity,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                      child: Image.network(
                        subscriptionRequest.paymentAttachment?.url ?? '',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              context.mediumGap,
              SubscriptionCardBottomSection(
                  subscriptionRequest: subscriptionRequest),
            ],
          ),
        ),
      ],
    );
  }
}
