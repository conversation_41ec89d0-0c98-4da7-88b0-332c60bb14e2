import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:permission_handler/permission_handler.dart';

class LocalMediaRepo {
  final ImagePicker _picker = ImagePicker();

  Future<List<XFile>?> pickFiles(BuildContext context,
      {bool imageUpload = true,
      bool uploadMultiple = true,
      int oldFilesLength = 2}) async {
    try {
      if (!kIsWeb) {
        await _getPermission();
      }
      List<XFile>? result = [];
      if(uploadMultiple) {
        result  = await _picker.pickMultiImage(
          imageQuality: 100,
          limit: 4,
        );
      } else {
        final pickedFile = await _picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 100,
        );
        if (pickedFile != null) {
          result.add(pickedFile);
        }
      }


      final totalMaxFilesLength = 4 - oldFilesLength;

      if (!context.mounted) return null;

      //! Check if any file more than 20 mb
      final isMoreThan20MG = _checkFilesSize(context, result: result);

      if (isMoreThan20MG) return null;

      //! Check if total files more than 4
      final isMoreThan4Files = _checkFilesLength(
        context,
        result: result,
        totalMaxFilesLength: totalMaxFilesLength,
      );

      if (isMoreThan4Files) return null;

      return result;
    } catch (e) {
      debugPrint('error $e');
      return null;
    }
  }

  bool _checkFilesSize(
    BuildContext context, {
    required List<XFile>? result,
  }) {
    final anyImageMoreThan20MG = result?.any((element) {
          final file = File(element.path);
          final fileSize = file.lengthSync();
          return fileSize > 20 * 1024 * 1024;
        }) ??
        false;

    if (result != null && result.isNotEmpty && anyImageMoreThan20MG) {
      if (context.mounted) {
        context.showBarMessage(context.tr.maxUploadFileSizeIsOnly10MB,
            isError: true);
      }
      return true;
    }

    return false;
  }

  bool _checkFilesLength(
    BuildContext context, {
    required List<XFile>? result,
    required int totalMaxFilesLength,
  }) {
    if (result != null &&
        result.isNotEmpty &&
        result.length > totalMaxFilesLength) {
      if (context.mounted) {
        context.showBarMessage(context.tr.maxUploadFilesIsOnly4, isError: true);
      }
      return true;
    }

    return false;
  }

  Future<void> _getPermission() async {
    if (await Permission.storage.isGranted) return;
    try {
      await Permission.storage.request();
    } catch (e) {
      debugPrint('error $e');
    }
  }
}

// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/foundation.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:permission_handler/permission_handler.dart';
//
// class LocalMediaRepo {
//   Future<FilePickerResult?> pickFiles(BuildContext context,
//       {bool imageUpload = true,
//       bool uploadMultiple = true,
//       int oldFilesLength = 2}) async {
//     try {
//       if (!kIsWeb) {
//         await _getPermission();
//       }
//       FilePickerResult? result = await FilePicker.platform.pickFiles(
//         type: imageUpload ? FileType.image : FileType.any,
//         allowMultiple: uploadMultiple,
//         compressionQuality: 100,
//         allowCompression: false,
//
//       );
//
//       final totalMaxFilesLength = 4 - oldFilesLength;
//
//       if (!context.mounted) return null;
//
//       //! Check if any file more than 10 mg
//       final isMoreThan20MG = _checkFilesSize(context, result: result);
//
//       if (isMoreThan20MG) return null;
//
//       //! Check if total files more than 4
//       final isMoreThan4Files = _checkFilesLength(
//         context,
//         result: result,
//         totalMaxFilesLength: totalMaxFilesLength,
//       );
//
//       if (isMoreThan4Files) return null;
//
//       return result;
//     } catch (e) {
//       debugPrint('error $e');
//       return null;
//     }
//   }
//
//   bool _checkFilesSize(
//     BuildContext context, {
//     required FilePickerResult? result,
//   }) {
//     final anyImageMoreThan10MG =
//         result?.files.any((element) => element.size > 10 * 1024 * 1024) ??
//             false;
//
//     if (result != null && result.files.isNotEmpty && anyImageMoreThan10MG) {
//       if (context.mounted) {
//         context.showBarMessage(context.tr.maxUploadFileSizeIsOnly10MB,
//             isError: true);
//       }
//       return true;
//     }
//
//     return false;
//   }
//
//   bool _checkFilesLength(
//     BuildContext context, {
//     required FilePickerResult? result,
//     required int totalMaxFilesLength,
//   }) {
//     if (result != null &&
//         result.files.isNotEmpty &&
//         result.files.length > totalMaxFilesLength) {
//       if (context.mounted) {
//         context.showBarMessage(context.tr.maxUploadFilesIsOnly4, isError: true);
//       }
//       return true;
//     }
//
//     return false;
//   }
//
//   Future<void> _getPermission() async {
//     if (await Permission.storage.isGranted) return;
//     try {
//       await Permission.storage.request();
//     } catch (e) {
//       debugPrint('error $e');
//     }
//   }
// }
