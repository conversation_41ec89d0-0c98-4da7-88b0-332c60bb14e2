import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/banner/model/banner_model.dart';
import 'package:idea2app_vendor_app/src/screens/banner/repository/banner_repository.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view/banner_screen.dart';

class BannerVM extends BaseVM {
  final BannerRepository bannerRepository;

  BannerVM({required this.bannerRepository});

  var bannerList = <BannerModel>[];

  Future<void> getBannersData(BuildContext context) async {
    return await baseFunction(context, () async {
      bannerList = await bannerRepository.getBanner();
    });
  }

  Future<void> addBanner(BuildContext context,
      {required String title, required String fileResult}) async {
    return await baseFunction(
      context,
      () async {
        final banner = BannerModel(title: title);
        await bannerRepository.addBanner(
            banner: banner, fileResult: fileResult);
        if (!context.mounted) return;
        getBannersData(context);
      },
      additionalFunction: (_) {
        context.back();

        context.toReplacement(const BannerScreen());

        context.showBarMessage(context.tr.addedSuccessfully);
      },
    );
  }

  Future<void> editBanner(BuildContext context,
      {required String title,
      required String fileResult,
      required String? id}) async {
    return await baseFunction(
      context,
      () async {
        final banner = BannerModel(documentId: id, title: title);
        await bannerRepository.editBanner(
            banner: banner, fileResult: fileResult);
        if (!context.mounted) return;
        getBannersData(context);
      },
      additionalFunction: (_) {
        context.back();
        context.toReplacement(const BannerScreen());

        context.showBarMessage(context.tr.editedSuccessfully);
      },
    );
  }

  //? Delete Banner
  Future<void> deleteBanner(BuildContext context,
      {required BannerModel banner}) async {
    return await baseFunction(
      context,
      () async {
        await bannerRepository.deleteBanner(banner: banner);
      },
      type: FlushBarType.delete,
      additionalFunction: (_) {
        context.back();
        context.toReplacement(const BannerScreen());
      },
    );
  }
}
