import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/cached_images/main_cached_image.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/banner/model/banner_model.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view/widgets/add_banner_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view_model/banner_VM.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/theme/theme.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

class BannerCardWidget extends StatelessWidget {
  final BannerModel banner;

  const BannerCardWidget({super.key, required this.banner});

  @override
  Widget build(BuildContext context) {
    return Consumer<BannerVM>(
      builder: (context, bannerVM, child) {
        return Stack(
          children: [
            ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: BaseCachedImage(
                height: 160.h,
                width: double.infinity,
                fit: BoxFit.cover,
                banner.media?.url ?? '',
              ),
            ),
            if (banner.title.trim().isNotEmpty)
              Container(
                height: 160.h,
                width: double.infinity,
                padding: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(.3),
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius),
                ),
                child: Center(
                    child: Text(
                  banner.title,
                  textAlign: TextAlign.center,
                  style: context.whiteHeadLine,
                )),
              ),

            //? Edit Button
            Positioned(
              right: 10,
              top: 10,
              child: const CircleAvatar(
                      backgroundColor: ColorManager.successColor,
                      child: BaseLottieWidget.icon('assets/animated/edit.json',
                          width: 20, height: 20))
                  .onTapWithRipple(() {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AddBannerDialog(
                      banner: banner,
                    );
                  },
                );
              }),
            ),

            //? Delete Button
            Positioned(
              right: 10,
              bottom: 10,
              child: const CircleAvatar(
                      backgroundColor: ColorManager.errorColor,
                      child: BaseLottieWidget.icon(Assets.animatedDelete,
                          width: 20, height: 20))
                  .onTapWithRipple(() {
                showAlertDialog(context,
                    child: Text(context.tr.deleteProductConfirmationBanner,
                        style: context.labelLarge), onConfirm: () async {
                  await bannerVM.deleteBanner(context, banner: banner);
                });
              }),
            ),
          ],
        );
      },
    );
  }
}
