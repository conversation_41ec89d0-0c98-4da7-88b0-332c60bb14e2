import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/payment/model/payment_model.dart';
import 'package:idea2app_vendor_app/src/screens/payment/repository/payment_repository.dart';

import '../../../core/consts/app_constants.dart';
import '../view/payment_screen.dart';

class PaymentVM extends BaseVM {
  final PaymentRepository paymentRepository;

  PaymentVM({required this.paymentRepository});

  var paymentList = <PaymentModel>[];

  List<String> get paymentVMListCheck =>
      paymentList.map((e) => e.title ?? '').toList();

  List<String> get newList => AppConsts.paymentMethods
      .where((element) => !paymentVMListCheck.contains(element))
      .toList();

  Future<void> getPaymentsData(BuildContext context) async {
    return await baseFunction(context, () async {
      paymentList = await paymentRepository.getPayment();
    });
  }

  Future<void> addPayment(
    BuildContext context, {
    required String title,
    required String description,
  }) async {
    return await baseFunction(
      context,
      () async {
        final payment = PaymentModel(title: title, description: description);
        await paymentRepository.addPayment(
          payment: payment,
        );
        if (!context.mounted) return;
        getPaymentsData(context);
      },
      additionalFunction: (_) {
        context.back();

        context.toReplacement(const PaymentScreen());

        context.showBarMessage(context.tr.addedSuccessfully);
      },
    );
  }

  Future<void> editPayment(BuildContext context,
      {required String description,
      int? id,
      required String? documentId,
      required String title}) async {
    return await baseFunction(
      context,
      () async {
        final payment = PaymentModel(
            documentId: documentId,
            description: description,
            title: title,
            id: id);
        await paymentRepository.editPayment(payment: payment);
        if (!context.mounted) return;
        getPaymentsData(context);
      },
      additionalFunction: (_) {
        context.back();
        context.toReplacement(const PaymentScreen());

        context.showBarMessage(context.tr.editedSuccessfully);
      },
    );
  }

//? Delete Payment
  Future<void> deletePayment(BuildContext context, {required String id}) async {
    return await baseFunction(
      context,
      () async {
        await paymentRepository.deletePayment(id: id);
      },
      type: FlushBarType.delete,
      additionalFunction: (_) {
        context.back();
        context.toReplacement(const PaymentScreen());
      },
    );
  }
}
