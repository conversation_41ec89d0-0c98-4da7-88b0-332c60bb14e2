import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/payment/model/payment_model.dart';
import 'package:idea2app_vendor_app/src/screens/payment/view/widgets/edit_payment_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/payment/view_model/payment_view_model.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/shared_widgets/dialogs/show_dialog.dart';

class PaymentCard extends StatelessWidget {
  final PaymentModel payment;

  const PaymentCard({super.key, required this.payment});

  @override
  Widget build(BuildContext context) {
    final isCOD = payment.title == 'COD';
    final isOnlinePayment = payment.title == AppConsts.fawaterak ||
        payment.title == AppConsts.paypal;
    return Consumer<PaymentVM>(
      builder: (context, paymentVM, child) {
        final onePaymentAtLeast = paymentVM.paymentList.length > 1;

        return Container(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
              color: context.appTheme.cardColor,
              boxShadow: context.isDark
                  ? ConstantsWidgets.darkBoxShadowFromBottom
                  : ConstantsWidgets.boxShadowFromBottom),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Image.asset(
                convertTextToImage(payment.title ?? ''),
                height: 60.h,
                width: 70.w,
                fit: BoxFit.contain,
              ),
              context.smallGap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      payment.title ?? '',
                      style: context.subTitle,
                    ),
                    context.smallGap,
                    if (isOnlinePayment)
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: context.tr.youMust,
                              style: context.labelMedium,
                            ),
                            TextSpan(
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  // open whats
                                  launchUrl(
                                      Uri.parse('https://wa.me/+201011984272'));
                                  // context.toReplacement(const PaymentScreen());
                                },
                              text: context.tr.ContactSupport,
                              style: context.labelMedium.copyWith(
                                color: context.appTheme.primaryColor,
                                decoration: TextDecoration.underline,
                                decorationColor: context.appTheme.primaryColor,
                              ),
                            ),
                            TextSpan(
                                text: context.tr.ToActivateYourOnlinePayment,
                                style: context.labelMedium),
                          ],
                        ),
                      )
                    else
                      Text(
                        isCOD
                            ? context.tr.cash_on_delivery
                            : payment.description ?? '',
                        style: context.labelMedium,
                      ),
                  ],
                ),
              ),
              // if (!isCOD)
              Column(
                children: [
                  if (!isOnlinePayment && !isCOD)
                    IconButton(
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return EditPaymentDialog(
                                payment: payment,
                              );
                            },
                          );
                        },
                        icon: Icon(
                          Icons.edit_note_rounded,
                          color: context.isDark
                              ? ColorManager.white
                              : ColorManager.black,
                        )),
                  context.smallGap,

                  //? Delete Button
                  if (onePaymentAtLeast)
                    Icon(
                      Icons.delete_outline_rounded,
                      color: context.isDark
                          ? ColorManager.white
                          : ColorManager.black,
                    ).onTapWithRipple(() {
                      showAlertDialog(context,
                          child: Text(
                              context.tr.areYouSureYouWantToDeleteThisPayment,
                              style: context.labelLarge), onConfirm: () async {
                        await paymentVM.deletePayment(context,
                            id: payment.documentId!);
                      });
                    }),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

String convertTextToImage(String title) {
  switch (title) {
    case AppConsts.vodafoneCash:
      return Assets.assetsVodafoneCash;
    case AppConsts.cod:
      return Assets.imagesCod;
    case AppConsts.etisalatCash:
      return Assets.assetsEtisalatCash;
    case AppConsts.orangeCash:
      return Assets.assetsOrangeCash;
    case AppConsts.instapay:
      return Assets.assetsInstapay;
    case AppConsts.bankAccount:
      return Assets.imagesBankAccount;
    case AppConsts.fawaterak:
      return Assets.assetsImagesFawaterk;
    case AppConsts.paypal:
      return Assets.imagesPaypal;
  }

  return '';
}
