import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/pagination_widget/base_pagination_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shimmer_loading/home_loading/orders_loading.dart';
import 'package:idea2app_vendor_app/src/screens/invoices/view/widgets/Invoices_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/reports/view_model/report_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/reports/widgets/report_total_widget.dart';
import 'package:provider/provider.dart';

import '../../core/shared_widgets/animations/entrance_fader.dart';
import 'widgets/filter_by_dates_widget.dart';

class ReportsScreen extends HookWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final orderVM = context.read<OrderVM>();
    final reportsVM = context.read<ReportVM>();
    final watchReportsVM = context.watch<ReportVM>();
    final dateFilterCtrl =
        useTextEditingController(text: DateTime.now().formatDateToString);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        orderVM.clearFilter();

        orderVM.setOrderFilter(context,
            filter: orderVM.orderFilter.copyWith(
              limit: 10,
              allOrders: true,
              startDate: DateTime.now().copyWith(
                hour: 0,
                minute: 0,
                second: 0,
              ),
              endDate: DateTime.now().copyWith(
                hour: 23,
                minute: 59,
                second: 59,
              ),
            ));

        reportsVM.getReports(
          context,
          startDate: DateTime.now().copyWith(
            hour: 0,
            minute: 0,
            second: 0,
          ),
          endDate: DateTime.now().copyWith(
            hour: 23,
            minute: 59,
            second: 59,
          ),
        );
      });

      return () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          orderVM.clearFilter();
        });
      };
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.reports,
        haveBackButton: true,
      ),
      floatingActionButton: watchReportsVM.report == null ||
              watchReportsVM.report?.dailyEarnings == null ||
              watchReportsVM.report!.dailyEarnings.isEmpty
          ? null
          : FloatingActionButton(
              backgroundColor: ColorManager.secondaryColor,
              onPressed: () async {
                reportsVM.printReport(
                  context,
                  date: dateFilterCtrl.text,
                  report: reportsVM.report!,
                );
              },
              child: const Icon(
                Icons.print,
                color: ColorManager.white,
              ),
            ),
      body: Consumer2<OrderVM, ReportVM>(
        builder: (context, orderVM, reportVM, child) {
          final orders = orderVM.orders;

          final isInitialLoading = (orderVM.isLoading || orders.isEmpty) &&
              orderVM.orderFilter.limit == 10;

          return Column(
            children: [
              // * Reports Top Section & Date Filter
              ReportsDatesWidget(
                dateFilterCtrl: dateFilterCtrl,
              ),

              context.smallGap,

              // * Reports List
              const ReportTotalWidget(),

              if (isInitialLoading)
                Expanded(
                  child: EmptyDataWidget(
                    message: context.tr.noOrdersFound,
                    isLoading: orderVM.isLoading,
                    loadingWidget: const OrdersLoading(),
                  ),
                )
              else ...[
                Expanded(
                  child: BasePaginationWidget(
                    onRefresh: () {
                      final filter = orderVM.orderFilter.copyWith(
                        allOrders: true,
                      );

                      orderVM.setOrderFilter(context, filter: filter);

                      return Future.value();
                    },
                    isLoading: orderVM.isLoading,
                    onScrollEnd: () {
                      final filter = orderVM.orderFilter.copyWith(
                        limit: orderVM.orderFilter.limit + 10,
                        allOrders: true,
                      );

                      orderVM.setOrderFilter(context, filter: filter);
                    },
                    child: Stack(
                      children: [
                        ListView.separated(
                          padding: const EdgeInsets.all(AppSpaces.smallPadding),
                          shrinkWrap: true,
                          itemCount: orders.length,
                          itemBuilder: (context, index) {
                            var order = orders[index];
                            return EntranceFader(
                              delay: Duration(
                                  milliseconds:
                                      AppConsts.orderAnimatedDuration * index),
                              child: InvoicesCardWidget(
                                order: order,
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return context.xSmallGap;
                          },
                        ),
                        if (orderVM.isLoading)
                          const Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: LoadingWidget(
                              isLinear: true,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ]
            ],
          );
        },
      ),
    );
  }
}
