import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/store_order_invoices.dart';
import 'package:idea2app_vendor_app/src/screens/reports/model/report_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

Future<pw.Document> generateReport(
  BuildContext appContext, {
  required bool isEnglish,
  required ReportModel report,
  required String date,
}) async {
  final pdf = pw.Document();

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final int totalCount = report.totalCount;
  final num totalEarnings = report.totalEarnings;

  final List<DailyEarningsModel> dailyEarnings = report.dailyEarnings;

  final currentVendor = appContext.read<AuthVM>().currentVendor;

  final vendorImage = await networkImage(currentVendor?.logo?.url ?? '');

  englishTable() {
    return pw.TableHelper.fromTextArray(
      cellAlignment: pw.Alignment.center,
      headers: [appContext.tr.date, appContext.tr.earnings],
      headerStyle: pw.TextStyle(
        fontWeight: pw.FontWeight.bold,
        fontSize: 14,
        font: arabicFont,
      ),
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.grey300,
      ),
      data: dailyEarnings.map((e) {
        return [
          e.date.formatDateToString,
          toInvoiceCurrency(appContext, price: e.earnings),
        ];
      }).toList(),
      cellStyle: pw.TextStyle(
        fontSize: 12,
        font: arabicFont,
      ),
      cellHeight: 25,
      cellAlignments: {
        0: pw.Alignment.center,
        1: pw.Alignment.center,
      },
    );
  }

  arabicTable() {
    return pw.TableHelper.fromTextArray(
      cellAlignment: pw.Alignment.center,
      headers: [appContext.tr.earnings, appContext.tr.date],
      // Reverse headers for Arabic
      headerStyle: pw.TextStyle(
        fontWeight: pw.FontWeight.bold,
        fontSize: 14,
        font: arabicFont,
      ),

      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.grey300,
      ),
      data: dailyEarnings.map((e) {
        return [
          toInvoiceCurrency(appContext, price: e.earnings),
          e.date.formatDateToString,
        ];
      }).toList(),
      cellStyle: pw.TextStyle(
        fontSize: 12,
        font: arabicFont,
      ),
      cellHeight: 25,
      headerDirection: pw.TextDirection.rtl,
      tableDirection: pw.TextDirection.rtl,
      cellAlignments: {
        0: pw.Alignment.center,
        1: pw.Alignment.center,
      },
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return [
          pw.Header(
            level: 0,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                if (currentVendor?.logo?.url != null &&
                    (currentVendor?.logo?.url!.isNotEmpty ?? false))
                  pw.Center(
                    child: pw.Image(vendorImage,
                        height: 120, fit: pw.BoxFit.contain),
                  )
                else
                  pw.Text(
                    currentVendor?.name ?? '',
                    textAlign: pw.TextAlign.center,
                    style: pw.TextStyle(
                      fontSize: 30,
                      font: arabicFont,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                pw.SizedBox(height: 20),
                baseTableText(date,
                    arabicFont: arabicFont,
                    isBold: true,
                    fontSize: 20,
                    isLtr: isEnglish),
                pw.SizedBox(height: 10),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                  children: [
                    baseTableText(
                        '${appContext.tr.totalEarning}: ${toInvoiceCurrency(appContext, price: totalEarnings)}',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 20,
                        isLtr: isEnglish),
                    baseTableText('${appContext.tr.totalOrders}: $totalCount',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 20,
                        isLtr: isEnglish),
                  ],
                ),
              ],
            ),
            padding: const pw.EdgeInsets.only(bottom: 10),
            decoration: const pw.BoxDecoration(
              border: pw.Border(
                bottom: pw.BorderSide(
                  color: PdfColors.grey,
                  width: 1,
                ),
              ),
            ),
          ),
          pw.SizedBox(height: 20),
          // Conditionally render the table based on the language
          if (isEnglish) englishTable() else arabicTable(),
        ];
      },
    ),
  );

  return pdf;
}

// Future<pw.Document> generateReport(
//   BuildContext appContext, {
//   required bool isEnglish,
//   required ReportModel report,
//   required String date,
// }) async {
//   final pdf = pw.Document();
//
//   final arabicFont = Font.ttf(
//     await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
//   );
//
//   final int totalCount = report.totalCount;
//   final num totalEarnings = report.totalEarnings;
//
//   final List<DailyEarningsModel> dailyEarnings = report.dailyEarnings;
//
//   final currentVendor = appContext.read<AuthVM>().currentVendor;
//
//   final vendorImage = await networkImage(currentVendor?.logo?.url ?? '');
//
//   pdf.addPage(
//     pw.MultiPage(
//       pageFormat: PdfPageFormat.a4,
//       build: (pw.Context context) {
//         return [
//           pw.Header(
//             level: 0,
//             child: pw.Column(
//               crossAxisAlignment: pw.CrossAxisAlignment.center,
//               children: [
//                 if (currentVendor?.logo?.url != null &&
//                     (currentVendor?.logo?.url!.isNotEmpty ?? false))
//                   pw.Center(
//                     child: pw.Image(vendorImage,
//                         height: 120, fit: pw.BoxFit.contain),
//                   )
//                 else
//                   pw.Text(
//                     currentVendor?.name ?? '',
//                     textAlign: pw.TextAlign.center,
//                     style: pw.TextStyle(
//                       fontSize: 30,
//                       font: arabicFont,
//                       fontWeight: pw.FontWeight.bold,
//                     ),
//                   ),
//                 pw.SizedBox(height: 20),
//                 pw.Text(
//                   date,
//                   style: pw.TextStyle(
//                     fontSize: 20,
//                     font: arabicFont,
//                     fontWeight: pw.FontWeight.bold,
//                   ),
//                 ),
//                 pw.SizedBox(height: 10),
//                 pw.Row(
//                   mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
//                   children: [
//                     baseTableText(
//                       '${appContext.tr.totalOrders}: $totalCount',
//                       arabicFont: arabicFont,
//                       isBold: true,
//                       fontSize: 20,
//                     ),
//                     baseTableText(
//                       '${appContext.tr.totalEarning}: ${toInvoiceCurrency(appContext, price: totalEarnings)}',
//                       arabicFont: arabicFont,
//                       isBold: true,
//                       fontSize: 20,
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             padding: const pw.EdgeInsets.only(bottom: 10),
//             decoration: const pw.BoxDecoration(
//               border: pw.Border(
//                 bottom: pw.BorderSide(
//                   color: PdfColors.grey,
//                   width: 1,
//                 ),
//               ),
//             ),
//           ),
//           pw.SizedBox(height: 20),
//           // Table Header
//           pw.TableHelper.fromTextArray(
//             cellAlignment: pw.Alignment.center,
//             headers: [appContext.tr.date, appContext.tr.earnings],
//             headerStyle: pw.TextStyle(
//               fontWeight: pw.FontWeight.bold,
//               fontSize: 14,
//               font: arabicFont,
//             ),
//             headerDecoration: const pw.BoxDecoration(
//               color: PdfColors.grey300,
//             ),
//             data: dailyEarnings.map((e) {
//               return [
//                 e.date.formatDateToString,
//                 toInvoiceCurrency(appContext, price: e.earnings),
//               ];
//             }).toList(),
//             cellStyle: pw.TextStyle(
//               fontSize: 12,
//               font: arabicFont,
//             ),
//             cellHeight: 25,
//             cellAlignments: {
//               0: pw.Alignment.center,
//               1: pw.Alignment.center,
//             },
//           ),
//         ];
//       },
//     ),
//   );
//
//   return pdf;
// }
