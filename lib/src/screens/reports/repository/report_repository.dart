import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/core/services/printing_service.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/reports/model/report_model.dart';
import 'package:idea2app_vendor_app/src/screens/reports/repository/generate_print_report.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:provider/provider.dart';

class ReportRepository {
  final BaseApiServices networkApiServices;

  ReportRepository({required this.networkApiServices});

  Future<ReportModel> getReports(
      {required DateTime startDate, required DateTime? endDate}) async {
    final filter =
        '?filters[createdAt][\$gte]=${startDate.toIso8601String()}&filters[createdAt][\$lte]=${endDate!.toIso8601String()}';

    try {
      final response = await networkApiServices
          .getResponse('${ApiEndPoints.reports}$filter');

      Log.i('Report Data: $response');

      final reportData = await compute(responseFromReportModel, response);

      return reportData;
    } catch (error, s) {
      Log.e(
        'Error in getReports $error, \n $s',
      );
      rethrow;
    }
  }

  //! Print Report ================================
  Future<String> printReport(
    BuildContext context, {
    required ReportModel report,
    required String date,
  }) async {
    try {
      final isEng = context.read<AppSettingsVM>().locale.languageCode == 'en';

      var generatedPDF = await generateReport(context,
          isEnglish: isEng, date: date, report: report);

      final pdfTitle = isEng ? '$date Report' : 'تقرير $date';

      final generatedPDFReportPath =
          await PrintingService.saveAndOpenPdf(generatedPDF, title: pdfTitle);

      return generatedPDFReportPath;
    } catch (e) {
      rethrow;
    }
  }
}
