import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/alert_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/order_details_screen/edit_order_screen.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_spaces.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/row_widgets/title_and_sub_title_row.dart';

class OrderBottomSection extends StatelessWidget {
  final OrderModel order;
  final ValueNotifier<OrderStatus> orderStatus;
  final ValueNotifier<num?> deliveryCost;

  const OrderBottomSection(
      {super.key,
      required this.order,
      required this.orderStatus,
      required this.deliveryCost});

  @override
  Widget build(BuildContext context) {
    final haveDiscount = order.discount != null && order.discount! > 0;
    final ignoring = order.status == OrderStatus.canceled ||
        order.status == OrderStatus.refunded;

    final total = (order.total! - order.deliveryCost!) + deliveryCost.value!;

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      height: haveDiscount ? context.height * 0.29 : context.height * 0.24,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20), topLeft: Radius.circular(20)),
        boxShadow: context.isDark
            ? ConstantsWidgets.darkBoxShadowFromBottom
            : ConstantsWidgets.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          IconAndTitleAndSubTitleRow(
            titleAndSubTitle: (
              context.tr.subtotal,
              order.products!.fold(0, (previousValue, element) {
                Log.w(
                    ' ========== ${order.products?.map((e) => e.product?.actualPrice).toList()}');
                return previousValue +=
                    element.actualPrice.toInt() * element.quantity!;
              }).toCurrency(context)
            ),
          ),

          context.smallGap,

          //! Delivery fee
          IconAndTitleAndSubTitleRow(
            titleAndSubTitle: (
              context.tr.delivery_fee,
              deliveryCost.value.toCurrency(context)
            ),
          ),

          if (order.discount != null && order.discount! > 0) ...[
            context.smallGap,
            //! Promo code
            IconAndTitleAndSubTitleRow(
              titleAndSubTitle: (
                '${context.tr.promoCode}: ${order.promoCode?.promo ?? ''} ${order.promoCode?.discount}${order.promoCode!.isPercent ? '%' : 'L.E'}',
                order.discount.toCurrency(context)
              ),
            ),
          ],

          context.smallGap,

          //! Total
          IconAndTitleAndSubTitleRow(
            isTotal: true,
            titleAndSubTitle: (context.tr.total, total.toCurrency(context)),
          ),

          if (!ignoring) ...[
            context.mediumGap,
            //! Order Actions (Edit, Cancel)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  flex: 2,
                  child: orderBottomSectionButtonWidget(context,
                      isWhiteText: true, text: context.tr.edit, onPressed: () {
                    context.to(EditOrderScreen(
                      order: order,
                      orderStatus: orderStatus,
                      deliveryCost: deliveryCost,
                    ));
                  }),
                ),
                context.mediumGap,
                Expanded(
                  child: orderBottomSectionButtonWidget(
                    context,
                    text: context.tr.cancel,
                    color: ColorManager.textFieldColor(context),
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (context) {
                            final isLoading =
                                context.watch<OrderVM>().isLoading;

                            return AlertDialogWidget(
                              isLoading: isLoading,
                              isWarningMessage: true,
                              child: Text(
                                  context
                                      .tr.areYouSureYouWantToCancelThisOrderOf,
                                  maxLines: 1,
                                  style: context.labelLarge),
                              onConfirm: () {
                                final copiedOrder = order.copyWith(
                                    status: OrderStatus.canceled);

                                context
                                    .read<OrderVM>()
                                    .cancelOrder(context, order: copiedOrder);
                              },
                            );
                          });
                    },
                  ),
                ),
              ],
            ),
            context.mediumGap
          ],
        ],
      ),
    );
  }

  Widget orderBottomSectionButtonWidget(
    BuildContext context, {
    required VoidCallback onPressed,
    required String text,
    Color color = ColorManager.primaryColor,
    bool isWhiteText = false,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        height: 35.h,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: context.labelMedium.copyWith(
                color: isWhiteText ? ColorManager.white : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
