import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:share_plus/share_plus.dart' show Share;

import '../../../../../../core/resources/theme/theme.dart';
import '../../../../../settings/view/contact_us.dart';

class UserAddressWidget extends StatelessWidget {
  final OrderModel order;

  const UserAddressWidget({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final isLatLongNotNull = order.lat != null && order.lng != null;
    final String googleMapsUrl =
        'https://www.google.com/maps/search/?api=1&query=${order.lat},${order.lng}';

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.tr.address,
                    overflow: TextOverflow.ellipsis,
                    style: context.greyLabelLarge,
                  ),
                  if (isLatLongNotNull)
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            openURL(googleMapsUrl);
                          },
                          icon: Image.asset(
                            Assets.assetsIconsMap,
                            height: 37,
                            fit: BoxFit.cover,
                          ),
                          color: ColorManager.white,
                        ),
                        CircleAvatar(
                            backgroundColor: ColorManager.primaryColor,
                            child: IconButton(
                              onPressed: () {
                                Share.share(googleMapsUrl);
                              },
                              icon: const Icon(
                                Icons.share_rounded,
                                size: 24,
                              ),
                              color: ColorManager.white,
                            )),
                      ],
                    ),
                ],
              ),
              context.smallGap,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //! State
                  Row(
                    children: [
                      Expanded(
                        child: _AddressTitleAndSubtitle(
                          titleAndSubTitle: (
                            context.tr.city,
                            order.addressModel?.city
                          ),
                        ),
                      ),
                      if (order.addressModel?.area != '-')
                        //! City
                        Expanded(
                          child: _AddressTitleAndSubtitle(
                            titleAndSubTitle: (
                              context.tr.area,
                              order.addressModel?.area
                            ),
                          ),
                        ),
                    ],
                  ),

                  context.smallGap,

                  //! Street Name
                  _AddressTitleAndSubtitle(
                    titleAndSubTitle: (
                      context.tr.streetName,
                      order.addressModel?.streetName
                    ),
                  ),

                  context.smallGap,

                  //! Building
                  Row(
                    children: [
                      Expanded(
                        child: _AddressTitleAndSubtitle(
                          titleAndSubTitle: (
                            context.tr.building,
                            order.addressModel?.building
                          ),
                        ),
                      ),

                      context.xSmallGap,

                      //! Floor
                      Expanded(
                        child: _AddressTitleAndSubtitle(
                          titleAndSubTitle: (
                            context.tr.floor,
                            order.addressModel?.floor
                          ),
                        ),
                      ),

                      context.xSmallGap,

                      //! Apartment
                      Expanded(
                        child: _AddressTitleAndSubtitle(
                          titleAndSubTitle: (
                            context.tr.apartment,
                            order.addressModel?.apartment
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _AddressTitleAndSubtitle extends StatelessWidget {
  final (String, String?) titleAndSubTitle;

  const _AddressTitleAndSubtitle({required this.titleAndSubTitle});

  @override
  Widget build(BuildContext context) {
    final String title = titleAndSubTitle.$1;
    final String subTitle = titleAndSubTitle.$2 ?? '';

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$title: ',
          overflow: TextOverflow.ellipsis,
          style: context.labelLarge.copyWith(fontWeight: FontWeight.w400),
        ),
        Expanded(
          child: Text(
            subTitle,
            // overflow: TextOverflow.ellipsis,
            style: context.labelLarge,
          ),
        ),
      ],
    );
  }
}
