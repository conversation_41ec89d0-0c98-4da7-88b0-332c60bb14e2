import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/filter/search_filter.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/pagination_widget/base_pagination_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/widgets/orders_tab_bar.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/home_tab_bar_provider.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../auth/view_model/auth_view_model.dart';
import '../../orders/view/orders_list_widget.dart';

bool isInitialized = false;

class OrdersScreen extends HookWidget {
  const OrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final orderVM = context.read<OrderVM>();
    final authVM = context.watch<AuthVM>();

    final scrollController = useScrollController();

    useEffect(() {
      if (!isInitialized) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          orderVM.clearFilter();
          orderVM.getOrders(context);
        });
        isInitialized = true;
      }

      return () {};
    }, []);

    final watchOrderVM = context.watch<OrderVM>();

    final isInitialLoading =
        (watchOrderVM.isLoading || watchOrderVM.orders.isEmpty) &&
            watchOrderVM.orderFilter.limit == 10;

    return BasePaginationWidget(
      isLoading: watchOrderVM.isLoading,
      onScrollEnd: () {
        final filter = watchOrderVM.orderFilter.copyWith(
          limit: orderVM.orderFilter.limit + 10,
        );

        orderVM.setOrderFilter(context, filter: filter);
      },
      onRefresh: () {
        context.read<HomeTabBarVM>().setCurrentIndex(0);
        orderVM.clearFilter();
        orderVM.getOrders(
          context,
        );
        // vendorsVM.getVendorOrdersCount(context);

        return Future.value();
      },
      child: Stack(
        children: [
          CustomScrollView(
            controller: scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    context.mediumGap,
                    const OrdersFilterWidget(),
                    if (orderVM.orderFilter.isFilterApplied)
                      context.smallGap
                    else
                      context.mediumGap,
                    const OrdersTabBar(),
                    context.smallGap,
                  ],
                ),
              ),

              // * Order List
              OrderListWidget(
                scrollController: scrollController,
              ),
            ],
          ),
          if (watchOrderVM.isLoading && !isInitialLoading)
            const Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: LoadingWidget(
                isLinear: true,
              ),
            ),
        ],
      ),
    );
  }
}
