import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_plans/view/vendor_choose_plan_screen.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

import '../../../../../core/shared_widgets/dialogs/alert_dialog.dart';
import '../../../view_model/order_view_model.dart';
import '../../order_details_screen/order_details_screen.dart';

class OrderCard extends HookWidget {
  final OrderModel order;

  const OrderCard({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final ignoring = order.status == OrderStatus.canceled ||
        order.status == OrderStatus.refunded;

    final currentVendor = context.read<AuthVM>().currentVendor;

    return Opacity(
      opacity: ignoring ? .5 : 1,
      child: InkWell(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        radius: AppRadius.baseContainerRadius,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        onTap: () {
          if (currentVendor?.isActive == false) {
            QuickAlert.show(
                type: QuickAlertType.error,
                title: context.tr.yourAccountIsNotActive,
                text: context
                    .tr.yourAccountIsNotActivePleaseRenewYourSubscription,
                context: context,
                confirmBtnText: context.tr.renewNow,
                confirmBtnColor: ColorManager.successColor,
                onConfirmBtnTap: () =>
                    context.to(const VendorChoosePlanScreen()));
            return;
          }

          context.to(MainOrderDetailsScreen(order: order));
        },
        child: Container(
          padding: EdgeInsets.symmetric(
              vertical: AppSpaces.mediumPadding,
              horizontal: AppSpaces.mediumPadding),
          decoration: BoxDecoration(
              color: context.isDark
                  ? ColorManager.textFieldColor(context)
                  : ColorManager.textFieldColor(context),
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text('#${order.orderId.toString()}',
                      style: context.labelMedium
                          .copyWith(fontWeight: FontWeight.w300)),
                  Text(order.createdAt.formatDateToStringWithTime,
                      style: context.labelMedium
                          .copyWith(fontWeight: FontWeight.w300)),
                ],
              ),

              Divider(
                color:
                    context.isDark ? ColorManager.grey.withOpacity(.5) : null,
              ),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      context.tr.delivery_fee,
                      style: context.labelMedium,
                    ),
                  ),
                  Text(order.deliveryCost.toCurrency(context),
                      style: context.labelMedium)
                ],
              ),

              if (order.discount != null && order.discount! > 0) ...[
                context.smallGap,
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        context.tr.discount,
                        style: context.labelMedium,
                      ),
                    ),
                    Text(order.discount.toCurrency(context),
                        style: context.labelMedium)
                  ],
                ),
              ],
              context.smallGap,

              //! Total
              Row(
                children: [
                  Expanded(
                    child: Text(
                      context.tr.total,
                      style: context.labelMedium,
                    ),
                  ),
                  Text(
                    order.total.toCurrency(context),
                    style: context.labelMedium,
                  )
                  // Helper.getPrice(Helper.getTotalOrdersPrice(widget.order), context, style: Theme.of(context).textTheme.headline4)
                ],
              ),

              context.mediumGap,

              //! Order status container
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _CancelButtons(order: order),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.smallPadding,
                        vertical: AppSpaces.xSmallPadding),
                    width: 100,
                    decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(AppRadius.smallRadius),
                        color: order.status!.displayColor),
                    alignment: AlignmentDirectional.center,
                    child: Text(
                      order.status?.getTranslation(context,
                              isConfirmedText: true) ??
                          '',
                      maxLines: 1,
                      overflow: TextOverflow.fade,
                      softWrap: false,
                      style: context.whiteLabelMedium
                          .copyWith(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _CancelButtons extends StatelessWidget {
  final OrderModel order;

  const _CancelButtons({required this.order});

  @override
  Widget build(BuildContext context) {
    if (order.status == OrderStatus.canceled) {
      return SizedBox();
    }
    return Row(
      children: [
        //! Cancel Button
        GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                final isLoading = context.watch<OrderVM>().isLoading;

                return AlertDialogWidget(
                  isLoading: isLoading,
                  isWarningMessage: true,
                  child: Text(context.tr.areYouSureYouWantToCancelThisOrderOf,
                      style: context.labelLarge),
                  onConfirm: () {
                    final copiedOrder =
                        order.copyWith(status: OrderStatus.canceled);
                    final orderVM = context.read<OrderVM>();

                    orderVM.cancelOrder(context, order: copiedOrder);

                    orderVM.getHomeOrders(context);
                  },
                );
              },
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: AppSpaces.xSmallPadding,
              horizontal: AppSpaces.mediumPadding,
            ),
            decoration: BoxDecoration(
                color: context.isDark
                    ? ColorManager.lightGrey
                    : ColorManager.black.withOpacity(.1),
                borderRadius: BorderRadius.circular(AppRadius.smallRadius)),
            child: Text(
              "${context.tr.cancel} ",
              style: context.labelMedium.copyWith(fontWeight: FontWeight.w300),
            ),
          ),
        ),
      ],
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
// import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
// import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
// import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
// import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/alert_dialog.dart';
// import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
// import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
// import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
// import 'package:idea2app_vendor_app/src/screens/orders/view/order_details_screen/order_details_screen.dart';
// import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
// import 'package:idea2app_vendor_app/src/screens/subscription_plans/view/vendor_choose_plan_screen.dart';
// import 'package:provider/provider.dart';
// import 'package:quickalert/models/quickalert_type.dart';
// import 'package:quickalert/widgets/quickalert_dialog.dart';
//
// import '../../../../../core/shared_widgets/shared_widgets.dart';
//
// class OrderCard extends HookWidget {
//   final OrderModel order;
//
//   const OrderCard({super.key, required this.order});
//
//   @override
//   Widget build(BuildContext context) {
//     final ignoring = order.status == OrderStatus.canceled ||
//         order.status == OrderStatus.refunded;
//
//     final theme = Theme.of(context).copyWith(dividerColor: Colors.transparent);
//
//     return Stack(
//       children: [
//         IgnorePointer(
//           ignoring: ignoring ? true : false,
//           child: Opacity(
//             opacity: ignoring ? 0.4 : 1,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.end,
//               children: [
//                 Container(
//                   margin: const EdgeInsets.only(top: 12),
//                   padding: const EdgeInsets.only(
//                       top: AppSpaces.largePadding - 4,
//                       bottom: AppSpaces.smallPadding),
//                   decoration: BoxDecoration(
//                     borderRadius:
//                         BorderRadius.circular(AppRadius.baseContainerRadius),
//                     color: context.appTheme.cardColor,
//                     boxShadow: context.isDark
//                         ? ConstantsWidgets.darkBoxShadowFromBottom
//                         : ConstantsWidgets.boxShadowFromBottom,
//                   ),
//                   child: Theme(
//                     data: theme,
//                     child: ExpansionTile(
//                       controlAffinity: ListTileControlAffinity.leading,
//                       collapsedIconColor: order.status!.displayColor,
//                       iconColor: order.status!.displayColor,
//                       initiallyExpanded: false,
//                       title: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Text(
//                             '${context.tr.order_id}: #${order.orderId}',
//                             style: context.labelLarge,
//                           ),
//                           Text(
//                             order.createdAt!.formatDateToStringWithTime,
//                             style: context.labelLarge,
//                           ),
//                         ],
//                       ),
//                       trailing: Column(
//                         crossAxisAlignment: CrossAxisAlignment.end,
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Text(
//                             order.total.toCurrency(context),
//                             style: context.labelMedium,
//                           ),
//                           context.xSmallGap,
//                           Text(
//                             order.payment?.title ?? '',
//                             style: context.labelMedium,
//                           ),
//                         ],
//                       ),
//                       children: [
//                         //! Delivery fee & Tax & Total
//                         Column(
//                           children: [
//                             Row(
//                               children: [
//                                 Expanded(
//                                   child: Text(
//                                     context.tr.delivery_fee,
//                                     style: context.labelMedium,
//                                   ),
//                                 ),
//                                 Text(order.deliveryCost.toCurrency(context),
//                                     style: context.labelMedium)
//                               ],
//                             ),
//
//                             if (order.discount != null &&
//                                 order.discount! > 0) ...[
//                               context.smallGap,
//                               Row(
//                                 children: [
//                                   Expanded(
//                                     child: Text(
//                                       context.tr.discount,
//                                       style: context.labelMedium,
//                                     ),
//                                   ),
//                                   Text(order.discount.toCurrency(context),
//                                       style: context.labelMedium)
//                                 ],
//                               ),
//                             ],
//                             context.smallGap,
//
//                             //! Total
//                             Row(
//                               children: [
//                                 Expanded(
//                                   child: Text(
//                                     context.tr.total,
//                                     style: context.labelMedium,
//                                   ),
//                                 ),
//                                 Text(
//                                   order.total.toCurrency(context),
//                                   style: context.labelMedium,
//                                 )
//                                 // Helper.getPrice(Helper.getTotalOrdersPrice(widget.order), context, style: Theme.of(context).textTheme.headline4)
//                               ],
//                             ),
//                           ],
//                         ).paddingSymmetric(horizontal: 20, vertical: 5)
//                       ],
//                     ),
//                   ),
//                 ),
//                 _ViewAndCancelButtons(order: order),
//               ],
//             ),
//           ),
//         ),
//
//         //! Order status container
//         Container(
//           margin: const EdgeInsetsDirectional.only(start: 20),
//           padding: const EdgeInsets.symmetric(horizontal: 10),
//           height: 28,
//           width: 140,
//           decoration: BoxDecoration(
//               borderRadius: const BorderRadius.all(Radius.circular(100)),
//               color: order.status!.displayColor),
//           alignment: AlignmentDirectional.center,
//           child: Text(
//             order.status?.getTranslation(context, isConfirmedText: true) ?? '',
//             maxLines: 1,
//             overflow: TextOverflow.fade,
//             softWrap: false,
//             style:
//                 context.whiteLabelMedium.copyWith(fontWeight: FontWeight.bold),
//           ),
//         ),
//       ],
//     );
//   }
// }
//
// class _ViewAndCancelButtons extends StatelessWidget {
//   final OrderModel order;
//
//   const _ViewAndCancelButtons({super.key, required this.order});
//
//   @override
//   Widget build(BuildContext context) {
//     final currentVendor = context.read<AuthVM>().currentVendor;
//
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: [
//         //! View Button
//         MaterialButton(
//           elevation: 0,
//           shape: const StadiumBorder(),
//           onPressed: () {
//             if (currentVendor?.isActive == false) {
//               QuickAlert.show(
//                   type: QuickAlertType.error,
//                   title: context.tr.yourAccountIsNotActive,
//                   text: context
//                       .tr.yourAccountIsNotActivePleaseRenewYourSubscription,
//                   context: context,
//                   confirmBtnText: context.tr.renewNow,
//                   confirmBtnColor: ColorManager.successColor,
//                   onConfirmBtnTap: () =>
//                       context.to(const VendorChoosePlanScreen()));
//               return;
//             }
//
//             context.to(MainOrderDetailsScreen(order: order));
//           },
//           textColor: Theme.of(context).hintColor,
//           padding: EdgeInsets.zero,
//           child: Text(
//             context.tr.view,
//             style: context.labelMedium,
//           ),
//         ),
//         //! Cancel Button
//         MaterialButton(
//           elevation: 0,
//           shape: const StadiumBorder(),
//           padding: EdgeInsets.zero,
//           onPressed: () {
//             showDialog(
//               context: context,
//               builder: (BuildContext context) {
//                 final isLoading = context.watch<OrderVM>().isLoading;
//
//                 return AlertDialogWidget(
//                   isLoading: isLoading,
//                   isWarningMessage: true,
//                   child: Text(context.tr.areYouSureYouWantToCancelThisOrderOf,
//                       style: context.labelLarge),
//                   onConfirm: () {
//                     final copiedOrder =
//                         order.copyWith(status: OrderStatus.canceled);
//
//                     context
//                         .read<OrderVM>()
//                         .cancelOrder(context, order: copiedOrder);
//                   },
//                 );
//               },
//             );
//           },
//           child: Text(
//             "${context.tr.cancel} ",
//             style: context.labelMedium.copyWith(color: ColorManager.errorColor),
//           ),
//         ),
//       ],
//     );
//   }
// }
