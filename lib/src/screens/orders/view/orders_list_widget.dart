import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shimmer_loading/home_loading/orders_loading.dart';
import 'package:provider/provider.dart';

import '../view_model/order_view_model.dart';
import 'widgets/order_card/order_card.dart';

class OrderListWidget extends StatelessWidget {
  final ScrollController scrollController;

  const OrderListWidget({super.key, required this.scrollController});

  @override
  Widget build(BuildContext context) {
    return Consumer<OrderVM>(
      builder: (context, orderVM, child) {
        final orders = orderVM.orders;

        final isInitialLoading = (orderVM.isLoading || orders.isEmpty) &&
            orderVM.orderFilter.limit == 10;

        if (isInitialLoading) {
          return SliverFillRemaining(
            hasScrollBody: false,
            child: SizedBox(
              height: 200,
              child: WidgetAnimator(
                delay: const Duration(milliseconds: AppConsts.animatedDuration),
                child: EmptyDataWidget(
                  message: context.tr.doNotHaveAnyOrder,
                  isLoading: orderVM.isLoading,
                  height: 150,
                  loadingWidget: const OrdersLoading(),
                ),
              ),
            ),
          );
        }

        return SliverPadding(
          padding: const EdgeInsets.only(
            top: AppSpaces.smallPadding,
            right: AppSpaces.mediumPadding,
            left: AppSpaces.mediumPadding,
            bottom: AppSpaces.mediumPadding,
          ),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                var order = orders[index];

                return WidgetAnimator(
                  delay: Duration(
                    milliseconds: AppConsts.orderAnimatedDuration * index,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: AppSpaces.mediumPadding),
                    child: OrderCard(
                      order: order,
                    ),
                  ),
                );
              },
              childCount: orders.length,
            ),
          ),
        );
      },
    );
  }
}

// class OrderListWidget extends StatelessWidget {
//   const OrderListWidget({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<OrderVM>(
//       builder: (context, orderVM, child) {
//         final orders = orderVM.orders;
//
//         final isInitialLoading = (orderVM.isLoading || orders.isEmpty) &&
//             orderVM.orderFilter.limit == 10;
//
//         if (isInitialLoading) {
//           return EntranceFader(
//             delay: const Duration(milliseconds: AppConsts.animatedDuration),
//             child: EmptyDataWidget(
//               message: context.tr.doNotHaveAnyOrder,
//               isLoading: orderVM.isLoading,
//               loadingWidget: const OrdersLoading(
//                 collapsed: true,
//               ),
//             ),
//           );
//         }
//
//         return BasePaginationWidget(
//           isLoading: orderVM.isLoading,
//           onScrollEnd: () {
//             final filter = orderVM.orderFilter.copyWith(
//               limit: orderVM.orderFilter.limit + 10,
//             );
//
//             orderVM.setOrderFilter(context, filter: filter);
//           },
//           child: RefreshIndicatorWidget(
//             onRefresh: () {
//               context.read<HomeTabBarVM>().setCurrentIndex(0);
//               orderVM.clearFilter();
//               orderVM.getOrders(
//                 context,
//               );
//
//               return Future.value();
//             },
//             child: Stack(
//               children: [
//                 ListView.separated(
//                   physics: const AlwaysScrollableScrollPhysics(),
//                   padding: const EdgeInsets.only(
//                       top: AppSpaces.smallPadding,
//                       bottom: AppSpaces.xlLargePadding),
//                   itemCount: orders.length,
//                   itemBuilder: (context, index) {
//                     var order = orders[index];
//
//                     if (isDrHoopa()) {
//                       return DRHoopaOrderCard(order: order, index: index);
//                     }
//
//                     return EntranceFader(
//                       delay: Duration(
//                           milliseconds:
//                               AppConsts.orderAnimatedDuration * index),
//                       child: OrderCard(
//                         order: order,
//                       ),
//                     );
//                   },
//                   separatorBuilder: (context, index) {
//                     return context.xSmallGap;
//                   },
//                 ),
//                 if (orderVM.isLoading)
//                   const Positioned(
//                     bottom: 0,
//                     left: 0,
//                     right: 0,
//                     child: LoadingWidget(
//                       isLinear: true,
//                     ),
//                   ),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
