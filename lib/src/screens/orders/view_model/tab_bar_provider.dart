import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

class OrderTabBarVM extends ChangeNotifier {
  List<String> tabBarList(BuildContext context) => [
        context.tr.products,
        context.tr.customer,
      ];

  int _currentIndex = 0;

  int get currentIndex => _currentIndex;

  void setCurrentIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }

  void resetIndex() {
    _currentIndex = 0;
    notifyListeners();
  }
}
