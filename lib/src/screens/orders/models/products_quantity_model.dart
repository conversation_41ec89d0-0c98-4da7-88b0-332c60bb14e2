import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';

import '../../../core/consts/api_strings.dart';

class ProductQuantityModel {
  final num? totalPrice;
  final int? quantity;
  final ProductModel? product;
  final String size;
  final String color;
  final String? englishTitle;
  final String? arabicTitle;
  final num? price;

  ProductQuantityModel({
    required this.quantity,
    this.product,
    this.totalPrice = 0,
    this.price,
    this.englishTitle,
    this.arabicTitle,
    this.size = '',
    this.color = '',
  });

  String get id => '${product?.documentId}$size$color';

  String nameByLang(BuildContext context) {
    final isEnglish = context.readIsEng;

    return isEnglish
        ? (englishTitle?.isNotEmpty == true ? englishTitle! : arabicTitle ?? '')
        : (arabicTitle?.isNotEmpty == true ? arabicTitle! : englishTitle ?? '');
  }

  num get actualPrice => price ?? product?.actualPrice ?? 0;

  num get totalQuantityPrice =>
      (price ?? product?.actualPrice ?? 0) * (quantity ?? 0);

  factory ProductQuantityModel.fromJson(Map<String, dynamic> json) {
    final product = json[ApiStrings.product] != null
        ? ProductModel.fromJson(json[ApiStrings.product])
        : null;

    return ProductQuantityModel(
      product: product,
      englishTitle: json[ApiStrings.title] ??
          product?.englishTitle ??
          product?.arabicTitle ??
          '',
      arabicTitle: json[ApiStrings.arabicTitle] ??
          product?.arabicTitle ??
          product?.englishTitle ??
          '',
      quantity: json[ApiStrings.quantity],
      totalPrice: json[ApiStrings.totalPrice] ?? 0,
      price: json[ApiStrings.price],
      size: json[ApiStrings.size] ?? '',
      color: json[ApiStrings.color] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.quantity: quantity,
      ApiStrings.product: product?.id,
      ApiStrings.color: color,
      ApiStrings.size: size,
      ApiStrings.price: price,
    };
  }

  Map<String, dynamic> toLocalJson() {
    return {
      ApiStrings.quantity: quantity,
      ApiStrings.product: product?.toJson(fromLocal: true),
      ApiStrings.color: color,
      ApiStrings.size: size,
      ApiStrings.price: price,
      ApiStrings.totalPrice: totalPrice,
    };
  }

  ProductQuantityModel copyWith({
    int? quantity,
    ProductModel? product,
    String? size,
    String? color,
    num? totalPrice,
    num? price,
  }) {
    return ProductQuantityModel(
      quantity: quantity ?? this.quantity,
      product: product ?? this.product,
      size: size ?? this.size,
      color: color ?? this.color,
      totalPrice: totalPrice ?? this.totalPrice,
      price: price ?? this.price,
    );
  }
}
