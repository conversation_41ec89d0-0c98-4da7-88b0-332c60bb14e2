import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';

class FilterModel {
  final OrdersSearchBy? searchBy;
  final String? searchValue;
  final DateTime? startDate;
  final DateTime? endDate;
  final OrderStatus? orderStatus;
  final bool? storeInvoices;

  final int limit;

  FilterModel({
    this.searchBy = OrdersSearchBy.orderId,
    this.searchValue,
    this.startDate,
    this.endDate,
    this.orderStatus,
    this.limit = 10,
    this.storeInvoices = false,
  });

  String get filter {
    var searchFilter = '';

    if (searchValue != null && searchBy != null) {
      if (searchBy == OrdersSearchBy.invoiceId) {
        searchFilter = '?filters[invoice_id][\$eq]=$searchValue';
      } else if (searchBy == OrdersSearchBy.orderId) {
        searchFilter = '?filters[order_id][\$eq]=$searchValue';
      } else if (searchBy == OrdersSearchBy.userName) {
        searchFilter = '?filters[guest_name][\$contains]=$searchValue';
      } else if (searchBy == OrdersSearchBy.userPhone) {
        searchFilter = '?filters[phone_number][\$contains]=$searchValue';
      }
    }

    if (orderStatus != null) {
      final sign = searchFilter.isEmpty ? '?' : '&';
      searchFilter =
          '$searchFilter${sign}filters[order_status][\$eq]=${orderStatus!.name}';
    }

    if (startDate != null) {
      final sign = searchFilter.isEmpty ? '?' : '&';
      searchFilter =
          '$searchFilter${sign}filters[createdAt][\$gte]=${startDate!.toIso8601String()}';
    }

    if (endDate != null) {
      final sign = searchFilter.isEmpty ? '?' : '&';
      searchFilter =
          '$searchFilter${sign}filters[createdAt][\$lte]=${endDate!.toIso8601String()}';
    }

    final sign = searchFilter.isEmpty ? '?' : '&';
    searchFilter = '$searchFilter${sign}pagination[limit]=$limit';

    if (storeInvoices != null) {
      final sign = searchFilter.isEmpty ? '?' : '&';
      searchFilter =
          '$searchFilter${sign}filters[from_store][\$eq]=$storeInvoices';
    }

    return searchFilter;
  }

  bool get isFilterApplied {
    return searchValue != null || startDate != null || endDate != null;
  }

  FilterModel copyWith({
    OrdersSearchBy? searchBy,
    String? searchValue,
    DateTime? startDate,
    DateTime? endDate,
    OrderStatus? orderStatus,
    int? limit,
    int? start,
    bool isAll = false,
    bool? storeInvoices,
    bool allOrders = false,
  }) {
    return FilterModel(
      searchBy: searchBy ?? this.searchBy,
      searchValue: searchValue ?? this.searchValue,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      orderStatus: isAll ? null : orderStatus ?? this.orderStatus,
      limit: limit ?? this.limit,
      storeInvoices: allOrders ? null : storeInvoices ?? this.storeInvoices,
    );
  }
}
