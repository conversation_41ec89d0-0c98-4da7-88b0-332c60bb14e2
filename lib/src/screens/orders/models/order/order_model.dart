import 'package:idea2app_vendor_app/src/core/shared_models/base_media_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/user_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/address_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/promo_code_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/payment/model/payment_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/shipping_model.dart';

import '../../../../core/consts/api_strings.dart';

class OrderModel {
  final int? id;
  final int? orderId;
  final String? documentId;
  final DateTime? createdAt;
  final num? total;
  final num? discount;
  final OrderStatus status;
  final UserModel? user;
  final ShippingModel? shipping;
  final String? guestPhoneNumber;
  final String? guestName;
  final num? deliveryCost;
  final PaymentModel? payment;
  final List<ProductQuantityModel>? products;
  final AddressModel? addressModel;
  final String? note;
  final bool? isFromStore;
  final int? invoiceId;
  final BaseMediaModel? paymentAttachment;
  final PromoCodeModel? promoCode;
  final String? lat;
  final String? lng;

  const OrderModel(
      {this.documentId,
      this.id,
      this.createdAt,
      this.orderId,
      this.discount,
      this.addressModel,
      this.total,
      this.note,
      this.status = OrderStatus.pending,
      this.paymentAttachment,
      this.invoiceId,
      this.guestPhoneNumber,
      this.guestName,
      this.user,
      this.isFromStore,
      this.shipping,
      this.payment,
      this.deliveryCost,
      this.promoCode,
      this.lat,
      this.lng,
      this.products});

  // is guest number same as user phone number
  bool get isGuestPhoneExistAndIsNotSameAsUserPhone =>
      guestPhoneNumber != null && user?.phone != guestPhoneNumber;

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    final product = json[ApiStrings.productsQuantity] ?? [];

    final productList = List<ProductQuantityModel>.from(
      product.map(
        (product) => ProductQuantityModel.fromJson(product),
      ),
    );

    final payment = json[ApiStrings.payment] != null
        ? PaymentModel.fromJson(json[ApiStrings.payment])
        : null;
    final paymentAttachment = json[ApiStrings.paymentAttachment] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.paymentAttachment])
        : null;

    final address = json[ApiStrings.address] != null
        ? AddressModel.fromJson(json[ApiStrings.address])
        : null;

    final shipping = json[ApiStrings.shipping] != null
        ? ShippingModel.fromJson(json[ApiStrings.shipping])
        : null;

    final promoCodeModel = json[ApiStrings.promoCode] != null
        ? PromoCodeModel.fromJson(json[ApiStrings.promoCode])
        : null;

    final deliveryCostFee =
        json[ApiStrings.deliveryCost] ?? shipping?.cost ?? 0;

    final user = json[ApiStrings.user] != null
        ? UserModel.fromJson(json[ApiStrings.user])
        : UserModel(
            phone: json[ApiStrings.guestPhoneNumber],
            displayName: json[ApiStrings.guestName] ?? 'Guest',
          );

    final status =
        parseOrderStatus(json[ApiStrings.status] ?? ApiStrings.pending);

    final total = json[ApiStrings.total] + deliveryCostFee;

    return OrderModel(
      id: json[ApiStrings.id],
      orderId: json[ApiStrings.orderId],
      documentId: json[ApiStrings.documentId],
      invoiceId: json[ApiStrings.invoiceId],
      lat: json[ApiStrings.location] != null &&
              json[ApiStrings.location][ApiStrings.lat] != null
          ? json[ApiStrings.location][ApiStrings.lat]
          : null,
      lng: json[ApiStrings.location] != null &&
              json[ApiStrings.location][ApiStrings.lng] != null
          ? json[ApiStrings.location][ApiStrings.lng]
          : null,
      total: total ?? 0,
      note: json[ApiStrings.note],
      isFromStore: json[ApiStrings.fromStore] ?? false,
      createdAt: DateTime.tryParse(json[ApiStrings.createdAt])?.toLocal(),
      guestPhoneNumber: json[ApiStrings.guestPhoneNumber],
      payment: payment,
      guestName: json[ApiStrings.guestName] ?? '',
      shipping: shipping,
      discount: json[ApiStrings.discount],
      promoCode: promoCodeModel,
      deliveryCost: deliveryCostFee,
      user: user,
      status: status,
      paymentAttachment: paymentAttachment,
      products: productList,
      addressModel: address,
    );
  }

  //! To Json
  Map<String, dynamic> toJson() => {
        if (documentId != null) ApiStrings.documentId: documentId,
        if (status != null)
          ApiStrings.status: OrderStatus.values
              .firstWhere((element) => element == status)
              .name,
        if (deliveryCost != null) ApiStrings.deliveryCost: deliveryCost
      };

  //! To Cart Json
  Map<String, dynamic> toCartJson() => {
        if (documentId != null) ApiStrings.id: documentId,
        ApiStrings.total: total,
        ApiStrings.productsQuantity: products?.map((e) => e.toJson()).toList(),
        ApiStrings.status: 'done',
        ApiStrings.fromStore: true,
        ApiStrings.deliveryCost: 0.0,
        if (orderId != null) ApiStrings.orderId: orderId,
      };

  //! CopyWith
  OrderModel copyWith({
    String? documentId,
    DateTime? createdAt,
    num? total,
    OrderStatus? status,
    UserModel? user,
    ShippingModel? shipping,
    num? deliveryCost,
    num? discount,
    PaymentModel? payment,
    List<ProductQuantityModel>? products,
    AddressModel? addressModel,
    String? note,
    bool? isFromStore,
    int? invoiceId,
    BaseMediaModel? paymentAttachment,
    PromoCodeModel? promoCode,
    int? orderId,
  }) {
    return OrderModel(
      documentId: documentId ?? this.documentId,
      createdAt: createdAt ?? this.createdAt,
      total: total ?? this.total,
      status: status ?? this.status,
      user: user ?? this.user,
      shipping: shipping ?? this.shipping,
      deliveryCost: deliveryCost ?? this.deliveryCost,
      payment: payment ?? this.payment,
      products: products ?? this.products,
      addressModel: addressModel ?? this.addressModel,
      discount: discount ?? this.discount,
      note: note ?? this.note,
      isFromStore: isFromStore ?? this.isFromStore,
      invoiceId: invoiceId ?? this.invoiceId,
      paymentAttachment: paymentAttachment ?? this.paymentAttachment,
      orderId: orderId ?? this.orderId,
      promoCode: promoCode ?? this.promoCode,
    );
  }
}
