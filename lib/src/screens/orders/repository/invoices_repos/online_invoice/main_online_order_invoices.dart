import 'package:colornames/colornames.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/online_invoice/widgets/customer_info_section.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

import '../store_order_invoices.dart';

Future<pw.Document> generateOnlineOrderInvoicePDF(BuildContext appContext,
    {required OrderModel order, required bool isEnglish}) async {
  final currentVendor = appContext.read<AuthVM>().currentVendor;

  // final response = await http.get(Uri.parse(currentVendor?.logo?.url ?? ''));

  // final image = pw.MemoryImage(response.bodyBytes);
  //
  // var response = await get(Uri.parse(currentVendor?.logo?.url ?? ''));
  // var data = response.bodyBytes;

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  englishTable() {
    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: [
          baseTableText(
            appContext.tr.product,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.size,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.color,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.quantity,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.price,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.total,
            arabicFont: arabicFont,
            isBold: true,
          ),
        ],
      ),
    ];

    // Add productQuantity details to the table
    for (var productQuantity in order.products!) {
      final productTotal =
          productQuantity.actualPrice * productQuantity.quantity!;

      children.add(
        pw.TableRow(
          children: [
            baseTableText(
              productQuantity.nameByLang(appContext),
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.size.isEmpty ? '-' : productQuantity.size,
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.color.isEmpty
                  ? '-'
                  : ColorNames.guess(Color(productQuantity.color.toInt())),
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.quantity.toString(),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(
                appContext,
                price: productQuantity.actualPrice,
              ),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(appContext, price: productTotal),
              arabicFont: arabicFont,
            ),
          ],
        ),
      );
    }

    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  arabicTable() {
    //? reverse the order of the columns
    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: [
          baseTableText(
            appContext.tr.total,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.price,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.quantity,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.color,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.size,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.product,
            arabicFont: arabicFont,
            isBold: true,
          ),
        ],
      ),
    ];

    // Add product details to the table
    for (var product in order.products!) {
      final productTotal = product.actualPrice * product.quantity!;

      children.add(
        pw.TableRow(
          children: [
            baseTableText(
              toInvoiceCurrency(appContext, price: productTotal),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(appContext, price: product.actualPrice),

              // product.product!.actualPrice.toStringAsFixed(2),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.quantity.toString(),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.color.isEmpty
                  ? '-'
                  : ColorNames.guess(Color(product.color.toInt())),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.size.isEmpty ? '-' : product.size,
              arabicFont: arabicFont,
            ),
            baseTableText(
              product?.nameByLang(appContext) ?? '-',
              arabicFont: arabicFont,
            ),
          ],
        ),
      );
    }

    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(2),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(2),
      },
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  final vendorImage = await networkImage(currentVendor?.logo?.url ?? '');

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
              level: 0,
              child: pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 10),
                  child: pw.Align(
                      alignment: isEnglish
                          ? pw.Alignment.centerLeft
                          : pw.Alignment.centerRight,
                      child: pw.Column(
                        crossAxisAlignment: isEnglish
                            ? pw.CrossAxisAlignment.start
                            : pw.CrossAxisAlignment.end,
                        children: [
                          pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                //! Order Id --------------------------------
                                baseTableText(
                                  '${appContext.tr.order_id}: ${isEnglish ? '#${order.orderId}' : '${order.orderId}#'}',
                                  arabicFont: arabicFont,
                                ), //! Order Id --------------------------------
                                baseTableText(
                                  '${order.createdAt?.formatDateToStringWithTime}',
                                  arabicFont: arabicFont,
                                ),
                              ]),

                          //! Vendor Logo --------------------------------
                          if (currentVendor?.logo?.url != null &&
                              (currentVendor?.logo?.url!.isNotEmpty ?? false))
                            pw.Center(
                              child: pw.Image(vendorImage,
                                  height: 150, fit: pw.BoxFit.contain),
                            )
                          else
                            pw.Center(
                              child: pw.Text(
                                currentVendor?.name ?? '',
                                textScaleFactor: 2,
                                textAlign: pw.TextAlign.center,
                                textDirection: pw.TextDirection.rtl,
                                style: pw.TextStyle(
                                  fontSize: 30,
                                  font: arabicFont,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ),

                          //! CustomerInfo -------------------------------
                          if (isEnglish)
                            englishCustomerInfo(appContext,
                                arabicFont: arabicFont, order: order)
                          else
                            arabicCustomerInfo(appContext,
                                arabicFont: arabicFont, order: order),
                        ],
                      )))),

          pw.SizedBox(height: 10),

          if (isEnglish) englishTable() else arabicTable(),

          pw.SizedBox(height: 10),

          pw.Divider(
            thickness: .9,
            color: PdfColors.black,
          ),

          pw.SizedBox(height: 10),

          //! Total Order Price & Bottom Section
          if (isEnglish)
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        '${appContext.tr.subtotal}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: (order.total! - order.deliveryCost!) ?? 0),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        '${appContext.tr.delivery_fee}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: order.deliveryCost),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                ],
              ),
            )
          else
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: (order.total! - order.deliveryCost!)),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        '${appContext.tr.subtotal}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ), //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: order.deliveryCost),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        '${appContext.tr.delivery_fee}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ), //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ];
      },
    ),
  );

  return pdf;
}
