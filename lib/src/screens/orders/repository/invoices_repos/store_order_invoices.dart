import 'package:colornames/colornames.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

baseTableText(
  String text, {
  required Font arabicFont,
  bool isBold = false,
  bool isLtr = false,
  PdfColor? color,
  double? fontSize,
}) {
  var english = RegExp(r'[a-zA-Z]');

  var isEnglishText = english.hasMatch(text);

  return pw.Padding(
    padding: const pw.EdgeInsets.all(4),
    child: pw.Text(
      text,
      textScaleFactor: 1,
      textAlign: pw.TextAlign.center,
      textDirection:
          isLtr && isEnglishText ? pw.TextDirection.ltr : pw.TextDirection.rtl,
      style: pw.TextStyle(
        font: arabicFont,
        fontSize: fontSize,
        fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
        color: color,
      ),
    ),
  );
}

String _currentCurrency(BuildContext context) {
  final vendorVM = context.read<AuthVM>();

  final isEnglish = context.read<AppSettingsVM>().locale.languageCode == 'en';

  final currentCurrency = vendorVM.currentVendor;

  final currency = currentCurrency?.config?.currencies.firstOrNull;

  final currencyName = isEnglish ? currency?.currencyEn : currency?.currencyAr;

  return currencyName ?? '';
}

String toInvoiceCurrency(BuildContext context, {required num? price}) {
  final currentCurrency = _currentCurrency(context);

  final num = price.removeDecimalZeroFormat(price ?? 0);

  final isEnglish = context.read<AppSettingsVM>().locale.languageCode == 'en';

  if (isEnglish) {
    return '$currentCurrency$num';
  }

  return '$num$currentCurrency';
}

Future<pw.Document> generateStoreOrderInvoicePDF(BuildContext appContext,
    {required OrderModel order, required bool isEnglish}) async {
  final currentVendor = appContext.read<AuthVM>().currentVendor;

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  final pdf = pw.Document();

  englishTable() {
    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: [
          baseTableText(
            appContext.tr.product,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.size,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.color,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.quantity,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.price,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.total,
            arabicFont: arabicFont,
            isBold: true,
          ),
        ],
      ),
    ];

    // Add productQuantity details to the table
    for (var productQuantity in order.products!) {
      final productTotal =
          (productQuantity.price ?? productQuantity.product!.actualPrice) *
              productQuantity.quantity!;

      children.add(
        pw.TableRow(
          children: [
            baseTableText(
              productQuantity.product?.englishTitle ?? '-',
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.size.isEmpty ? '-' : productQuantity.size,
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.color.isEmpty
                  ? '-'
                  : ColorNames.guess(Color(productQuantity.color.toInt())),
              arabicFont: arabicFont,
            ),
            baseTableText(
              productQuantity.quantity.toString(),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(appContext,
                  price: productQuantity.price ??
                      productQuantity.product!.actualPrice),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(appContext, price: productTotal),
              arabicFont: arabicFont,
            ),
          ],
        ),
      );
    }

    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(1),
      },
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  arabicTable() {
    //? reverse the order of the columns
    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: [
          baseTableText(
            appContext.tr.total,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.price,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.quantity,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.color,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.size,
            arabicFont: arabicFont,
            isBold: true,
          ),
          baseTableText(
            appContext.tr.product,
            arabicFont: arabicFont,
            isBold: true,
          ),
        ],
      ),
    ];

    // Add product details to the table
    for (var product in order.products!) {
      final productTotal = product.product!.actualPrice * product.quantity!;

      children.add(
        pw.TableRow(
          children: [
            baseTableText(
              toInvoiceCurrency(appContext, price: productTotal),
              arabicFont: arabicFont,
            ),
            baseTableText(
              toInvoiceCurrency(appContext,
                  price: product.product!.actualPrice),

              // product.product!.actualPrice.toStringAsFixed(2),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.quantity.toString(),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.color.isEmpty
                  ? '-'
                  : ColorNames.guess(Color(product.color.toInt())),
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.size.isEmpty ? '-' : product.size,
              arabicFont: arabicFont,
            ),
            baseTableText(
              product.product?.englishTitle ?? '-',
              arabicFont: arabicFont,
            ),
          ],
        ),
      );
    }

    return pw.Table(
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(2),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(2),
      },
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  var vendorImage;

  try {
    vendorImage = await networkImage(currentVendor?.logo?.url ?? '');
  } catch (e) {}

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
              level: 0,
              child: pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 10),
                  child: pw.Align(
                      alignment: isEnglish
                          ? pw.Alignment.centerLeft
                          : pw.Alignment.centerRight,
                      child: pw.Column(
                        crossAxisAlignment: isEnglish
                            ? pw.CrossAxisAlignment.start
                            : pw.CrossAxisAlignment.end,
                        children: [
                          //! Vendor Logo --------------------------------
                          if (currentVendor?.logo?.url != null &&
                              (currentVendor?.logo?.url!.isNotEmpty ?? false))
                            pw.Center(
                              child: pw.Image(vendorImage,
                                  height: 180, fit: pw.BoxFit.contain),
                            )
                          else
                            pw.Center(
                              child: pw.Text(
                                currentVendor?.name ?? '',
                                textScaleFactor: 2,
                                textAlign: pw.TextAlign.center,
                                textDirection: pw.TextDirection.rtl,
                                style: pw.TextStyle(
                                  fontSize: 30,
                                  font: arabicFont,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ),

                          pw.Text(
                            '${appContext.tr.order_id}: ${isEnglish ? '#${order.documentId}' : '${order.documentId}#'}',
                            textScaleFactor: 2,
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.Text(
                            '${appContext.tr.dateAndTime}: ${order.createdAt?.formatDateToStringWithTime}',
                            textScaleFactor: 2,
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        ],
                      )))),

          pw.SizedBox(height: 10),

          if (isEnglish) englishTable() else arabicTable(),

          pw.SizedBox(height: 10),

          pw.Divider(
            thickness: .9,
            color: PdfColors.black,
          ),

          pw.SizedBox(height: 10),

          //! Total Order Price & Bottom Section
          if (isEnglish)
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                ],
              ),
            )
          else
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ];
      },
    ),
  );

  return pdf;
}
