import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shimmer_loading/home_loading/total_loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import '../../../reports/widgets/report_total_widget.dart';

class InvoiceTotalWidget extends StatelessWidget {
  final bool isLoading;
  const InvoiceTotalWidget({super.key, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return Consumer<OrderVM>(
      builder: (context, orderVM, child) {
        if (isLoading) {
          return const TotalLoadingWidget();
        }
        return Row(
          children: [
            Expanded(
              child: WidgetAnimator(
                delay: const Duration(milliseconds: 100),
                child: TotalItemWidget(
                  title: context.tr.totalEarning,
                  total: orderVM.storeTotal,
                ),
              ),
            ),
            context.mediumGap,
            Expanded(
              child: WidgetAnimator(
                delay: const Duration(milliseconds: 200),
                child: TotalItemWidget(
                  isTotalEarnings: false,
                  title: context.tr.totalOrders,
                  total: orderVM.storeOrdersCount,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
