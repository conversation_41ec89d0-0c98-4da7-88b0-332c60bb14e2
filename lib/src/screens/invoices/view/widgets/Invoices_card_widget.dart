import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/invoices/view/widgets/invoice_top_section.dart';
import 'package:idea2app_vendor_app/src/screens/invoices/view/widgets/invoices_product_card_widget.dart';

import '../../../../core/resources/theme/theme.dart';
import '../../../orders/models/order/order_model.dart';

class InvoicesCardWidget extends StatelessWidget {
  final OrderModel order;

  const InvoicesCardWidget({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
        iconColor: ColorManager.primaryColor,
        shape: LinearBorder.none,
        tilePadding: EdgeInsets.zero,
        title: InvoiceTitleSection(
          order: order,
        ),
        children: order.products!
            .map((e) =>
                InvoicesProductCardWidget(productQuantity: e).paddingSymmetric(
                  vertical: AppSpaces.xSmallPadding,
                ))
            .toList());
  }
}
