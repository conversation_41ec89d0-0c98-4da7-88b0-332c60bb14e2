import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/pagination_widget/base_pagination_widget.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/app_constants.dart';
import '../../../core/shared_widgets/shimmer_loading/home_loading/orders_loading.dart';
import '../../orders/view_model/order_view_model.dart';
import 'widgets/Invoices_card_widget.dart';

final GlobalKey<ScaffoldState> invoicesScaffoldKey = GlobalKey<ScaffoldState>();

class StoreInvoicesScreen extends HookWidget {
  const StoreInvoicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final orderVM = context.read<OrderVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        orderVM.setOrderFilter(context,
            filter: orderVM.orderFilter.copyWith(
              limit: 5,
              storeInvoices: true,
            ));
      });

      return () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          orderVM.clearFilter();
        });
      };
    }, []);

    return Scaffold(
        key: invoicesScaffoldKey,
        appBar: MainAppBar(
          title: context.tr.storeInvoices,
          haveBackButton: true,
        ),
        body: Consumer<OrderVM>(
          builder: (context, orderVM, child) {
            final orders = orderVM.storeOrder;

            final isInitialLoading =
                (orderVM.isLoading) && orderVM.orderFilter.limit == 5;

            if (orders.isEmpty) {
              return EmptyDataWidget(
                message: context.tr.noStoreInvoices,
                isLoading: isInitialLoading,
                loadingWidget: const OrdersLoading(),
              );
            }

            return BasePaginationWidget(
              isLoading: orderVM.isLoading,
              onScrollEnd: () {
                final filter = orderVM.orderFilter.copyWith(
                  limit: orderVM.orderFilter.limit + 10,
                  storeInvoices: true,
                );

                orderVM.setOrderFilter(context, filter: filter);
              },
              child: Stack(
                children: [
                  ListView.separated(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    itemCount: orders.length,
                    itemBuilder: (context, index) {
                      var order = orders[index];
                      return WidgetAnimator(
                        delay: Duration(
                            milliseconds:
                                AppConsts.orderAnimatedDuration * index),
                        child: InvoicesCardWidget(
                          order: order,
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return context.xSmallGap;
                    },
                  ),
                  if (orderVM.isLoading)
                    const Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: LoadingWidget(
                        isLinear: true,
                      ),
                    ),
                ],
              ),
            );
          },
        ));
  }
}
