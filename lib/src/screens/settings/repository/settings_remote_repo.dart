import 'dart:async';

import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';

import '../model/settings_model.dart';

class SettingsRemoteRepo {
  final BaseApiServices _networkApiServices;

  SettingsRemoteRepo(this._networkApiServices);

//! Get Settings =====================================

  Future<SettingsModel> getSettings() async {
    try {
      final settings =
          await _networkApiServices.getResponse(ApiEndPoints.settings);

      final settingsData = SettingsModel.fromJson(settings);
      return settingsData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }
}
