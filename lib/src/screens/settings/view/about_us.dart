import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/app_constants.dart';
import '../../../core/resources/app_spaces.dart';
import '../../../core/shared_widgets/animated/entrance_fader.dart';
import '../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../view_model/settings_view_model.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsVM>(
      builder: (context, settingsVM, child) {
        final about = context.isEng
            ? settingsVM.settings?.aboutUsEn
            : settingsVM.settings?.aboutUsAr;

        return Scaffold(
          appBar: MainAppBar(
            haveBackButton: true,
            title: context.tr.aboutUs,
          ),
          body: settingsVM.isLoading
              ? const LoadingWidget()
              : WidgetAnimator(
                  delay:
                      const Duration(milliseconds: AppConsts.animatedDuration),
                  child: Text(
                    about ?? '',
                    style: context.title,
                    textAlign: TextAlign.center,
                  ).paddingAll(AppSpaces.largePadding),
                ),
        );
      },
    );
  }
}
