import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/resources/app_spaces.dart';
import '../../../core/shared_widgets/appbar/main_appbar.dart';
import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../view_model/settings_view_model.dart';

class ContactUsScreen extends StatelessWidget {
  final bool fromDrawer;

  const ContactUsScreen({super.key, this.fromDrawer = true});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsVM>(
      builder: (context, settingsVM, child) {
        final contactUS = settingsVM.settings?.contactUs;

        return Scaffold(
          appBar: MainAppBar(
            title: context.tr.contactUs,
            haveBackButton: true,
          ),
          body: settingsVM.isLoading
              ? const LoadingWidget()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //! Website ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.website,
                        Assets.iconsWebsite,
                        contactUS?.website?.url ?? '',
                      ),
                    ),

                    context.largeGap,

                    //! Email ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.email,
                        Assets.iconsEmail,
                        contactUS?.email?.url ?? '',
                      ),
                    ),

                    context.largeGap,

                    //! Facebook ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.facebook,
                        Assets.iconsFacebook,
                        contactUS?.facebook?.url ?? '',
                      ),
                      color: ColorManager.facebookColor,
                    ),

                    context.largeGap,

                    //! Instagram ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.instagram,
                        Assets.iconsInstagram,
                        contactUS?.instagram?.url ?? '',
                      ),
                      color: Colors.deepOrange,
                    ),

                    context.largeGap,

                    //! Tiktok ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.tiktok,
                        Assets.iconsTiktok,
                        contactUS?.tiktok?.url ?? '',
                      ),
                      color: context.isDark ? Colors.white : Colors.black,
                    ),

                    context.largeGap,

                    //! youtube ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.youtube,
                        Assets.iconsYoutube,
                        contactUS?.youtube?.url ?? '',
                      ),
                      color: context.isDark ? Colors.white : Colors.black,
                    ),

                    context.largeGap,
                    //! WhatsApp ---------------------------
                    _ContactUsColumn(
                      titleAndSubTitleAndIconPAthAndLink: (
                        context.tr.whatsApp,
                        Assets.iconsWhatsApp,
                        contactUS?.whatsapp?.url ?? '',
                      ),
                    ),
                  ],
                ).paddingAll(AppSpaces.largePadding),
        );
      },
    );
  }
}

class _ContactUsColumn extends StatelessWidget {
  final (String, String, String) titleAndSubTitleAndIconPAthAndLink;
  final Color color;

  const _ContactUsColumn({
    required this.titleAndSubTitleAndIconPAthAndLink,
    this.color = ColorManager.primaryColor,
  });

  @override
  Widget build(BuildContext context) {
    final title = titleAndSubTitleAndIconPAthAndLink.$1;
    final iconPath = titleAndSubTitleAndIconPAthAndLink.$2;
    final link = titleAndSubTitleAndIconPAthAndLink.$3;
    link;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      onTap: () => openURL(link),
      trailing: const Icon(
        Icons.arrow_forward_ios_rounded,
        size: 15,
        color: ColorManager.primaryColor,
      ),
      leading: Image.asset(
        iconPath,
        width: 30.w,
        height: 30.h,
        fit: BoxFit.contain,
      ),
      title: Text(
        title,
        style: context.title,
      ),
    );
  }
}

void openURL(String url) {
  Log.w('URL -> $url');
  launchUrl(
    Uri.parse(url),
  );
}

void launchWhatsApp() {
  openURL('https://wa.link/cv1r8s');
}

Future<void> shareQrCode(
    {required GlobalKey qrKey,
    required BuildContext context,
    required String link}) async {
  try {
    final boundary =
        qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    if (boundary != null) {
      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/qr_code.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles([XFile(file.path)], text: link);
    }
  } catch (e) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to share QR code')),
    );
  }
}
