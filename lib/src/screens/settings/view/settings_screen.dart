import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/terms_and_condition.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/app_constants.dart';
import '../../drawer/widgets/settings_widgets.dart';

class SettingsScreen extends HookWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.appSettings,
        haveBackButton: true,
      ),
      body: Consumer2<AppSettingsVM, AuthVM>(
        builder: (context, settingsVM, authVM, child) {
          return Column(
            children: [
              BaseSettingsContainer(
                  title: context.tr.app_settings,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      //! About us
                      SettingsWidget(
                        onTap: () => context.to(DynamicSettingsScreen(
                          title: context.tr.aboutUs,
                        )),
                        header: context.tr.aboutUs,
                        iconPath: Assets.iconsAbout,
                      ),

                      //! Terms & Condition
                      SettingsWidget(
                        onTap: () => context.to(DynamicSettingsScreen(
                          title: context.tr.termsAndCondition,
                        )),
                        header: context.tr.termsAndCondition,
                        iconPath: Assets.iconsLicense,
                      ),

                      //! Contact us
                      SettingsWidget(
                        onTap: () => context.to(const ContactUsScreen()),
                        header: context.tr.contactUs,
                        iconPath: Assets.iconsPhone,
                      ),

                      //! Change Language
                      SettingsWidget(
                        iconPath: Assets.iconsLanguage,
                        header: context.tr.languages,
                        trailingWidget: BaseDropDown<Locale>(
                          value: settingsVM.locale,
                          onChanged: (value) {
                            settingsVM.updateLanguage(value!);
                          },
                          data: AppConsts.supportedLocales
                              .map((loc) => DropdownMenuItem(
                                    value: loc,
                                    child: Text(
                                      context.langText(loc.languageCode),
                                      style: context.labelMedium,
                                    ),
                                  ))
                              .toList(),
                        ).sized(height: 25.h),
                      ),

                      //! Change Theme
                      // SettingsWidget(
                      //   header: context.tr.appearance,
                      //   trailingWidget: BaseDropDown<ThemeMode>(
                      //     value: settingsVM.themeMode,
                      //     onChanged: (value) {
                      //       settingsVM.changeAppMode(value);
                      //
                      //       Log.f(
                      //           "settingsVM.themeMode: ${settingsVM.themeMode}");
                      //     },
                      //     data: themeList
                      //         .map(
                      //           (value) => DropdownMenuItem(
                      //             value: value,
                      //             child: Text(
                      //               context.themeText(value.index),
                      //               style: context.labelMedium,
                      //             ),
                      //           ),
                      //         )
                      //         .toList(),
                      //   ).sized(height: 25.h),
                      //   iconPath:
                      //       context.isDark ? Assets.iconsMoon : Assets.iconsSun,
                      // ),
                      SettingsWidget(
                        header:
                            '${context.tr.appearance} (${settingsVM.themeMode == ThemeMode.dark ? context.tr.dark_mode : context.tr.light_mode})',
                        trailingWidget: SwitchButtonWidget(
                          onChanged: (value) {
                            settingsVM.changeAppMode(
                                value ? ThemeMode.dark : ThemeMode.light);
                          },
                          value: ValueNotifier(
                              settingsVM.themeMode == ThemeMode.dark ||
                                  context.isDark),
                        ),
                        iconPath:
                            context.isDark ? Assets.iconsMoon : Assets.iconsSun,
                      ),
                    ],
                  )),
            ],
          ).paddingAll(AppSpaces.mediumPadding);
        },
      ),
    );
  }
}

class BaseSettingsContainer extends StatelessWidget {
  final String title;

  final Widget child;

  const BaseSettingsContainer(
      {super.key, required this.child, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          color: context.appTheme.cardColor,
          boxShadow: context.isDark
              ? ConstantsWidgets.darkBoxShadowFromBottom
              : ConstantsWidgets.boxShadowFromBottom),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Image.asset(
                Assets.iconsSettings,
                height: 25,
              ),
              context.smallGap,
              Text(
                title,
                style:
                    context.subTitle.copyWith(color: ColorManager.primaryColor),
              ),
            ],
          ),
          context.largeGap,
          child,
        ],
      ),
    );
  }
}
