import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/tasks/model/tasks_model.dart';

import 'tasks_card_widget.dart';

class TasksList extends StatelessWidget {
  final List<TasksModel> tasks;

  const TasksList({super.key, required this.tasks});

  @override
  Widget build(BuildContext context) {
    if (tasks.isEmpty) {
      return Center(
        child: Text(
          context.tr.noTasks,
          style: context.headLine,
        ).paddingOnly(top: context.height / 3.2),
      );
    }
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) => TasksCardWidget(
              task: tasks[index],
            ),
        separatorBuilder: (context, index) => context.mediumGap,
        itemCount: tasks.length);
  }
}
