import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/tasks/controller/tasks_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/tasks/view/widgets/add_tasks_dialog.dart';
import 'package:idea2app_vendor_app/src/screens/tasks/view/widgets/tasks_list.dart';
import 'package:provider/provider.dart';

import '../../../admin/vendors/view/vendors_screen/vendors_screen.dart';
import '../../../core/resources/app_spaces.dart';
import '../../../core/shared_widgets/appbar/main_appbar.dart';

class TasksScreen extends HookWidget {
  const TasksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final tasksVM = context.read<TasksVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        tasksVM.getTasks(context);
      });
      return () {};
    }, []);

    return Consumer<TasksVM>(
      builder: (context, tasksVM, child) {
        final totalUnCompletedTasks = tasksVM.tasks
                .where((element) => element.isDone == false)
                .toList()
                .length ??
            0;
        return Scaffold(
            floatingActionButton: FloatingActionButton(
              backgroundColor: ColorManager.primaryColor,
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return const AddTasksDialog();
                  },
                );
              },
              child: const Icon(
                Icons.add,
                color: ColorManager.white,
                size: 30,
              ),
            ),
            appBar: MainAppBar(
              title: currentAdminVendor != null
                  ? '${currentAdminVendor?.name} ${context.tr.tasks}'
                  : context.tr.tasks,
              haveBackButton: true,
            ),
            body: tasksVM.isLoading
                ? const Center(child: LoadingWidget())
                : ListView(
                    padding: const EdgeInsets.only(
                      right: AppSpaces.mediumPadding,
                      left: AppSpaces.largePadding,
                      top: AppSpaces.largePadding,
                      bottom: AppSpaces.largePadding * 2,
                    ),
                    children: [
                      Row(
                        children: [
                          Expanded(
                              child: TotalTasksWidget(
                            title: context.tr.totalTasks,
                            total: tasksVM.tasks.length,
                          )),
                          context.mediumGap,
                          Expanded(
                              child: TotalTasksWidget(
                            title: context.tr.uncompleted,
                            total: totalUnCompletedTasks,
                            isCompletedTask: false,
                          )),
                        ],
                      ),
                      context.largeGap,
                      TasksList(tasks: tasksVM.tasks)
                    ],
                  ));
      },
    );
  }
}

class TotalTasksWidget extends StatelessWidget {
  final String title;
  final int total;
  final bool isCompletedTask;

  const TotalTasksWidget(
      {super.key,
      required this.title,
      required this.total,
      this.isCompletedTask = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
          color: context.appTheme.cardColor,
          borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          Text(
            title,
            style: context.title,
          ),
          context.mediumGap,
          Text(
            total.toString(),
            style: context.whiteSubHeadLine.copyWith(
                color: isCompletedTask
                    ? ColorManager.successColor
                    : ColorManager.errorColor),
          ),
        ],
      ),
    );
  }
}
