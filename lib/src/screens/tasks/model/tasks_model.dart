import '../../../core/consts/api_strings.dart';
import '../../../core/shared_models/base_media_model.dart';

class TasksModel {
  final String? documentId;
  final String title;
  final String? description;
  final bool isDone;
  final String createdAt;
  final BaseMediaModel? image;

  const TasksModel({
    this.documentId,
    this.title = '',
    this.isDone = false,
    this.description = '',
    this.createdAt = '',
    this.image,
  });

  factory TasksModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.attachment] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.attachment])
        : null;
    return TasksModel(
      documentId: json[ApiStrings.documentId],
      description: json[ApiStrings.description],
      isDone: json[ApiStrings.isDone],
      title: json[ApiStrings.title] ?? '',
      image: image,
      createdAt: json[ApiStrings.createdAt] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (documentId != null) ApiStrings.documentId: documentId,
      if (title.isNotEmpty) ApiStrings.title: title,
      if (title.isNotEmpty) ApiStrings.description: description,
      ApiStrings.isDone: isDone,
    };
  }
}
