import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:rounded_loading_button/rounded_loading_button.dart';

import '../consts/app_constants.dart';

class BaseVM extends ChangeNotifier {
  //! Loading ====================================
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set setIsLoading(bool isLoading) {
    _isLoading = isLoading;
    notifyListeners();
  }

  final buttonController = RoundedLoadingButtonController();

  //! Render Success
  void success() {
    buttonController.success();
  }

  //! Error Button
  void error() {
    buttonController.error();
  }

  //! Reset Button Controller
  void reset() {
    buttonController.reset();
  }

  //! Reset Button Controller
  Future<void> resetErrorWithDuration() async {
    error();

    await Future.delayed(const Duration(milliseconds: AppConsts.errorDuration))
        .then((value) => reset());
  }

  //! Handle Success ====================================
  void handleSuccess(BuildContext context,
      [FlushBarType type = FlushBarType.add, bool isBack = true]) {
    if (type != FlushBarType.delete && isBack) {
      context.back();
    }

    context.showFlushBar(type: type);

    setIsLoading = false;
  }

  //! Base Function With Exception Handling ====================================
  Future<dynamic> baseFunction(BuildContext context, Function() mainFunction,
      {FlushBarType? type,
      bool isBack = true,
      bool isLoading = true,
      Function(BuildContext context)? additionalFunction,
      Function(Object error, Object stack)? onError,
      RoundedLoadingButtonController? buttonController}) async {
    try {
      final isDelete = type == FlushBarType.delete;

      if (!isDelete && isLoading) {
        setIsLoading = isLoading;
      }

      final function = await mainFunction();

      if (context.mounted) {
        await _handleSuccess(context,
            type: type,
            isBack: isBack,
            additionalFunction: additionalFunction,
            buttonController: buttonController);
      }

      return function;
    } on FetchDataException catch (error, s) {
      Log.e(error.toString() + s.toString());
      setIsLoading = false;
      if (buttonController != null) {
        buttonController.reset();
      }

      if (context.mounted) {
        context.showFlushBar(type: FlushBarType.error);
      }
    } on SocketException {
      setIsLoading = false;

      if (buttonController != null) {
        buttonController.reset();
      }

      if (context.mounted) {
        context.showFlushBar(type: FlushBarType.noInternet);
      }
    } on TimeoutException {
      setIsLoading = false;
      if (buttonController != null) {
        buttonController.reset();
      }

      if (context.mounted) {
        context.showFlushBar(type: FlushBarType.timeOut);
      }
    } catch (error, s) {
      Log.e(error.toString() + s.toString());

      if (onError != null) {
        onError(error, s);
      }

      if (buttonController != null) {
        buttonController.reset();
      }

      setIsLoading = false;

      if (context.mounted) {
        context.showFlushBar(type: FlushBarType.error);
      }
    } finally {
      if (buttonController != null) {
        buttonController.reset();
      }
      setIsLoading = false;
    }
  }

  Future<void> _handleSuccess(BuildContext context,
      {FlushBarType? type,
      bool isBack = true,
      RoundedLoadingButtonController? buttonController,
      Function(BuildContext context)? additionalFunction}) async {
    if (additionalFunction != null) {
      await additionalFunction(context);
    }

    if (buttonController != null) {
      if (type == FlushBarType.add || type == FlushBarType.update) {
        buttonController.success();
      } else if (type == FlushBarType.error) {
        buttonController.error();
      }
      await Future.delayed(const Duration(seconds: 1));
    }

    if (type != null) {
      if (!context.mounted) return;
      handleSuccess(context, type, isBack);
    } else {
      setIsLoading = false;
    }
  }
}
