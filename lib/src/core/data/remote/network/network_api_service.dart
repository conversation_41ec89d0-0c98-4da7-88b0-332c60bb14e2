import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/local/local_keys.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/models/upload_image_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:universal_html/html.dart' as html;

import '../../../../screens/auth/models/helper_models/vendor_helper_model.dart';
import '../../../utils/logger.dart';
import '../app_exception.dart';
import '../response/api_end_points.dart';
import 'base_api_service.dart';

class NetworkApiService extends BaseApiServices {
  //! Get request
  @override
  Future getResponse(String url,
      {bool sortByCreatedAt = true,
      bool withOutVendor = false,
      int? populateLevel}) async {
    dynamic responseJson;
    try {
      await getVendorResponseAndUpdateLocal(
        url: url,
      );
      final endpoint = ApiEndPoints.apiURL + url;

      final shouldFilterWithVendor = !endpoint.contains('vendors') &&
          !endpoint.contains('setting') &&
          !withOutVendor;

      final filterWithLimit =
          endpoint.contains('limit') ? '' : '&pagination[limit]=500';

      const populate = '&pLevel';

      final shouldPopulateDeep = endpoint.contains('shipping') ||
          endpoint.contains('configs') ||
          endpoint.contains('product-categories') ||
          !shouldFilterWithVendor;

      final shouldPopulateVeryDeep = endpoint.contains('orders') ||
          endpoint.contains('subscription-requests');

      final populateLevelDepth = populateLevel ??
          (shouldPopulateVeryDeep
              ? '4'
              : shouldPopulateDeep
                  ? '3'
                  : '2');

      final sortBy = sortByCreatedAt ? '&sort[0]=createdAt:desc' : '';

      final vendorBusinessNameFilter = currentAdminVendor?.businessName ??
          VendorModelHelper.currentVendorBusinessName();

      final vendorFilter = shouldFilterWithVendor
          ? '${endpoint.contains('?') ? '&' : '?'}filters[vendor][business_name]=$vendorBusinessNameFilter$filterWithLimit${sortBy}$populate=$populateLevelDepth'
          : '${endpoint.contains('?') ? '&' : '?'}${endpoint.contains('limit') ? '' : 'pagination[limit]=500&'}$sortBy$populate=$populateLevelDepth';

      final filteredEndpoint = '$endpoint$vendorFilter';

      Log.i('GetApiUrl => $filteredEndpoint');

      final response = await http.get(
        Uri.parse(
          filteredEndpoint,
        ),
        headers: ApiEndPoints.headers,
      );

      final decodedRes = jsonDecode(response.body);

      // Log.w('GET_URL $filteredEndpoint\nRES => $decodedRes');

      final data = decodedRes.runtimeType == List
          ? decodedRes
          : decodedRes.containsKey('data')
              ? decodedRes['data']
              : decodedRes;

      responseJson = data;

      return responseJson;
      // responseJson = returnResponse(response);
    } on SocketException {
      rethrow;
    } catch (e) {
      throw FetchDataException(
          "FetchDataException: getResponse ${e.toString()}");
    }
  }

  // get vendor response
  Future getVendorResponseAndUpdateLocal({required String url}) async {
    if (url.contains('vendors')) return;

    try {
      final response = await http.get(
        Uri.parse(
          '${ApiEndPoints.apiURL}${ApiEndPoints.vendors}/${VendorModelHelper.currentVendorDocumentId()}?pLevel=1',
        ),
        headers: ApiEndPoints.headers,
      );
      final decodedRes = jsonDecode(response.body);

      if (decodedRes == null || decodedRes['data'] == null) {
        return null;
      }

      final vendorsData = VendorModelHelper.currentVendorModel()
          .copyWith(id: decodedRes['data']['id']);

      final encodedVendor = jsonEncode(vendorsData.toJson(
        fromLocal: true,
      ));

      GetStorageHandler.setLocalData(
          key: LocalKeys.vendorData, value: encodedVendor);

      Log.w('UpdatedCurrentVendorId ${decodedRes['data']['id']}');
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Upload multiple files
  @override
  Future<List<int>> uploadFiles(
      {required List<String?> fileResult,
      UploadImageModel? uploadImageModel}) async {
    List<int> assetsIds = [];

    try {
      http.MultipartRequest request = http.MultipartRequest(
          'POST', Uri.parse(ApiEndPoints.apiURL + ApiEndPoints.uploadFile));

      if (kIsWeb) {
        final webFiles = webFilesPickedResult.value;

        for (var file in webFiles) {
          final fileExtension = file.name.split('.').last;
          final mimeType = fileExtension == 'png' ? 'png' : 'jpeg';

          final reader = html.FileReader();
          reader.readAsArrayBuffer(file);
          await reader.onLoad.first;
          final bytes = reader.result as Uint8List;

          request.files.add(http.MultipartFile.fromBytes(
            'files',
            bytes,
            filename: file.name,
            contentType: MediaType('image', mimeType),
          ));
        }
      } else {
        for (String? filePath in fileResult) {
          final fileExtension = filePath!.split('.').last;
          final mimeType = fileExtension == 'png' ? 'png' : 'jpeg';

          request.files.add(await http.MultipartFile.fromPath(
            'files',
            filePath,
            contentType: MediaType('image', mimeType),
            filename: filePath.split('/').last,
          ));
        }
      }

      if (uploadImageModel != null) {
        Log.i(
            'UploadFilesFieldsModel -------------------> ${uploadImageModel.toJson()}');
        request.fields.addAll(
          uploadImageModel
              .toJson()
              .map((key, value) => MapEntry(key, value.toString())),
        );
      }

      request.headers.addAll(ApiStrings.headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson =
            await jsonDecode(await response.stream.bytesToString());

        Log.f('UploadFilesResponse -------------------> $responseJson');
        assetsIds = (responseJson as List?)?.map(
              (asset) {
                return asset['id'] as int;
              },
            ).toList() ??
            [];

        Log.f('AssetIDS $assetsIds');
      } else {
        final body = await response.stream.bytesToString();
        throw FetchDataException(
            'Error occurred while communication with server with status code : ${response.statusCode} \n BODDDD $body');
      }
    } on SocketException {
      throw FetchDataException('No Internet Connection');
    }
    return assetsIds;
  }

  // get vendorId
  Future<int?> getVendorIdByDocId() async {
    try {
      final response = await getResponse(
        '${ApiEndPoints.vendors}/${VendorModelHelper.currentVendorDocumentId()}',
        populateLevel: 1,
      );

      if (response == null) return null;

      final vendorId = response['id'];

      Log.w('CurrentVendorId => $vendorId');

      return vendorId;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  @override
  Future postResponse(
    String url, {
    required Map<String, dynamic> data,
    List<String>? fileResult,
    String? fieldName,
    String? relationName,
    bool connectWithVendorRelation = true,
    bool withOutVendor = false,
  }) async {
    dynamic responseJson;

    try {
      // await getVendorResponseAndUpdateLocal(
      //   url: url,
      // );
      final apiUrl = Uri.parse(ApiEndPoints.apiURL + url);

      if (
          // (
          //     VendorModelHelper.currentVendorId() != null ||
          //         data.containsKey(ApiStrings.vendor)) &&
          !url.contains('vendors') && !withOutVendor) {
        data[ApiStrings.vendor] = (await getVendorIdByDocId()) ??
            VendorModelHelper.currentVendorId() ??
            data[ApiStrings.vendor];
        data[ApiStrings.vendor] =
            VendorModelHelper.currentVendorId() ?? data[ApiStrings.vendor];
      }

      List<int> assetsIds = [];

      if (fileResult != null &&
          fileResult.isNotEmpty &&
          fileResult.any((element) => element != '')) {
        assetsIds = await uploadFiles(
          fileResult: fileResult,
        );
      }

      if (fieldName != null && assetsIds.isNotEmpty) {
        data[fieldName] = assetsIds;
      }

      Log.w('PostApiUrl => $apiUrl\n 💾💾💾 PostResponse -> $data 💾💾💾');

      if (data.containsKey(ApiStrings.documentId)) {
        data.remove(ApiStrings.documentId);
      }

      if (data.containsKey(ApiStrings.id)) {
        data.remove(ApiStrings.id);
      }

      final res = http.post(
        apiUrl,
        body: jsonEncode({
          ApiStrings.data: data,
        }),
        headers: ApiEndPoints.headers,
      );

      final response = await res.timeout(const Duration(
        seconds: ApiStrings.timeOutDuration,
      ));

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = jsonDecode(response.body)['data'];

        if (connectWithVendorRelation) {
          final docId = responseJson['documentId'];

          final isPlural = url.endsWith('s');

          await connectRelation(
            data: {
              relationName ?? url: {
                "connect": isPlural ? [docId] : docId
              }
            },
          );
        }

        Log.w('PostURL => $apiUrl\nPostRes => $responseJson');

        return responseJson;
      } else {
        throw FetchDataException(
            'Error occurred while communication with server: ${response.body} with status code : ${response.statusCode}');
      }
    } on SocketException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } catch (e) {
      throw FetchDataException(
          "FetchDataException: getResponse ${e.toString()}");
    }
  }

  @override
  Future putResponse(
    String url, {
    required Map<String, dynamic> data,
    List<String>? fileResult,
    String? fieldName,
    String? relationName,
    bool connectWithVendorRelation = false,
  }) async {
    dynamic responseJson;

    try {
      // await getVendorResponseAndUpdateLocal(
      //   url: url,
      // );

      final documentId = data[ApiStrings.documentId];

      final apiUrl = Uri.parse(
          '${ApiEndPoints.apiURL}$url${documentId != null ? '/$documentId' : ''}');

      // data[ApiStrings.vendor] =
      //     VendorModelHelper.currentVendorId() ?? data[ApiStrings.vendor];

      data.remove(ApiStrings.documentId);

      if (data.containsKey(ApiStrings.id)) {
        data.remove(ApiStrings.id);
      }

      List<int?> assetsIds = [];

      if (fileResult != null &&
          fileResult.isNotEmpty &&
          fileResult.any((element) => element != '')) {
        assetsIds = await uploadFiles(
          fileResult: fileResult,
        );
      }

      if (fieldName != null && assetsIds.isNotEmpty) {
        final oldAssets = data[fieldName]?.toList();
        if (oldAssets != null) {
          assetsIds = [...oldAssets, ...assetsIds];
        }

        data[fieldName] = assetsIds;
      }

      Log.w('PutApiUrl => $apiUrl\n 💾💾💾 PutResponse -> $data 💾💾💾');

      final res = http.put(
        apiUrl,
        body: jsonEncode({
          ApiStrings.data: data,
        }),
        headers: ApiEndPoints.headers,
      );

      final response = await res
          .timeout(const Duration(seconds: ApiStrings.timeOutDuration));

      Log.w('Res => ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body)['data'];

        responseJson = data;

        if (connectWithVendorRelation) {
          final docId = responseJson['documentId'];

          final isPlural = url.endsWith('s');

          await connectRelation(
            data: {
              relationName ?? url: {
                "connect": isPlural ? [docId] : docId
              }
            },
          );
        }

        Log.w('PutResSSSS => $responseJson');

        return responseJson;
      } else {
        throw FetchDataException(
            'Error occurred while communication with server: ${response.body} with status code : ${response.statusCode}');
      }
    } on SocketException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } catch (e, s) {
      throw FetchDataException(
          "FetchDataException: getResponseError $url\nIDDDDD$data\n${e.toString()}\n$s");
    }
  }

  //! Delete request
  @override
  Future deleteResponse(String url) async {
    try {
      await getVendorResponseAndUpdateLocal(
        url: url,
      );
      final apiUrl = Uri.parse(ApiEndPoints.apiURL + url);

      Log.e('DeleteApiUrl => $apiUrl');

      final response = await http.delete(apiUrl, headers: ApiEndPoints.headers);

      if (response.statusCode == 200 ||
          response.statusCode == 201 ||
          response.statusCode == 204) {
        Log.e('DeleteUrl => $apiUrl\nDeleteRes => ${response.body}');

        return response;
      } else {
        Log.e(
            'DeleteUrlError => $apiUrl\nDeleteResError => ${response.body} Status Code => ${response.statusCode}');
        throw FetchDataException(
            'Error occurred while communication with server: ${response.body} with status code : ${response.statusCode}');
      }
    } on SocketException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } catch (e) {
      throw FetchDataException(
          "FetchDataException: getResponse ${e.toString()}");
    }
  }

  @override
  Future<dynamic> deleteImage({required int? imageId}) async {
    if (imageId == null) return;

    dynamic responseJson;
    try {
      final url =
          '${ApiEndPoints.apiURL}${ApiEndPoints.uploadFile}/files/$imageId';

      Log.e('DeleteImageFromMediaLibraryUrl => $url');

      final response = await http.delete(Uri.parse(url));

      Log.e(
          'DeleteImageFromMediaLibraryUrl => $url\nDeleteRes => ${response.body}');

      responseJson = returnResponse(response);
    } on SocketException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } catch (e) {
      throw FetchDataException(
          "FetchDataException: getResponse ${e.toString()}");
    }
    return responseJson;
  }

  dynamic returnResponse(http.Response response) {
    switch (response.statusCode) {
      case 200:
        return jsonDecode(response.body.toString());
      case 400:
        throw FetchDataException(response.toString());
      case 401:
      case 403:
        throw UnauthorisedException(response.body.toString());
      case 404:
        throw UnauthorisedException(response.body.toString());
      case 500:
      default:
        throw FetchDataException(
            'Error occurred while communication with server: ${response.body} with status code : ${response.statusCode}');
    }
  }

  @override
  Future<void> connectRelation({required Map<String, dynamic> data}) async {
    final currentDocumentId = currentAdminVendor?.documentId ??
        VendorModelHelper.currentVendorDocumentId();

    data[ApiStrings.documentId] = currentDocumentId;

    final newVendor = await putResponse(
      ApiEndPoints.vendors,
      data: data,
    );

    final vendor = VendorModel.fromJson(newVendor);

    final encodedVendor = jsonEncode(vendor.toJson(fromLocal: true));

    //! Update Local Storage
    await GetStorageHandler.setLocalData(
      key: LocalKeys.vendorData,
      value: encodedVendor,
    );
  }
}
