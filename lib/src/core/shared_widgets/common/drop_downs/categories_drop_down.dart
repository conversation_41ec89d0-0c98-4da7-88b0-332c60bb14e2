part of '../../shared_widgets.dart';

class CategoriesDropDown extends StatelessWidget {
  final ValueNotifier<CategoryModel?>? selectedCat;
  final ValueNotifier<List<CategoryModel>?>? selectedMultiCategories;
  final List<CategoryModel>? categoriesList;
  final Function(dynamic)? additionalOnChanged;
  final String? documentId;
  final List<String> documentIds;
  final String? label;
  final bool isMulti;

  const CategoriesDropDown({
    super.key,
    this.selectedCat,
    this.selectedMultiCategories,
    this.categoriesList,
    this.additionalOnChanged,
    this.documentId,
    this.label,
    this.documentIds = const [],
    this.isMulti = false,
  });

  @override
  Widget build(BuildContext context) {
    bool isNullOrBlank(String? text) {
      return text == null || text.trim().isEmpty;
    }

    void setCategoriesWithId(List<CategoryModel> categories) {
      if (documentId != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          selectedCat?.value = categories.firstWhere(
            (element) => element.documentId == documentId,
          );
        });
      }

      if (documentIds.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          selectedMultiCategories?.value = categories
              .where((element) => documentIds.contains(element.documentId))
              .toList();
        });
      }
    }

    Widget categoriesDropDown(List<CategoryModel> categories) {
      return HookBuilder(builder: (context) {
        useEffect(() {
          setCategoriesWithId(categories);
          return () {};
        }, []);

        if (isMulti) {
          return BaseSearchSheet(
            isMultiSelect: true,
            onChanged: (value) {
              selectedMultiCategories?.value = List<CategoryModel>.from(value);
              if (additionalOnChanged != null) {
                additionalOnChanged!(value);
              }
            },
            data: categories,
            itemModelAsName: (data) {
              final category = data as CategoryModel;
              return context.readIsEng
                  ? (isNullOrBlank(category.englishName)
                      ? category.arabicName
                      : category.englishName)
                  : (isNullOrBlank(category.arabicName)
                      ? category.englishName
                      : category.arabicName);
            },
            multiItemsAsName: (data) {
              if (selectedMultiCategories?.value == null) return '';
              return selectedMultiCategories!.value!
                  .map((e) => context.readIsEng
                      ? (isNullOrBlank(e.englishName)
                          ? e.arabicName
                          : e.englishName)
                      : (isNullOrBlank(e.arabicName)
                          ? e.englishName
                          : e.arabicName))
                  .toList()
                  .toString()
                  .filterMultiDropDownList;
            },
            label: label ?? context.tr.categories,
            selectedValue: selectedMultiCategories?.value,
          );
        }

        return BaseSearchDropDown(
          onChanged: (value) {
            selectedCat?.value = value;
            if (additionalOnChanged != null) {
              additionalOnChanged!(value);
            }
          },
          data: categories,
          itemModelAsName: (data) {
            final category = data as CategoryModel;
            return context.readIsEng
                ? (isNullOrBlank(category.englishName)
                    ? category.arabicName
                    : category.englishName)
                : (isNullOrBlank(category.arabicName)
                    ? category.englishName
                    : category.arabicName);
          },
          label: label ?? context.tr.categories,
          selectedValue: selectedCat?.value,
        );
      });
    }

    if (categoriesList != null) {
      return categoriesDropDown(categoriesList!);
    }

    return Consumer<CategoryVM>(
      builder: (context, categoryVM, child) {
        if (categoryVM.isLoading) {
          return const Center(child: LoadingWidget());
        }
        final categories = categoryVM.categories;
        return categoriesDropDown(categories);
      },
    );
  }
}
