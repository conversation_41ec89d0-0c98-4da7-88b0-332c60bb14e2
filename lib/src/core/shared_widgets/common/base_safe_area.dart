import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class BaseSafeArea extends StatelessWidget {
  final Widget child;
  final bool withSpaceForIOS;

  const BaseSafeArea(
      {super.key, required this.child, this.withSpaceForIOS = false});

  @override
  Widget build(BuildContext context) {
    final haveSpace =
        kIsWeb || (!Platform.isIOS && !withSpaceForIOS) ? false : true;

    return SafeArea(
      top: haveSpace,
      bottom: haveSpace,
      child: child,
    );
  }
}
