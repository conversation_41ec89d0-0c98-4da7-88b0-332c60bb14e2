part of shared_widgets;

class BaseTopSection extends StatelessWidget {
  final String title;
  final bool isBackButton;
  final Function()? onBack;

  const BaseTopSection(
      {super.key, required this.title, this.isBackButton = true, this.onBack});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: context.isEng ? Alignment.topLeft : Alignment.topRight,
      children: [
        SizedBox(
          height: 150.h,
          child: HeaderWidget(
            height: 150.h,
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            context.largeGap,

            if (kIsWeb || Platform.isIOS) context.mediumGap,

            //! Back Button
            if (isBackButton)
              BackButtonWidget(onPressed: () {
                if (onBack != null) {
                  onBack!();
                } else {
                  context.back();
                }
                context.read<MediaVM>().clearFiles();
              }),

            context.mediumGap,

            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.mediumPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: context.whiteHeadLine,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
