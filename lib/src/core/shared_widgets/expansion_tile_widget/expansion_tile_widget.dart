import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

import '../../resources/theme/theme.dart';

class ExpansionTileWidget extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const ExpansionTileWidget({
    super.key,
    required this.children,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
        iconColor: ColorManager.primaryColor,
        tilePadding: const EdgeInsets.symmetric(horizontal: 20),
        title: Text(
          title,
          style: context.title.copyWith(color: ColorManager.primaryColor),
        ),
        initiallyExpanded: true,
        children: children);
  }
}
