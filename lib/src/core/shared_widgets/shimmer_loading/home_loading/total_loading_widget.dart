import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

import '../../../resources/app_radius.dart';
import '../../../resources/theme/theme.dart';
import '../main_shimmer_loading.dart';

class TotalLoadingWidget extends StatelessWidget {
  const TotalLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MainShimmerLoading(
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 80.h,
              decoration: BoxDecoration(
                  color: ColorManager.shimmerBaseColor,
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius)),
            ),
          ),
          context.mediumGap,
          Expanded(
            child: Container(
              height: 80.h,
              decoration: BoxDecoration(
                  color: ColorManager.shimmerBaseColor,
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius)),
            ),
          ),
        ],
      ),
    );
  }
}
