import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:shimmer/shimmer.dart';

import '../../resources/theme/theme.dart';

class MainShimmerLoading extends StatelessWidget {
  final Widget child;
  const MainShimmerLoading({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        direction: ShimmerDirection.ltr,
        highlightColor:
            context.isDark ? ColorManager.grey : ColorManager.highlightColor,
        baseColor: context.isDark
            ? ColorManager.grey.withOpacity(0.2)
            : ColorManager.shimmerBaseColor,
        child: child);
  }
}
