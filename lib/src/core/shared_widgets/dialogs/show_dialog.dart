import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/alert_dialog.dart';

Future<void> showAlertDialog(BuildContext context,
    {required Widget child,
    String? header,
    bool isWarningMessage = true,
    required Function() onConfirm,
    bool isLoading = false}) async {
  return await showAdaptiveDialog(
      context: context,
      builder: (context) {
        return AlertDialogWidget(
          header: header,
          isWarningMessage: isWarningMessage,
          onConfirm: onConfirm,
          isLoading: isLoading,
          child: child,
        );
      });
}
