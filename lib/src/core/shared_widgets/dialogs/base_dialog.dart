import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';

class BaseDialog extends StatefulWidget {
  final Widget child;
  final double radius;
  final bool isLoading;
  final Color? backgroundColor;
  final bool withCloseButton;
  final bool isExpanded;

  const BaseDialog({
    super.key,
    required this.child,
    this.isLoading = false,
    this.radius = AppRadius.tabBarRadius,
    this.backgroundColor,
    this.withCloseButton = false,
    this.isExpanded = false,
  });

  @override
  State<StatefulWidget> createState() => _MainDialog();
}

class _MainDialog extends State<BaseDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation = CurvedAnimation(
        parent: controller, curve: Curves.fastEaseInToSlowEaseOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }
    return Center(
      child: Material(
        color: Colors.transparent,
        child: ScaleTransition(
          scale: scaleAnimation,
          child: Container(
              margin: const EdgeInsets.all(AppSpaces.mediumPadding),
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: ShapeDecoration(
                  color: widget.backgroundColor ?? context.appTheme.cardColor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.radius))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  //! Close Icon
                  if (widget.withCloseButton)
                    CircleAvatar(
                      backgroundColor: context.isDark
                          ? ColorManager.darkGrey
                          : ColorManager.grey,
                      child: IconButton(
                          onPressed: () => context.back(),
                          icon: Icon(
                            Icons.close,
                            color: context.isDark ? Colors.white : Colors.black,
                          )),
                    ),
                  if (widget.isExpanded)
                    Expanded(child: widget.child)
                  else
                    widget.child,
                ],
              )),
        ),
      ),
    );
  }
}
