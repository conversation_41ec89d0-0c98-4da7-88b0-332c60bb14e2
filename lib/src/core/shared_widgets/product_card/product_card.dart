import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/show_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../generated/assets.dart';
import '../../../screens/categories/models/category_model.dart';
import '../../../screens/products/models/products_model.dart';
import '../../../screens/products/view/add_product/add_product.dart';
import '../../../screens/products/view/product_details/product_details_screen.dart';
import '../../resources/app_radius.dart';
import '../../resources/theme/theme.dart';
import '../shared_widgets.dart';

class ProductCard extends HookWidget {
  final ProductModel productModel;
  final CategoryModel cat;

  const ProductCard({
    super.key,
    required this.productModel,
    required this.cat,
  });

  @override
  Widget build(BuildContext context) {
    final isSale = productModel.isSale;

    final isActive = useState(productModel.isActive);

    final productVM = context.read<ProductVM>();

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        color: Theme.of(context).cardColor,
      ),
      child: Stack(
        children: [
          //! image & details
          InkWell(
            onTap: () {
              context.to(ProductsDetailsScreen(
                categoryModel: cat,
                productModel: productModel,
              ));
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Product image
                SizedBox(
                  height: 170.h,
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(
                          AppRadius.baseContainerRadius,
                        ),
                        child: Image.network(
                          productModel.thumbnail?.url ?? '',
                          width: double.infinity,
                          fit: BoxFit.cover,
                          height: 170.h,
                        ),
                      ),

                      //! Delete button
                      Positioned(
                        bottom: 0,
                        right: context.isEng ? 0 : null,
                        left: context.isEng ? null : 0,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: context.isEng
                              ? const BoxDecoration(
                                  color: ColorManager.red,
                                  borderRadius: BorderRadius.only(
                                    bottomRight: Radius.circular(
                                        AppRadius.baseContainerRadius),
                                    topLeft: Radius.circular(
                                        AppRadius.baseContainerRadius),
                                  ))
                              : const BoxDecoration(
                                  color: ColorManager.red,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(
                                        AppRadius.baseContainerRadius),
                                    topRight: Radius.circular(
                                        AppRadius.baseContainerRadius),
                                  )),
                          child: IconButton(
                              onPressed: () => showAlertDialog(context,
                                  child: Text(
                                      context
                                          .tr.deleteProductConfirmationMessage,
                                      style: context.labelLarge),
                                  onConfirm: () => productVM.deleteProduct(
                                      context,
                                      id: productModel.documentId!,
                                      cat: cat)),
                              icon: const BaseLottieWidget.icon(
                                Assets.animatedDelete,
                                height: AppSpaces.iconSize,
                              )),
                        ),
                      ),
                    ],
                  ),
                ),

                context.smallGap,

                //! product title & price & sale price
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productVM.getProductTitle(
                              product: productModel, context: context) ??
                          '',
                      style: context.labelLarge.copyWith(
                          overflow: TextOverflow.ellipsis,
                          fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    context.xSmallGap,

                    //! price and sale price
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (isSale) ...[
                            Text(
                              productModel.salePrice.toCurrency(context),
                              style: context.title.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            context.smallGap,
                          ],
                          Text(
                            productModel.totalPrice.toCurrency(context),
                            style: isSale
                                ? context.greyLabelMedium.copyWith(
                                    fontWeight: isSale ? null : FontWeight.bold,
                                    decoration: isSale
                                        ? TextDecoration.lineThrough
                                        : null,
                                    decorationColor: context.isDark
                                        ? ColorManager.grey.withOpacity(0.7)
                                        : ColorManager.darkGrey
                                            .withOpacity(0.5),
                                  )
                                : context.title.copyWith(
                                    fontWeight: isSale ? null : FontWeight.bold,
                                    decoration: isSale
                                        ? TextDecoration.lineThrough
                                        : null,
                                  ),
                          ),
                        ],
                      ),
                    ).sized(
                      width: context.width * 0.5,
                    ),

                    context.smallGap,
                  ],
                ).paddingOnly(
                    left: AppSpaces.smallPadding,
                    right: AppSpaces.smallPadding),
              ],
            ),
          ),

          // //! Is Active
          Positioned(
              top: 0,
              left: 7,
              child: SwitchButtonWidget(
                value: isActive,
                onChanged: (value) async {
                  await productVM.editStatusProduct(
                    context,
                    product: productModel,
                    status: value,
                  );

                  isActive.value = value;
                },
              )),

          //! Edit button
          Align(
            alignment: Alignment.topRight,
            child: CircleAvatar(
              backgroundColor: Colors.transparent,
              radius: 17,
              child: IconButton(
                  onPressed: () {
                    final product = productModel.copyWith(
                      isActive: isActive.value,
                    );
                    context.to(AddProductScreen(
                      product: product,
                      category: cat,
                    ));
                  },
                  icon: const BaseLottieWidget.icon(
                    Assets.animatedEdit,
                    height: AppSpaces.iconSize,
                  )),
            ).frosted(
              blur: 0.9,
              frostColor: Colors.blueGrey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
          ).paddingOnly(
            right: AppSpaces.smallPadding,
            top: AppSpaces.smallPadding,
          ),
        ],
      ),
    );
  }
}
