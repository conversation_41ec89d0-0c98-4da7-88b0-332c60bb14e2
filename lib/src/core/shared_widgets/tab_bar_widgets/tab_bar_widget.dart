import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:provider/provider.dart';

import '../../../screens/home/<USER>/home_tab_bar_provider.dart';
import '../../resources/app_radius.dart';
import '../../resources/theme/theme.dart';

class TapBarWidget extends StatelessWidget {
  final List<String> tabs;
  final int initialIndex;
  final Function(int)? onTab;

  const TapBarWidget({
    super.key,
    required this.tabs,
    required this.initialIndex,
    required this.onTab,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeTabBarVM>(
      builder: (context, homeTabBarVM, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DefaultTabController(
              initialIndex: initialIndex,
              length: tabs.length,
              child: TabBar(
                physics: const BouncingScrollPhysics(),
                overlayColor: MaterialStateProperty.all(Colors.transparent),
                splashBorderRadius:
                    BorderRadius.circular(AppRadius.tabBarRadius),
                tabAlignment: TabAlignment.start,
                unselectedLabelStyle: context.labelLarge,
                onTap: onTab,
                indicatorColor: Colors.transparent,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                unselectedLabelColor: context.isDark
                    ? ColorManager.grey.withOpacity(0.5)
                    : ColorManager.secondaryColor,
                labelPadding: const EdgeInsets.all(8),
                labelColor: ColorManager.white,
                labelStyle:
                    context.subTitle.copyWith(fontWeight: FontWeight.w400),
                isScrollable: true,
                tabs: tabs.indexed.map((e) {
                  final index = e.$1;
                  return WidgetAnimator(
                    delay: Duration(milliseconds: 300 * index),
                    child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        decoration: BoxDecoration(
                            color: homeTabBarVM.currentIndex == index
                                ? ColorManager.primaryColor
                                : ColorManager.textFieldColor(context),
                            borderRadius:
                                BorderRadius.circular(AppRadius.baseRadius)),
                        child: Text(
                          getOrderStatusTranslation(
                            context,
                            status: e.$2,
                          ),
                          style: context.labelLarge.copyWith(
                              color: homeTabBarVM.currentIndex == index
                                  ? ColorManager.white
                                  : null),
                        )),
                  );
                }).toList(),
              ),
            ),
          ],
        );
      },
    );
  }
}
