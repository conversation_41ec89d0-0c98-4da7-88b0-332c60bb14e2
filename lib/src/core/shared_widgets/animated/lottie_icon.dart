part of shared_widgets;

//! Base Lottie Icon ================================================
class BaseLottieWidget extends StatelessWidget {
  final String asset;
  final double? height;
  final double? width;
  final bool repeat;
  final BoxFit fit;

  const BaseLottieWidget(this.asset,
      {this.height, this.width, this.repeat = false,
        this.fit = BoxFit.contain, super.key});

  const BaseLottieWidget.icon(this.asset,
      {this.height = 45, this.width = 45, this.repeat = false,this.fit = BoxFit.contain, super.key});

  @override
  Widget build(BuildContext context) {
    return Lottie.asset(
      asset,
      height: height,
      width: width,
      fit: fit,
      repeat: repeat,
      frameRate: FrameRate.max,
    );
  }
}

// class BaseLottieIcon extends StatelessWidget {
//   final String asset;
//   final double height;
//   final double width;
//   final bool repeat;
//
//   const BaseLottieWidget.icon(this.asset,
//       {this.height = 45, this.width = 45, this.repeat = false, super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BaseLottieWidget(
//       asset,
//       height: height,
//       width: width,
//       repeat: repeat,
//     );
//   }
// }

//! For Fields ================================================
class BaseLottieFieldIcon extends StatelessWidget {
  final String asset;
  final double height;

  const BaseLottieFieldIcon(this.asset, {super.key, this.height = 35});

  @override
  Widget build(BuildContext context) {
    if (context.isDark) {
      return Container(
        width: 35.r,
        height: 35.r,
        margin: const EdgeInsets.symmetric(vertical: AppSpaces.smallPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.baseRadius),
          color: Colors.blueGrey.shade50,
        ),
        child: BaseLottieWidget.icon(
          asset,
          height: height,
          repeat: false,
        ),
      );
    }
    return BaseLottieWidget.icon(
      asset,
      height: height,
      repeat: false,
    );
  }
}
