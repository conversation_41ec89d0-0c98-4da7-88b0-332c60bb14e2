part of shared_widgets;

class BaseDropDown<T> extends StatelessWidget {
  final dynamic value;
  final String hint;
  final List<DropdownMenuItem<T>> data;
  final void Function(dynamic)? onChanged;
  final bool isExpanded;
  final EdgeInsets hintPadding;

  const BaseDropDown({
    super.key,
    required this.onChanged,
    this.hint = '',
    required this.data,
    required this.value,
    this.hintPadding = EdgeInsets.zero,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButton<T>(
      hint: Padding(
        padding: hintPadding,
        child: Text(
          hint,
          style: context.hint,
        ),
      ),
      borderRadius: BorderRadius.circular(
        AppRadius.imageContainerRadius,
      ),
      iconEnabledColor: ColorManager.primaryColor,
      isExpanded: isExpanded,
      dropdownColor: context.isDark
          ? ColorManager.darkBackgroundColor
          : ColorManager.backgroundColor,
      value: value,
      icon: const Icon(
        Icons.keyboard_arrow_down,
        color: ColorManager.primaryColor,
      ),
      underline: const Center(),
      onChanged: onChanged,
      items: data,
    );
  }
}
