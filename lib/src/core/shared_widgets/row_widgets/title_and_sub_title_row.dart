import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';

import '../../resources/theme/theme.dart';

class IconAndTitleAndSubTitleRow extends StatelessWidget {
  final (String, String) titleAndSubTitle; //? title , price
  final bool isTotal;
  final bool isNumber;

  final String? iconPath;
  final bool isIcon;

  const IconAndTitleAndSubTitleRow(
      {super.key,
      required this.titleAndSubTitle,
      this.isTotal = false,
      this.isNumber = true,
      this.isIcon = false,
      this.iconPath});

  @override
  Widget build(BuildContext context) {
    final title = titleAndSubTitle.$1;
    final subTitle = titleAndSubTitle.$2;

    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              if (isIcon) ...[
                IconWidget(
                    color: ColorManager.iconColor.withOpacity(0.5),
                    icon: iconPath!),
                context.smallGap,
              ],
              Text(
                title,
                style: isTotal
                    ? context.title.copyWith(
                        color: isTotal ? ColorManager.primaryColor : null)
                    : context.labelLarge,
              ),
            ],
          ),
        ),
        if (isNumber) ...[
          Text(
            subTitle,
            style: context.labelLarge.copyWith(
                color: isTotal ? ColorManager.primaryColor : null,
                fontSize: isTotal ? 18 : null),
          ),
        ] else ...[
          Text(
            subTitle,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: context.greyLabelLarge
                .copyWith(color: isTotal ? ColorManager.primaryColor : null),
          ),
        ]
      ],
    );
  }
}
