part of shared_widgets;

class SinglePickImageWidget extends StatelessWidget {
  final String? pickedResult;
  final String? networkImage;
  final String? title;
  final double? width;

  const SinglePickImageWidget({
    super.key,
    this.networkImage,
    this.pickedResult,
    this.title,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final showPickImageButton = pickedResult == null && networkImage == null;

    //! Pick Image Button ========================================
    if (showPickImageButton) {
      return _PickImageButton(
        title: title,
        width: width,
      );
    }

    final pickResultIsNotNull = pickedResult != null;
    // pickedResult?.files != null && pickedResult!.files.isNotEmpty;

    //! View Picked Image ========================================
    if (pickResultIsNotNull) {
      return _ViewPickedImage(
        pickedResult: pickedResult!,
      );
    }

    //! View Network Image ========================================
    return _ViewNetworkImage(
      viewedNetworkImage: networkImage!,
    );
  }
}
