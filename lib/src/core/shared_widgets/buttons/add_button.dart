part of '../shared_widgets.dart';

class AddButton extends StatelessWidget {
  final String label;
  final Widget? icon;
  final bool haveElevation;
  final void Function()? onPressed;
  final Color? color;
  final bool isPrefixIcon;
  final bool isOutLine;
  final bool isWhiteText;
  final bool isBold;
  final bool enabled;

  const AddButton({
    super.key,
    required this.label,
    this.haveElevation = true,
    required this.onPressed,
    this.icon,
    this.isPrefixIcon = false,
    this.isOutLine = false,
    this.isWhiteText = true,
    this.color = ColorManager.primaryColor,
    this.isBold = true,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
          padding: WidgetStatePropertyAll(EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding + 2,
            vertical: AppSpaces.smallPadding + 2,
          )),
          backgroundColor: WidgetStatePropertyAll(ColorManager.primaryColor),
          elevation: WidgetStatePropertyAll(0)),
      onPressed: onPressed,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            size: 20,
            Icons.add,
            color: ColorManager.white,
          ),
          context.smallGap,
          Text(label, style: context.whiteSubTitle)
        ],
      ),
    );
  }
}
