import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:rounded_loading_button/rounded_loading_button.dart';

class LoadingButton extends StatelessWidget {
  final RoundedLoadingButtonController roundedLoadingButtonController;
  final String label;
  final Function() onTap;
  final bool isBold;
  final bool isWhiteText;

  const LoadingButton(
      {super.key,
      required this.roundedLoadingButtonController,
      required this.onTap,
      this.isBold = true,
      this.isWhiteText = true,
      required this.label});

  // void _doSomething(RoundedLoadingButtonController controller) async {
  //   Timer(const Duration(seconds: 10), () {
  //     controller.start();
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return RoundedLoadingButton(
      width: context.width * .9,
      color: ColorManager.primaryColor,
      failedIcon: Icons.close,
      controller: roundedLoadingButtonController,
      onPressed: onTap,
      child: Text(
        label,
        style: isWhiteText
            ? context.whiteSubTitle.copyWith(
                fontWeight: isBold ? FontWeight.bold : null,
              )
            : context.subTitle.copyWith(
                color: Colors.black,
                fontWeight: isBold ? FontWeight.bold : null,
              ),
      ),
    );
  }
}
