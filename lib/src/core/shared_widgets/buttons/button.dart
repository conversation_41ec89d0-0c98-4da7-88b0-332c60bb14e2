part of '../shared_widgets.dart';

class <PERSON>ton extends StatelessWidget {
  final String label;
  final Widget? icon;
  final bool haveElevation;
  final void Function()? onPressed;
  final Color? color;
  final bool isPrefixIcon;
  final bool isOutLine;
  final bool isWhiteText;
  final bool isBold;
  final bool enabled;
  final bool isLoading;
  final double radius;
  final double? fontSize;

  const Button({
    super.key,
    required this.label,
    this.haveElevation = true,
    required this.onPressed,
    this.icon,
    this.isPrefixIcon = false,
    this.isLoading = false,
    this.isOutLine = false,
    this.isWhiteText = true,
    this.color = ColorManager.primaryColor,
    this.isBold = true,
    this.enabled = true,
    this.fontSize,
    this.radius = AppRadius.extraLargeContainerRadius,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 55,
      child: isLoading
          ? const LoadingWidget()
          : ElevatedButton(
              onPressed: enabled ? onPressed : null,
              style: ElevatedButton.styleFrom(
                elevation: haveElevation && !isOutLine ? 3 : 0,
                foregroundColor:
                    isOutLine ? color!.withOpacity(.1) : Colors.white,
                backgroundColor: isOutLine ? Colors.transparent : color,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(radius),
                ),
                side: isOutLine
                    ? BorderSide(
                        color: color!,
                        width: 1,
                      )
                    : BorderSide.none,
              ),
              child: _buildChild(context)),
    );
  }

  Widget _buildChild(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isPrefixIcon && icon != null)
          Row(
            children: [
              icon!,
            ],
          ),
        Flexible(
          child: Center(
            child: isLoading
                ? const LoadingWidget()
                : FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      label,
                      maxLines: 1,
                      style: isWhiteText
                          ? context.whiteSubTitle.copyWith(
                              color: isOutLine ? color : null,
                              fontWeight:
                                  isBold ? FontWeight.bold : FontWeight.w400,
                              fontSize: fontSize)
                          : context.subTitle.copyWith(
                              color: Colors.black,
                              fontSize: fontSize,
                              fontWeight:
                                  isBold ? FontWeight.bold : FontWeight.w400,
                            ),
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
