import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';

double _previousScrollPosition = 0;

class BasePaginationWidget extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Function() onScrollEnd;
  final Function()? onRefresh;

  const BasePaginationWidget(
      {super.key,
      required this.child,
      required this.onScrollEnd,
      this.onRefresh,
      required this.isLoading});

  @override
  Widget build(BuildContext context) {
    if (onRefresh != null) {
      return NotificationListener(
        onNotification: (ScrollNotification scrollInfo) {
          if (scrollInfo.metrics.axis == Axis.horizontal) {
            return true;
          }

          if (!isLoading &&
              scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
              scrollInfo.metrics.pixels > _previousScrollPosition) {
            onScrollEnd();
          }

          _previousScrollPosition = scrollInfo.metrics.pixels;
          return true;
        },
        child: RefreshIndicatorWidget(
          onRefresh:() {
            onRefresh!();
            return Future.value();
          },
          child: child,
        ),
      );
    }
    return NotificationListener(
      onNotification: (ScrollNotification scrollInfo) {
        if (!isLoading &&
            scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            scrollInfo.metrics.pixels > _previousScrollPosition) {
          onScrollEnd();
        }

        _previousScrollPosition = scrollInfo.metrics.pixels;
        return true;
      },
      child: child,
    );
  }
}
