import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/filter/date_filter.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:provider/provider.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../../screens/orders/view_model/order_view_model.dart';
import '../../resources/app_spaces.dart' show AppSpaces;

class OrdersFilterWidget extends HookWidget {
  const OrdersFilterWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final dateFilterCtrl = useTextEditingController();

    return Consumer<OrderVM>(
      builder: (context, orderVM, child) {
        return HookBuilder(builder: (context) {
          final searchController = useTextEditingController();

          return Padding(
            padding: const EdgeInsets.only(
              right: AppSpaces.mediumPadding,
              left: AppSpaces.mediumPadding,
              top: AppSpaces.largePadding,
            ),
            child: Column(
              children: [
                // * Date Filter
                DateFilter(
                    dateFilterCtrl: dateFilterCtrl,
                    startDate: orderVM.orderFilter.startDate,
                    endDate: orderVM.orderFilter.endDate,
                    onApplyClick: (start, end) {
                      final isSameDay = start.isSameDay(end);

                      if (isSameDay) {
                        dateFilterCtrl.text = start.formatDateToString;
                        final endDate =
                            end.copyWith(hour: 23, minute: 59, second: 59);

                        orderVM.setOrderFilter(context,
                            filter: orderVM.orderFilter.copyWith(
                              startDate: start,
                              endDate: endDate,
                            ));
                      } else {
                        dateFilterCtrl.text =
                            '${start.formatDateToString} - ${end.formatDateToString}';

                        orderVM.setOrderFilter(context,
                            filter: orderVM.orderFilter.copyWith(
                              startDate: start,
                              endDate: end,
                            ));
                      }
                    }),

                context.mediumGap,

                // * Search Filter
                Row(
                  children: [
                    Expanded(
                        flex: 2,
                        child: BaseTextField(
                          controller: searchController,
                          hint: context.tr.search,
                          withoutEnter: true,
                          suffixIcon: _SearchByWidget(
                            orderVM: orderVM,
                            searchController: searchController,
                          ),
                          onlyAcceptNumbers: orderVM.orderFilter.searchBy ==
                                  OrdersSearchBy.invoiceId ||
                              orderVM.orderFilter.searchBy ==
                                  OrdersSearchBy.orderId,
                          textInputType: () {
                            if (UniversalPlatform.isIOS) {
                              return TextInputType.text;
                            }
                            switch (orderVM.orderFilter.searchBy) {
                              case OrdersSearchBy.invoiceId:
                              case OrdersSearchBy.orderId:
                                return TextInputType.number;
                              case OrdersSearchBy.userName:
                                return TextInputType.text;
                              default:
                                return TextInputType.phone;
                            }
                          }(),
                          icon: const Icon(
                            Icons.search,
                          ),
                          onSubmitted: (value) {
                            if (value.isNotEmpty) {
                              orderVM.setOrderFilter(context,
                                  filter: orderVM.orderFilter.copyWith(
                                    searchValue: value,
                                  ));
                            }
                          },
                        )),
                  ],
                ),

                // * Clear Filter
                if (orderVM.orderFilter.isFilterApplied) ...[
                  context.mediumGap,
                  Align(
                    alignment: Alignment.centerRight,
                    child: InkWell(
                      onTap: () {
                        final orderStatus = orderVM.orderFilter.orderStatus;

                        orderVM.clearFilter();

                        searchController.clear();
                        dateFilterCtrl.clear();

                        orderVM.setOrderFilter(context,
                            filter: orderVM.orderFilter.copyWith(
                              orderStatus: orderStatus,
                            ));
                      },
                      child: Text(
                        context.tr.clearFilter,
                        style: context.labelMedium.copyWith(
                          color: context.appTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ]
              ],
            ),
          );
        });
      },
    );
  }
}

class _SearchByWidget extends StatelessWidget {
  final OrderVM orderVM;
  final TextEditingController searchController;

  const _SearchByWidget(
      {required this.orderVM, required this.searchController});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 40,
          width: .5,
          color: context.isDark
              ? ColorManager.grey.withOpacity(0.5)
              : ColorManager.black.withOpacity(0.5),
        ),
        context.mediumGap,
        BaseDropDown(
          value: orderVM.orderFilter.searchBy,
          onChanged: (value) {
            final startDate = orderVM.orderFilter.startDate;
            final endDate = orderVM.orderFilter.endDate;

            orderVM.clearFilter();

            orderVM.setOrderFilter(context,
                filter: orderVM.orderFilter.copyWith(
                  searchBy: value,
                  startDate: startDate,
                  endDate: endDate,
                ),
                fetchOrders: searchController.text.isNotEmpty);

            searchController.clear();
          },
          data: OrdersSearchBy.values
              .where((e) => e != OrdersSearchBy.invoiceId)
              .map((e) {
            return DropdownMenuItem(
                value: e,
                child: Text(
                  e.toSearchValueName(context),
                  style: context.labelMedium,
                ));
          }).toList(),
          hint: context.tr.searchBy,
        ),
        context.mediumGap,
      ],
    );
  }
}
