import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/filter/custom_date_range_picker.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';

class DateFilter extends HookWidget {
  final TextEditingController dateFilterCtrl;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime startDate, DateTime endDate) onApplyClick;
  final bool isDateRange;

  const DateFilter({
    super.key,
    required this.dateFilterCtrl,
    required this.startDate,
    required this.endDate,
    required this.onApplyClick,
    this.isDateRange = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showCustomDateRangePicker(
          context,
          dismissible: true,
          isDateRange: isDateRange,
          minimumDate: DateTime(2023, 1, 1),
          maximumDate: DateTime.now(),
          startDate: startDate,
          endDate: endDate,
          backgroundColor: context.appTheme.cardColor,
          primaryColor: Colors.green,
          onApplyClick: onApplyClick,
          onCancelClick: () {},
        );
      },
      child: BaseTextField(
        controller: dateFilterCtrl,
        enabled: false,
        icon: const Icon(
          CupertinoIcons.calendar_today,
        ),
        hint: context.tr.filterByDate,
        withoutEnter: true,
      ),
    );
  }
}
