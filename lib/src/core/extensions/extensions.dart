library extensions;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:gap/gap.dart';
import 'package:idea2app_vendor_app/src/core/data/local/local_keys.dart';
import 'package:idea2app_vendor_app/src/core/data/local/shared_preferences/get_storage.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/utils/flush_bar.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/main_screen.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../generated/l10n.dart';
import '../resources/app_spaces.dart';
import '../resources/theme/theme.dart';

part 'color_extensions.dart';
part 'context_extensions.dart';
part 'date_extensions.dart';
part 'num_extensions.dart';
part 'string_extensions.dart';
part 'widget_extensions.dart';
