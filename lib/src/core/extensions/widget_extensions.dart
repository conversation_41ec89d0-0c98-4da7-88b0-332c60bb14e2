part of 'extensions.dart';

extension WidgetExtenstions on Widget {
  Widget get leftAnimate => animate()
      .fadeIn(duration: 900.ms, delay: 500.ms)
      .move(begin: const Offset(0, 0), curve: Curves.slowMiddle);

  Widget shimmer({
    Color color = ColorManager.lightBlue,
  }) {
    if(kIsWeb){
      return this;
    }
    return animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 3000.ms, color: color
            // Color(0xFF78F4FF)
            )
        .animate() // this wraps the previous Animate in another Animate
        .fadeIn(duration: 1200.ms, curve: Curves.easeOutQuad)
        .slide();
  }

  //? Padding -----------------------------------------
  Widget paddingAll(double value) {
    return Padding(
      padding: EdgeInsets.all(value),
      child: this,
    );
  }

  Widget paddingSymmetric({double vertical = 0, double horizontal = 0}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal),
      child: this,
    );
  }

  Widget paddingOnly(
      {double top = 0, double bottom = 0, double left = 0, double right = 0}) {
    return Padding(
      padding:
          EdgeInsets.only(top: top, bottom: bottom, left: left, right: right),
      child: this,
    );
  }

  //? Align -----------------------------------------
  Widget align([AlignmentGeometry alignment = Alignment.center]) {
    return Align(
      alignment: alignment,
      child: this,
    );
  }

  //? Center -----------------------------------------
  Widget center() {
    return Center(
      child: this,
    );
  }

  //? SizedBox -----------------------------------------
  Widget sized({double? width, double? height}) {
    return SizedBox(
      width: width,
      height: height,
      child: this,
    );
  }

  //? Expanded -----------------------------------------
  Widget expanded({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: this,
    );
  }

  //? Flexible -----------------------------------------
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: this,
    );
  }

  //? Container
  Widget container({
    Key? key,
    AlignmentGeometry? alignment,
    EdgeInsetsGeometry? padding,
    Color? color,
    Decoration? decoration,
    Decoration? foregroundDecoration,
    double? width,
    double? height,
    BoxConstraints? constraints,
    EdgeInsetsGeometry? margin,
    Clip clipBehavior = Clip.none,
  }) {
    return Container(
      key: key,
      alignment: alignment,
      padding: padding,
      color: color,
      decoration: decoration,
      foregroundDecoration: foregroundDecoration,
      width: width,
      height: height,
      constraints: constraints,
      margin: margin,
      clipBehavior: clipBehavior,
      child: this,
    );
  }

  //? Container
  Widget decorated({
    Color? color,
    BorderRadius? borderRadius,
    AlignmentGeometry? alignment,
    double? height,
    double? width,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    BoxBorder? border,
    List<BoxShadow>? shadow,
    BoxShape shape = BoxShape.rectangle,
  }) {
    return Container(
      height: height,
      padding: padding,
      margin: margin,
      width: width,
      alignment: alignment,
      decoration: BoxDecoration(
          color: color,
          shape: shape,
          borderRadius: borderRadius,
          boxShadow: shadow,
          border: border),
      child: this,
    );
  }

  //? ScrollView
  Widget scroll() {
    return SingleChildScrollView(
      child: this,
    );
  }

  //? OnTap
  Widget onTap(Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: this,
    );
  }

  //? OnTap with ripple effect
  Widget onTapWithRipple(
    Function() onTap, {
    double radius = AppRadius.baseRadius,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.all(Radius.circular(radius)),
      child: this,
    );
  }
}

extension TextWidgets on String {
  //? Label Large
  Widget labelLarge(BuildContext context) {
    return Text(
      this,
      style: context.labelLarge,
    );
  }
}

extension OnWillPopScope on Widget {
  Widget onWillPopMainScreen(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return true;
      },
      child: this,
    );
  }
}
