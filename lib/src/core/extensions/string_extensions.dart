part of 'extensions.dart';

extension StringExtenstions on String? {
  String? get filterMultiDropDownList {
    if (this == null || this!.isEmpty) return null;
    return this?.replaceAll('[', '').replaceAll(']', '');
  }

  String get formatStringToDate {
    if (this == null || this!.isEmpty) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(DateTime.parse(this!));
  }

  int toInt() {
    return int.parse(this!);
  }

  int? toIntOrNull() {
    return int.tryParse(this ?? '0');
  }

  num? toNum() {
    return num.tryParse(this!);
  }

  num toNumOrZero() {
    return num.tryParse(this ?? '0') ?? 0;
  }

  //? Currency
  String toCurrency(BuildContext context) {
    final vendorVM = context.read<AuthVM>();

    final currentCurrency = vendorVM.currentCurrency(context);

    return '$currentCurrency ${this!.toNumOrZero().toStringAsFixed(2)}';
  }

  String currentCurrency(BuildContext context) {
    final vendorVM = context.read<AuthVM>();

    return vendorVM.currentCurrency(context);
  }

  String removeDecimalZeroFormat(num n) {
    return n.toStringAsFixed(n.truncateToDouble() == n ? 0 : 1);
  }

  String capitalize() {
    return "${this![0].toUpperCase()}${this!.substring(1)}";
  }

  String capitalizeFirstLetter() {
    if (this == null || this!.isEmpty) return '';
    return "${this![0].toUpperCase()}${this!.substring(1)}";
  }
}
