part of extensions;

Color getBackgroundColor(Color background) {
  return (background.computeLuminance() > 0.179) ? Colors.black : Colors.white;
}

extension ColorExtension on Color? {
  // String toHex() => '#${value.toRadixString(16).substring(2, 8)}';

  String? toHexWithoutHash() {
    if (this == null) {
      return null;
    }

    return '0xFF${this?.value.toRadixString(16).substring(2, 8)}';
  }
}
