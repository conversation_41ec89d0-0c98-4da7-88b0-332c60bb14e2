part of 'extensions.dart';

extension NumExtentions on num? {
  String removeDecimalZeroFormat(num n) {
    return n.toStringAsFixed(n.truncateToDouble() == n ? 0 : 1);
  }

  //? Currency
  String toCurrency(BuildContext context) {
    final vendorVM = context.read<AuthVM>();

    final currentCurrency = vendorVM.currentCurrency(context);

    final num = this?.removeDecimalZeroFormat(this ?? 0) ?? '0';

    if (context.isEng) {
      return '$currentCurrency$num';
    }

    return '$num$currentCurrency';
  }

  String formatNumber(BuildContext context, {bool withCurrency = true}) {
    final value = this ?? 0;
    if (value < 1000) {
      return withCurrency
          ? value.toString().toCurrency(context)
          : value.toString();
    } else if (value < 1000000) {
      return withCurrency
          ? '${(value / 1000).toStringAsFixed(1).toCurrency(context)}K'
          : '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return withCurrency
          ? '${(value / 1000000).toStringAsFixed(1).toCurrency(context)}M'
          : '${(value / 1000000).toStringAsFixed(1)}M';
    }
  }
}
