part of 'theme.dart';

class ColorManager {
  static Color selectedBgColor = backgroundColor;
  static const backgroundColor = Color(0xFFfafafa);
  static const darkBackgroundColor = Color(0xFF2F2F2F);
  static const primaryColor = Color(0xFF25D366);
  static const lightPrimaryColor = Color(0xFF1A8F44);
  static const accentColor = Color(0xff8ca898);
  static const secondaryColor = Color(0xFF89A798);
  // static const secondaryColor =Color(0xFFA2C7B3);
  static const Color shimmerHighlightColor = Color(0xFFE0E0E0);
  // Color(0XFF25534B);
  static const successColor = Color(0xFF2ECC71);

  // static const secondaryColor = Color(0XFFd8dde4);

  static const gradientBackground = [
    gradientColor,
    grey,
  ];
  static const gradientContainer = [
    primaryColor,
    lightPrimaryColor,
  ];
  static final activeGradientBackground = [
    Color(0xff25CA62),
    Color(0xff1C9A4A),
  ];

  static final expiredGradientBackground = [
    Color(0xffDF4244),
    Color(0xff931A1C),
  ];

  static const darkGradientBackground = [
    iconColor,
    darkGrey,
  ];
  static const highlightColor = Color(0xFFFFFFFF);
  static const shimmerBaseColor = grey;
  static const white = Color(0xFFFFFFFF);
  static const Color black = Color(0xff000000);
  static const Color lightBlack = Color(0xff1E1E1E);
  static const Color lightBlue = Color(0xff00fff7);

  static const Color lightGrey = Color(0x1cededed);

  static const grey = Color(0xFFEDEDED);
  // silver color
  static const Color silver = Color(0xffC0C0C0);
  static const blur = Color(0xFFF3F3F3);
  static const red = Color(0xFFFF0000);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFF363636);
  static const iconColor = Color(0xFF727272);
  static const gradientColor = Color(0xffccd3db);
  static const gradientColor2 = Color(0xffE7EBEE);
  static const errorColor = Color(0xFFFF0000);
  static const facebookColor = Color(0xFF4B6FBD);

  static textFieldColor(BuildContext context) {
    return context.isDark
        ? Colors.grey.withOpacity(.1)
        : Colors.grey.withOpacity(0.15);
  }
}
