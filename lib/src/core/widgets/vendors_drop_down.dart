import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view_model/vendors_view_model.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:provider/provider.dart';

class VendorDropDownWidget extends HookWidget {
  final ValueNotifier<VendorModel?> selectedVendor;
  final Function(VendorModel?)? onSelected;

  const VendorDropDownWidget({
    super.key,
    required this.selectedVendor,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    log('Selected Vendor: ${selectedVendor.value}');
    final isLoaded = useState(false);
    final vendors = useState<List<VendorModel>>([]);
    final isVendorLoading = useState(false);

    final vendorsList = context.watch<VendorsVM>().vendors;

    void getData() async {
      isVendorLoading.value = true;

      // if (vendorsList.isEmpty) {
      vendors.value = await context.read<VendorsVM>().getVendors(context);
      // } else {
      //   vendors.value = vendorsList;
      // }

      isVendorLoading.value = false;
      isLoaded.value = true;
    }

    void showSearchBottomSheet(BuildContext context) {
      final List<VendorModel> items = vendors.value;
      final ValueNotifier<String> searchQuery = ValueNotifier<String>('');

      showModalBottomSheet(
        backgroundColor: Colors.white,
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  onChanged: (value) => searchQuery.value = value,
                  decoration: InputDecoration(
                    hintText: context.tr.search,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ValueListenableBuilder<String>(
                    valueListenable: searchQuery,
                    builder: (context, query, child) {
                      final filteredItems = items
                          .where((item) => query.isEmpty
                              ? true
                              : (item.name ?? '')
                                      .toLowerCase()
                                      .contains(query.toLowerCase()) ||
                                  (item.businessName ?? '')
                                      .toLowerCase()
                                      .contains(query.toLowerCase()) ||
                                  (item.email ?? '').toLowerCase() ==
                                      query.toLowerCase() ||
                                  (item.phone ?? '')
                                      .toLowerCase()
                                      .contains(query.toLowerCase()))
                          .toList();

                      if (filteredItems.isEmpty) {
                        return Center(
                          child: Text(
                            context.tr.noResultFound,
                            style: const TextStyle(fontSize: 16),
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          return ListTile(
                            title: Row(
                              children: [
                                selectedVendor.value?.id == item.id
                                    ? const CircleAvatar(
                                        backgroundColor: Color(0xff233549),
                                        radius: 5,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(item.name ?? ''),
                                      Text(item.businessName ?? '',
                                          style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            onTap: () {
                              selectedVendor.value = item;

                              if (onSelected != null) {
                                onSelected!(item);
                              }

                              context.back();
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getData();
      });
      return () {};
    }, const []);

    if (!isLoaded.value) {
      return const LoadingWidget(
        isLinear: true,
      );
    }

    return GestureDetector(
      onTap: () => showSearchBottomSheet(context),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(width: 0.5, color: Colors.grey[300]!),
          color: Colors.white,
        ),
        height: 50,
        alignment: Alignment.centerLeft,
        child: Text(
          selectedVendor.value != null
              ? vendors.value
                      .firstWhereOrNull((v) => v.id == selectedVendor.value!.id)
                      ?.name ??
                  context.tr.selectVendor
              : context.tr.selectVendor,
          style: const TextStyle(fontSize: 16, color: Colors.black),
        ),
      ),
    );
  }
}
